var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.BottomBarController = undefined;
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2UIManager = require("UIManager");
var $2BottomBarModel = require("BottomBarModel");
var exp_BottomBarController = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t.viewpath = "ui/bottombar/BottomBarView";
    t.setup($2BottomBarModel.default.instance);
    t.changeListener(true);
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor.prototype, "classname", {
    get: function () {
      return "BottomBarController";
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerAllProtocol = function () {};
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.BottomBar_OpenView, this.onOpenView, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.BottomBar_SelectGame, this._model.startGame, this);
  };
  _ctor.prototype.onOpenView = function (e, t) {
    undefined === e && (e = 2);
    undefined === t && (t = 1);
    var o = $2MVC.MVC.openArgs();
    o.setSelect(e);
    o.setTab(t);
    $2UIManager.UIManager.Open(this.viewpath, o);
  };
  _ctor.prototype.setViewVisible = function (e) {
    var t = $2UIManager.UIManager.getView(this.viewpath);
    if (t) {
      t.node.opacity = e ? 255 : 0;
    } else {
      e && this.onOpenView();
    }
  };
  return _ctor;
}($2MVC.MVC.MController);
exports.BottomBarController = exp_BottomBarController;