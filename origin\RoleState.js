var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RoleState = undefined;
var $2Notifier = require("Notifier");
var $2StateMachine = require("StateMachine");
var $2ListenID = require("ListenID");
var $2GameUtil = require("GameUtil");
var l = cc.v2();
cc.v2();
cc.v2();
cc.v2();
cc.v2();
cc.v2();
(function (e) {
  var t = function (e) {
    function t() {
      return null !== e && e.apply(this, arguments) || this;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.IDLE;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
      t.playAction("idle", true);
      t.velocity.set(cc.Vec2.ZERO);
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
      var i = t.skillMgr.getReadySkill();
      i.length > 0 && this._parentState.changeState($2StateMachine.State.Type.SKILL, i[0].id);
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.IdleState = t;
  var o = function (e) {
    function t() {
      return null !== e && e.apply(this, arguments) || this;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.MOVE;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
      cc.Vec2.multiplyScalar(l, t.velocity, o);
      cc.Vec2.add(l, l, t.position);
      t.updateDir(o);
      t.setPosition(l);
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.MoveState = o;
  var i = function (e) {
    function t() {
      return null !== e && e.apply(this, arguments) || this;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.ATTACK;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.AttackState = i;
  var u = function (e) {
    function t() {
      var t = null !== e && e.apply(this, arguments) || this;
      t._deltaTime = 0;
      t._isShowView = false;
      return t;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.DEAD;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
      t.mySkeleton.clearTracks();
      t.setAnimation("die");
      this._deltaTime = .5;
      t.isDead = true;
      this._isShowView = false;
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
      this._isShowView || (this._deltaTime -= o) <= 0 && (t.isCanRelive ? (t.isCanRelive = false, $2Notifier.Notifier.send($2ListenID.ListenID.Fight_OpenReliveView)) : $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End), this._isShowView = true);
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.DeadState = u;
  var p = function (e) {
    function t() {
      return null !== e && e.apply(this, arguments) || this;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.SKILL;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t, o) {
      e.prototype.onEnter.call(this, t);
      t.setAnimation($2GameUtil.GameUtil.getRandomInArray(["attack", "attack2", "attack3"])[0]);
      t.skillMgr.use(o);
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.SkillState = p;
})(exports.RoleState || (exports.RoleState = {}));