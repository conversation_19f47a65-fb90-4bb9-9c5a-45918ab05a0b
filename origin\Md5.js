Object.defineProperty(exports, "__esModule", {
  value: true
});
var def_Md5 = function () {
  function _ctor() {}
  _ctor.safeAdd = function (e, t) {
    var o = (65535 & e) + (65535 & t);
    return (e >> 16) + (t >> 16) + (o >> 16) << 16 | 65535 & o;
  };
  _ctor.bitRotateLeft = function (e, t) {
    return e << t | e >>> 32 - t;
  };
  _ctor.md5cmn = function (e, t, o, i, n, r) {
    return this.safeAdd(this.bitRotateLeft(this.safeAdd(this.safeAdd(t, e), this.safeAdd(i, r)), n), o);
  };
  _ctor.md5ff = function (e, t, o, i, n, r, a) {
    return this.md5cmn(t & o | ~t & i, e, t, n, r, a);
  };
  _ctor.md5gg = function (e, t, o, i, n, r, a) {
    return this.md5cmn(t & i | o & ~i, e, t, n, r, a);
  };
  _ctor.md5hh = function (e, t, o, i, n, r, a) {
    return this.md5cmn(t ^ o ^ i, e, t, n, r, a);
  };
  _ctor.md5ii = function (e, t, o, i, n, r, a) {
    return this.md5cmn(o ^ (t | ~i), e, t, n, r, a);
  };
  _ctor.binlMD5 = function (e, t) {
    e[t >> 5] |= 128 << t % 32;
    e[14 + (t + 64 >>> 9 << 4)] = t;
    var o = 0;
    var i = 0;
    var n = 0;
    var r = 0;
    var a = 0;
    var s = 1732584193;
    var c = -271733879;
    var l = -1732584194;
    var u = 271733878;
    for (o = 0; o < e.length; o += 16) {
      i = s;
      n = c;
      r = l;
      a = u;
      s = this.md5ff(s, c, l, u, e[o], 7, -680876936);
      u = this.md5ff(u, s, c, l, e[o + 1], 12, -389564586);
      l = this.md5ff(l, u, s, c, e[o + 2], 17, 606105819);
      c = this.md5ff(c, l, u, s, e[o + 3], 22, -1044525330);
      s = this.md5ff(s, c, l, u, e[o + 4], 7, -176418897);
      u = this.md5ff(u, s, c, l, e[o + 5], 12, 1200080426);
      l = this.md5ff(l, u, s, c, e[o + 6], 17, -1473231341);
      c = this.md5ff(c, l, u, s, e[o + 7], 22, -45705983);
      s = this.md5ff(s, c, l, u, e[o + 8], 7, 1770035416);
      u = this.md5ff(u, s, c, l, e[o + 9], 12, -1958414417);
      l = this.md5ff(l, u, s, c, e[o + 10], 17, -42063);
      c = this.md5ff(c, l, u, s, e[o + 11], 22, -1990404162);
      s = this.md5ff(s, c, l, u, e[o + 12], 7, 1804603682);
      u = this.md5ff(u, s, c, l, e[o + 13], 12, -40341101);
      l = this.md5ff(l, u, s, c, e[o + 14], 17, -1502002290);
      c = this.md5ff(c, l, u, s, e[o + 15], 22, 1236535329);
      s = this.md5gg(s, c, l, u, e[o + 1], 5, -165796510);
      u = this.md5gg(u, s, c, l, e[o + 6], 9, -1069501632);
      l = this.md5gg(l, u, s, c, e[o + 11], 14, 643717713);
      c = this.md5gg(c, l, u, s, e[o], 20, -373897302);
      s = this.md5gg(s, c, l, u, e[o + 5], 5, -701558691);
      u = this.md5gg(u, s, c, l, e[o + 10], 9, 38016083);
      l = this.md5gg(l, u, s, c, e[o + 15], 14, -660478335);
      c = this.md5gg(c, l, u, s, e[o + 4], 20, -405537848);
      s = this.md5gg(s, c, l, u, e[o + 9], 5, 568446438);
      u = this.md5gg(u, s, c, l, e[o + 14], 9, -1019803690);
      l = this.md5gg(l, u, s, c, e[o + 3], 14, -187363961);
      c = this.md5gg(c, l, u, s, e[o + 8], 20, 1163531501);
      s = this.md5gg(s, c, l, u, e[o + 13], 5, -1444681467);
      u = this.md5gg(u, s, c, l, e[o + 2], 9, -51403784);
      l = this.md5gg(l, u, s, c, e[o + 7], 14, 1735328473);
      c = this.md5gg(c, l, u, s, e[o + 12], 20, -1926607734);
      s = this.md5hh(s, c, l, u, e[o + 5], 4, -378558);
      u = this.md5hh(u, s, c, l, e[o + 8], 11, -2022574463);
      l = this.md5hh(l, u, s, c, e[o + 11], 16, 1839030562);
      c = this.md5hh(c, l, u, s, e[o + 14], 23, -35309556);
      s = this.md5hh(s, c, l, u, e[o + 1], 4, -1530992060);
      u = this.md5hh(u, s, c, l, e[o + 4], 11, 1272893353);
      l = this.md5hh(l, u, s, c, e[o + 7], 16, -155497632);
      c = this.md5hh(c, l, u, s, e[o + 10], 23, -1094730640);
      s = this.md5hh(s, c, l, u, e[o + 13], 4, 681279174);
      u = this.md5hh(u, s, c, l, e[o], 11, -358537222);
      l = this.md5hh(l, u, s, c, e[o + 3], 16, -722521979);
      c = this.md5hh(c, l, u, s, e[o + 6], 23, 76029189);
      s = this.md5hh(s, c, l, u, e[o + 9], 4, -640364487);
      u = this.md5hh(u, s, c, l, e[o + 12], 11, -421815835);
      l = this.md5hh(l, u, s, c, e[o + 15], 16, 530742520);
      c = this.md5hh(c, l, u, s, e[o + 2], 23, -995338651);
      s = this.md5ii(s, c, l, u, e[o], 6, -198630844);
      u = this.md5ii(u, s, c, l, e[o + 7], 10, 1126891415);
      l = this.md5ii(l, u, s, c, e[o + 14], 15, -1416354905);
      c = this.md5ii(c, l, u, s, e[o + 5], 21, -57434055);
      s = this.md5ii(s, c, l, u, e[o + 12], 6, 1700485571);
      u = this.md5ii(u, s, c, l, e[o + 3], 10, -1894986606);
      l = this.md5ii(l, u, s, c, e[o + 10], 15, -1051523);
      c = this.md5ii(c, l, u, s, e[o + 1], 21, -2054922799);
      s = this.md5ii(s, c, l, u, e[o + 8], 6, 1873313359);
      u = this.md5ii(u, s, c, l, e[o + 15], 10, -30611744);
      l = this.md5ii(l, u, s, c, e[o + 6], 15, -1560198380);
      c = this.md5ii(c, l, u, s, e[o + 13], 21, 1309151649);
      s = this.md5ii(s, c, l, u, e[o + 4], 6, -145523070);
      u = this.md5ii(u, s, c, l, e[o + 11], 10, -1120210379);
      l = this.md5ii(l, u, s, c, e[o + 2], 15, 718787259);
      c = this.md5ii(c, l, u, s, e[o + 9], 21, -343485551);
      s = this.safeAdd(s, i);
      c = this.safeAdd(c, n);
      l = this.safeAdd(l, r);
      u = this.safeAdd(u, a);
    }
    return [s, c, l, u];
  };
  _ctor.binl2rstr = function (e) {
    var t;
    var o = "";
    var i = 32 * e.length;
    for (t = 0; t < i; t += 8) {
      o += String.fromCharCode(e[t >> 5] >>> t % 32 & 255);
    }
    return o;
  };
  _ctor.rstr2binl = function (e) {
    var t;
    var o = [];
    o[(e.length >> 2) - 1] = undefined;
    for (t = 0; t < o.length; t += 1) {
      o[t] = 0;
    }
    var i = 8 * e.length;
    for (t = 0; t < i; t += 8) {
      o[t >> 5] |= (255 & e.charCodeAt(t / 8)) << t % 32;
    }
    return o;
  };
  _ctor.rstrMD5 = function (e) {
    return this.binl2rstr(this.binlMD5(this.rstr2binl(e), 8 * e.length));
  };
  _ctor.rstrHMACMD5 = function (e, t) {
    var o;
    var i;
    var n = this.rstr2binl(e);
    var r = [];
    var a = [];
    r[15] = a[15] = undefined;
    n.length > 16 && (n = this.binlMD5(n, 8 * e.length));
    for (o = 0; o < 16; o += 1) {
      r[o] = 909522486 ^ n[o];
      a[o] = 1549556828 ^ n[o];
    }
    i = this.binlMD5(r.concat(this.rstr2binl(t)), 512 + 8 * t.length);
    return this.binl2rstr(this.binlMD5(a.concat(i), 640));
  };
  _ctor.rstr2hex = function (e) {
    var t;
    var o;
    var i = "";
    for (o = 0; o < e.length; o += 1) {
      t = e.charCodeAt(o);
      i += "0123456789abcdef".charAt(t >>> 4 & 15) + "0123456789abcdef".charAt(15 & t);
    }
    return i;
  };
  _ctor.str2rstrUTF8 = function (e) {
    return unescape(encodeURIComponent(e));
  };
  _ctor.rawMD5 = function (e) {
    return this.rstrMD5(this.str2rstrUTF8(e));
  };
  _ctor.hexMD5 = function (e) {
    return this.rstr2hex(this.rawMD5(e));
  };
  _ctor.rawHMACMD5 = function (e, t) {
    return this.rstrHMACMD5(this.str2rstrUTF8(e), this.str2rstrUTF8(t));
  };
  _ctor.hexHMACMD5 = function (e, t) {
    return this.rstr2hex(this.rawHMACMD5(e, t));
  };
  _ctor.md5 = function (e, t, o) {
    undefined === t && (t = null);
    undefined === o && (o = null);
    if (t) {
      if (o) {
        return this.rawHMACMD5(t, e);
      } else {
        return this.hexHMACMD5(t, e);
      }
    } else {
      if (o) {
        return this.rawMD5(e);
      } else {
        return this.hexMD5(e);
      }
    }
  };
  return _ctor;
}();
exports.default = def_Md5;