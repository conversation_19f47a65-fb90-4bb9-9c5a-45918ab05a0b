/**
 * SelectAlert
 * 组件类 - 从编译后的JS反编译生成
 */

const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2GameSeting = require('GameSeting');
const $2Manager = require('Manager');
const $2EaseScaleTransition = require('EaseScaleTransition');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        reasonDesc: {
            type: cc.RichText,
            default: null
        },
        wayDesc: {
            type: cc.Label,
            default: null
        },
        confirmText: {
            type: cc.Label,
            default: null
        },
        cancelText: {
            type: cc.Label,
            default: null
        },
        videoIcon: {
            type: cc.Node,
            default: null
        },
        icon: {
            type: cc.Sprite,
            default: null
        }
    },

    ctor: function () {
        this.reasonDesc = null
        this.wayDesc = null
        this.confirmText = null
        this.cancelText = null
        this.videoIcon = null
        this.icon = null
        this.cbConfirm = null
        this.cbCancel = null
        this.cbClickConfirm = null
    },

    // use this for initialization
    onLoad: function () {
    },

    onOpen: function () {
        var t = this;
        this._super();
        var o = this.args = this._openArgs.param;
        this.cbConfirm = o && o.confirm;
        this.cbCancel = o && o.cancel;
        this.reasonDesc.text = cc.js.formatStr("<outline color=#474747 width=3>%s</outline>", o && o.desc || "");
        this.reasonDesc.node.setActive(!!o.desc);
        this.wayDesc.string = o && o.title || "提示";
        this.confirmText.string = o && o.confirmText || "确 认";
        this.cancelText.string = o && o.cancelText || "取 消";
        this.cbClickConfirm = o && o.clickconfirm;
        this.UItype = o.tag ? o.tag : $2GameSeting.GameSeting.TweenType.Not;
        this.videoIcon.setActive(o.isVideo);
        this.icon.node.setActive(!!o.icon);
        if (o.icon) {
        $2Manager.Manager.loader.loadSpriteToSprit(o.icon, this.icon);
        this.icon.node.scale = o.iconScale || 2;
        }
        this.node.getComByPath(cc.Layout, "bg/content/box").setAttribute({
        paddingTop: o.icon ? 20 : 100,
        paddingBottom: o.icon ? 20 : 100
        });
        cc.find("bg/ToggleBox", this.node).setActive(o.hasIgnore);
        wonderSdk.hasPay && o.showShop && !$2Manager.Manager.Shop.checkOrder(100) && $2Manager.Manager.loader.loadPrefab("ui/shop/Shop_QuickBuy", this.node).then(function (e) {
        e.setAttribute({
        parent: cc.find("bg/content", t.node)
        });
        });
    },

    onToggle: function (e) {
        if (e.isChecked) {
        $2Manager.Manager.vo.userVo.dailyData.ignoreSelectAlert[this.args.viewType] = 1;
        } else {
        delete $2Manager.Manager.vo.userVo.dailyData.ignoreSelectAlert[this.args.viewType];
        }
        $2Manager.Manager.vo.saveUserData();
    },

    onConfirm: function () {
        this.cbClickConfirm && this.cbClickConfirm();
        this.cbConfirm && this.cbConfirm();
        this.close();
    },

    onCancel: function () {
        this.cbCancel && this.cbCancel();
        this.close();
    },

    toCancel: function () {
        this.close();
    },

    onClickFrame: function () {
        this.close();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
