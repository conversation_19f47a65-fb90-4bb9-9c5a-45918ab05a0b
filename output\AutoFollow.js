/**
 * AutoFollow
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2Notifier = require('Notifier');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        frame_time: {
            displayName: "速度",
            default: 30
        },
        target: {
            get() {
                var e;
                this._target || (this.target = null === (e = $2Notifier.Notifier.call($2CallID.CallID.Fight_GetMainRole)) || undefined === e ? undefined : e.node);
                return this._target;
            },
            set(value) {
                this._target = e;
            },
            visible: false
        }
    },

    ctor: function () {
        this.frame_time = 30
        this.offset = cc.Vec2.ZERO
    },

    // use this for initialization
    onLoad: function () {
    },

    update: function () {
        if (cc.isValid(this.target)) {
        this.node.position = this.target.position.add(this.offset);
        } else {
        this.destroy();
        }
    }
});
