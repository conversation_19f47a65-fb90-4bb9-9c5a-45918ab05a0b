Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Manager = undefined;
var $2AudioManager = require("AudioManager");
var $2LoaderAdapter = require("LoaderAdapter");
var $2VoManager = require("VoManager");
var $2StorageManager = require("StorageManager");
var $2UIManager = require("UIManager");
var $2NetAdapter = require("NetAdapter");
var $2GuidesModel = require("GuidesModel");
var $2ShopModel = require("ShopModel");
var $2Notifier = require("Notifier");
var $2CallID = require("CallID");
var $2ADModel = require("ADModel");
var $2Random = require("Random");
var g = function () {
  function e() {
    this.oldGroupMatrix = JSON.stringify(cc.game.collisionMatrix);
    this.random = new $2Random.default();
  }
  Object.defineProperty(e.prototype, "loader", {
    get: function () {
      return $2LoaderAdapter.LoaderAdapter;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "guide", {
    get: function () {
      return $2GuidesModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "storage", {
    get: function () {
      null == this._storage && (this._storage = new $2StorageManager.StorageManager());
      return this._storage;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "audio", {
    get: function () {
      null == this._audio && (this._audio = new $2AudioManager.AudioManager());
      return this._audio;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "net", {
    get: function () {
      return $2NetAdapter.NetAdapter;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "vo", {
    get: function () {
      return $2VoManager.VoManager.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "ui", {
    get: function () {
      return $2UIManager.UIManager.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "Shop", {
    get: function () {
      return $2ShopModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "AD", {
    get: function () {
      return $2ADModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "leveMgr", {
    get: function () {
      return $2Notifier.Notifier.call($2CallID.CallID.Game_GetLeverVo);
    },
    enumerable: false,
    configurable: true
  });
  e.prototype.setPhysics = function () {};
  e.prototype.getGroupIndex = function (e) {
    return cc.game.groupList.indexOf(e);
  };
  e.prototype.setGroupMatrix = function (e, t, o) {
    try {
      cc.game.collisionMatrix[e][t] = o;
    } catch (i) {
      cc.error("[setGroupMatrix]失败", i);
    }
  };
  e.prototype.setGroupMatrixByStr = function (e, t, o) {
    try {
      cc.game.collisionMatrix[this.getGroupIndex(e)][this.getGroupIndex(t)] = o;
    } catch (i) {
      cc.error("[setGroupMatrix]失败", i);
    }
  };
  e.prototype.restoreGroupMatrix = function () {
    cc.game.collisionMatrix = JSON.parse(this.oldGroupMatrix);
  };
  return e;
}();
exports.Manager = new g();