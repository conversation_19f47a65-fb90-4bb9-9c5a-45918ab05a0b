var i;
var cc__extends = __extends;
var cc__assign = __assign;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MChains = undefined;
var $2MovingBGSprite = require("MovingBGSprite");
var $2CallID = require("CallID");
var $2Cfg = require("Cfg");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2GameUtil = require("GameUtil");
var $2KnapsackVo = require("KnapsackVo");
var $2RecordVo = require("RecordVo");
var $2Game = require("Game");
var $2BronMonsterManger = require("BronMonsterManger");
var $2CompManager = require("CompManager");
var $2NodePool = require("NodePool");
var $2ModeChainsModel = require("ModeChainsModel");
var $2M33_TestBox = require("M33_TestBox");
var $2MCBoss = require("MCBoss");
var $2MCDragoMutilation = require("MCDragoMutilation");
var $2MCDragon = require("MCDragon");
var $2MCPet = require("MCPet");
var $2MCRole = require("MCRole");
(function (e) {
  var t;
  var o;
  var i;
  var T;
  (function (e) {
    e[e.NONE = 0] = "NONE";
    e[e.BATTLE = 1] = "BATTLE";
    e[e.TRANSITIONAM = 2] = "TRANSITIONAM";
    e[e.BOSSCOMEON = 3] = "BOSSCOMEON";
    e[e.END = 4] = "END";
  })(o = e.RoundStatus || (e.RoundStatus = {}));
  (function (e) {
    e[e.Forward = 0] = "Forward";
    e[e.D360 = 1] = "D360";
    e[e.Move = 2] = "Move";
    e[e.ForwardMoveExtend = 3] = "ForwardMoveExtend";
  })(i = e.PassType || (e.PassType = {}));
  (function (e) {
    e[e.NormalBuff = 1] = "NormalBuff";
    e[e.HighBuff = 2] = "HighBuff";
    e[e.Pumpkin = 3] = "Pumpkin";
    e[e.DragonBall = 9e3] = "DragonBall";
    e[e.InitialWeapon = 9001] = "InitialWeapon";
  })(T = e.poolType || (e.poolType = {}));
  var A = function (e) {
    function t() {
      var t;
      var o = null !== e && e.apply(this, arguments) || this;
      o.poolADMap = ((t = {})[T.NormalBuff] = {
        resetNum: 3,
        getAll: 1,
        freeResetNum: 3,
        title: "选择强化属性"
      }, t[T.HighBuff] = {
        resetNum: 1,
        getAll: 0,
        freeResetNum: 1,
        title: "请选择武器"
      }, t[T.Pumpkin] = {
        resetNum: 1,
        getAll: 0,
        freeResetNum: 1,
        title: "选择强化属性"
      }, t[T.DragonBall] = {
        resetNum: 1,
        getAll: 0,
        freeResetNum: 1,
        title: "请选择武器"
      }, t[T.InitialWeapon] = {
        resetNum: 2,
        getAll: 0,
        freeResetNum: 3,
        title: "请选择武器"
      }, t[$2CurrencyConfigCfg.CurrencyConfigDefine.boxBuffDrop] = {
        resetNum: 2,
        getAll: 1,
        freeResetNum: 1,
        title: "选择强化属性"
      }, t[1e4] = {
        resetNum: 1,
        getAll: 1,
        freeResetNum: 2,
        title: "选择全局增益"
      }, t[10001] = {
        resetNum: 1,
        getAll: 1,
        freeResetNum: 2,
        title: "选择全局增益"
      }, t[2e4] = {
        resetNum: 0,
        getAll: 0,
        freeResetNum: 0,
        title: "哇!金色传说!"
      }, t[20001] = {
        resetNum: 0,
        getAll: 0,
        freeResetNum: 0,
        title: "哇!金色传说!"
      }, t);
      o.freeTime = 1;
      o.killNum = 0;
      o.countdownTime = 0;
      o.ADNum = 0;
      return o;
    }
    cc__extends(t, e);
    return t;
  }($2RecordVo.RecordVo.Data);
  e.RecordData = A;
  e.DiffVal = ((t = {})[$2GameSeting.GameSeting.DiffType.Ease] = {
    hp: 1,
    speed: 1,
    startBuff: null
  }, t[$2GameSeting.GameSeting.DiffType.Hard] = {
    hp: 1.5,
    speed: 1,
    startBuff: 1e4
  }, t[$2GameSeting.GameSeting.DiffType.Hell] = {
    hp: 2,
    speed: 1.1,
    startBuff: 10001
  }, t);
  var R = function (t) {
    function o(o) {
      var n = t.call(this, o) || this;
      n.cameraZoomRatio = 1;
      n.passType = i.Forward;
      n.recordVo = new $2RecordVo.RecordVo.Mgr("MChains", function () {
        return new A();
      });
      n.bossSkilling = new Set();
      n.bossHp = 9999;
      n.totalLen = 0;
      n.killMonsterNum = 0;
      n.passParam = o;
      e.DiffVal[$2GameSeting.GameSeting.DiffType.Hard].hp = $2Manager.Manager.vo.switchVo.diffSelectCfg[0][0];
      e.DiffVal[$2GameSeting.GameSeting.DiffType.Hard].speed = $2Manager.Manager.vo.switchVo.diffSelectCfg[0][1];
      e.DiffVal[$2GameSeting.GameSeting.DiffType.Hell].hp = $2Manager.Manager.vo.switchVo.diffSelectCfg[1][0];
      e.DiffVal[$2GameSeting.GameSeting.DiffType.Hell].speed = $2Manager.Manager.vo.switchVo.diffSelectCfg[1][1];
      return n;
    }
    cc__extends(o, t);
    Object.defineProperty(o.prototype, "mode", {
      get: function () {
        return $2ModeChainsModel.default.instance;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "otherVal", {
      get: function () {
        return this.pathData.otherValue;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "isChallenge", {
      get: function () {
        return [10, 11].includes(this.miniGameCfg.type);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "curActivity", {
      get: function () {
        return $2Cfg.Cfg.activity.find({
          lvId: this.miniGameCfg.lvid
        });
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "rVo", {
      get: function () {
        return this.recordVo.vo;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "cutDiffVal", {
      get: function () {
        return e.DiffVal[this.miniGameCfg.type] || e.DiffVal[$2GameSeting.GameSeting.DiffType.Ease];
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.loadMap = function (e, t) {
      var o = this;
      this.gameNode = e;
      this.miniGameCfg = $2Cfg.Cfg.BagModeLv.get(this.passParam.id);
      this.pathData = $2Cfg.Cfg.dragonPath.get(this.miniGameCfg.pathId);
      this._entityNode = e.getChildByName("entityNode");
      this._mapNode = e.getChildByName("mapNode");
      if (this.otherVal[0][3]) {
        var n = this.gameNode.getORaddChildByName("wallNode").setAttribute({
          parent: this._mapNode
        });
        $2Manager.Manager.loader.loadPrefab("entity/fight/ModeChains/map" + this.otherVal[0][3]).then(function (e) {
          e.setAttribute({
            parent: n
          });
        });
      }
      var r = function (e) {
        var t = $2Manager.Manager.vo.switchVo.dragonBoxFree.find(function (t) {
          return t[0] == +e;
        });
        if (t) {
          var o = a.rVo.poolADMap[e];
          var i = a.recordVo.defaultData.poolADMap[e];
          o.freeResetNum = i.freeResetNum = t[1];
          o.resetNum = i.resetNum = t[2];
          o.getAll = i.getAll = t[3];
        }
      };
      var a = this;
      for (var s in this.rVo.poolADMap) {
        r(s);
      }
      this._botEffectNode = e.getChildByName("botEffectNode");
      this.lineNode = e.getChildByName("line").setAttribute({
        zIndex: 99,
        opacity: 0
      });
      this._finishCall = t;
      this.passType = this.otherVal[0][0];
      this.cameraZoomRatio = this.otherVal[0][1] || 1;
      this.scenceSize = [-375 / this.cameraZoomRatio + 50, 375 / this.cameraZoomRatio - 50, -$2GameUtil.GameUtil.getDesignSize.height / 2 / this.cameraZoomRatio + 50, $2GameUtil.GameUtil.getDesignSize.height / 2 / this.cameraZoomRatio - 350];
      if (this.passType == i.D360) {
        this.createRole(cc.v2(0, 0));
        e.getChildByName("fence").setActive(false);
        this.lineNode.setActive(true);
      } else if ([i.Forward, i.ForwardMoveExtend].includes(this.passType)) {
        this.offsetY = -($2GameUtil.GameUtil.getDesignSize.height / this.cameraZoomRatio - 1344) / 2;
        var l = -600 + this.offsetY;
        if (this.passType == i.ForwardMoveExtend) {
          $2GameUtil.GameUtil.getDesignSize.width;
          this.gameCamera.cutZoomRatio;
          e.getChildByName("fence").setActive(false);
          this._mapNode.getChildByName("bg").setAttribute({
            anchorY: .11,
            y: l
          });
        }
        this.createRole(cc.v2(0, l)).then(function (e) {
          e.limitSize = [o.scenceSize[0], o.scenceSize[1], l, l];
        });
        e.getChildByName("fence").y += this.offsetY;
      } else if (this.passType == i.Move) {
        this.createRole(cc.v2(0, -600 - ($2GameUtil.GameUtil.getDesignSize.height / this.cameraZoomRatio - 1344) / 2)).then(function (e) {
          e.limitSize = o.scenceSize;
        });
        e.getChildByName("fence").setActive(false);
      }
      this.gameCamera.setZoomRatio(this.cameraZoomRatio);
      this.gameCamera.lookPos.set(cc.Vec2.ZERO);
      // var u = JSON.parse(this.pathData.path);
      var u = this.pathData.path;
      if ([i.Move, i.ForwardMoveExtend].includes(this.passType)) {
        this.createDragon(u, 1);
      } else {
        u.forEach(function (e, t) {
          o.createDragon([e], t + 1);
        });
      }
      this._bulletNode = e.getORaddChildByName("bulletNode");
      this.LifeBarUI = e.getORaddChildByName("LifeBarUI");
      this.topUINode = e.getORaddChildByName("topUINode");
      this._topEffectNode = e.getORaddChildByName("topEffectNode");
      this.behitUI = e.getORaddChildByName("behitUI");
      this.knapsackMgr = new $2KnapsackVo.KnapsackVo.Mgr(false, "MChains");
      this._finishCall && this._finishCall();
      $2Manager.Manager.setGroupMatrixByStr("Monsetr", "Role", true);
      this.bronMonsterMgr = this.gameNode.getORaddComponent(B);
      this.bronMonsterMgr.init();
      this.bossDiff = $2Manager.Manager.vo.switchVo.dragonBossDiff.filter(function (e) {
        return e[0] == o.miniGameCfg.id && e[1] == (o.passParam.isHighDiff ? 2 : 1);
      });
      this.sendEvent("Start");
    };
    o.prototype.changeListener = function (e) {
      t.prototype.changeListener.call(this, e);
      $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_PetDead, this.onPetDead, this);
    };
    o.prototype.checkBossSkill = function () {
      var e;
      var t = this;
      if (this.bossSkilling.size > 0) {
        this.bossSkilling.forEach(function (e) {
          return !e.isActive && t.bossSkilling.delete(e);
        });
      } else {
        var o = [];
        var i = (null === (e = this.bossDiff.find(function (e) {
          return t.bossHp >= e[2] && t.bossHp < e[3];
        })) || undefined === e ? undefined : e[4]) || 1;
        this.monsterMap.forEach(function (e) {
          e instanceof $2MCBoss.MCBoss && o.push.apply(o, e.skillMgr.skills.filter(function (e) {
            return e.isReady;
          }));
        });
        $2GameUtil.GameUtil.getRandomInArray(o, i).forEach(function (e) {
          e.mgr.use(e.skillCfg.id);
        });
      }
    };
    o.prototype.setTestMode = function (e) {
      var t = this;
      if (this._mapTestMode) {
        this._mapTestMode.open(e);
      } else {
        $2Manager.Manager.loader.loadPrefab("ui/ModeChains/M33_TestBox").then(function (e) {
          e.setParent(t.gameNode);
          t._mapTestMode = e.getORaddComponent($2M33_TestBox.default);
        });
      }
    };
    o.prototype.createRole = function (t) {
      var o = this;
      return new Promise(function (i) {
        $2NodePool.NodePool.spawn("entity/fight/ModeChains/role").setNodeAssetFinishCall(function (n) {
          var r = n.getComponent($2MCRole.default);
          n.parent = o._topEffectNode;
          r.setPosition(t);
          r.init();
          r.setRole();
          $2CompManager.default.Instance.registerComp(r);
          o.mainRole = r;
          o.otherVal[0][4] && $2UIManager.UIManager.OpenInQueue("ui/ModeChains/M33_FightBuffView", $2MVC.MVC.openArgs().setParam({
            type: e.poolType.InitialWeapon,
            getPool: function (t) {
              return o.mode.fightBuffWidth(e.poolType.InitialWeapon, t);
            }
          }).setDailyTime(.3));
          o.otherVal[1] && o.otherVal[1][0] && o.createPet(o.otherVal[1][0], $2MCPet.default, {
            nodeAttr: {
              position: cc.v2(o.otherVal[1][2], o.otherVal[1][3])
            }
          });
          o.cutDiffVal.startBuff && $2UIManager.UIManager.OpenInQueue("ui/ModeChains/M33_FightBuffView", $2MVC.MVC.openArgs().setParam({
            type: o.cutDiffVal.startBuff,
            isBinding: true,
            getPool: function (e) {
              return o.mode.fightBuffWidth(o.cutDiffVal.startBuff, e, $2Cfg.Cfg.PoolList.get(o.cutDiffVal.startBuff).Pool);
            }
          }).setDailyTime(.3));
          o.otherVal[2] && o.otherVal[2][0] && $2UIManager.UIManager.OpenInQueue("ui/ModeChains/M33_RoleSelect");
          i(r);
        });
      });
    };
    o.prototype.createDragon = function (e, t) {
      var o = this;
      return new Promise(function (n) {
        var r = o.gameNode.getORaddChildByName("Chains_" + t).getORaddChildByName("Chains");
        var a = $2Game.ModeCfg.MonsterLv.find({
          lv: o.pathData.dragonLvId,
          round: t
        });
        var s = 11 == $2Cfg.Cfg.Monster.get($2GameUtil.GameUtil.randomArr(a.monId)).type ? $2MCDragoMutilation.MCDragoMutilation : $2MCDragon.MCDragon;
        var l = r.addComponent(s);
        if (o.passType == i.ForwardMoveExtend) {
          l.setAttribute({
            sectionInterval: 4,
            jointNum: 1,
            retreatBuffer: 1,
            isBodyRotate: true
          });
        } else {
          l.setAttribute({
            sectionInterval: 1,
            jointNum: 8,
            retreatBuffer: 2
          });
        }
        l.init();
        l.setInfo(t, e);
        $2CompManager.default.Instance.registerComp(l);
        o.elementMap.set(l.ID, l);
        o.chainsList.push(l);
        n(l);
      });
    };
    o.prototype.reliveSuccess = function () {
      this.mainRole.relive();
      this.chainsList.forEach(function (e) {
        e.curIndex = Math.max(10, e.curIndex - 100);
      });
    };
    o.prototype.onPetDead = function () {
      this.mainRole.toDead();
    };
    o.prototype.showEntityDieEffect = function (e, t) {
      var o = this;
      undefined === e && (e = .5);
      return new Promise(function () {
        o.showEffectByPath("bones/skill/fx_draw", {
          nodeAttr: t,
          spAttr: {
            defaultAnimation: "animation",
            premultipliedAlpha: false,
            loop: false
          },
          delayRemove: .3
        });
      });
    };
    Object.defineProperty(o.prototype, "mainRole", {
      get: function () {
        return this._mainRole;
      },
      set: function (e) {
        this._mainRole = e;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.getCutGameData = function () {
      var e = $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode);
      return {
        Mode: e,
        AdNums: $2Manager.Manager.AD.data.getor(e, 0),
        FightID: $2Notifier.Notifier.call($2CallID.CallID.Fight_GetFightID) || 0,
        Lv: this.miniGameCfg.lvid,
        Batch: (this.killMonsterNum / this.totalLen).toFixed(2)
      };
    };
    return o;
  }($2Game.Game.Mgr);
  e.Mgr = R;
  var B = function (t) {
    function s() {
      var e = null !== t && t.apply(this, arguments) || this;
      e._batchNum = 0;
      e.MonsterType = {
        1: "ModeChains/Boss",
        2: "ModeChains/Boss",
        3: "ModeChains/Boss",
        4: "CanWreckItem"
      };
      return e;
    }
    cc__extends(s, t);
    Object.defineProperty(s.prototype, "batchNum", {
      get: function () {
        return this._batchNum;
      },
      set: function (e) {
        this._batchNum = e;
        0 != e && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_GameRound, e);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(s.prototype, "game", {
      get: function () {
        return $2Game.Game.mgr;
      },
      enumerable: false,
      configurable: true
    });
    s.prototype.init = function () {
      var t = this;
      this.changeGameStatus(e.RoundStatus.BATTLE);
      var o = this.game.passType == i.Move ? this.game._mapNode.getComByChild($2MovingBGSprite.default, "MovingBG") : this.game._mapNode.getComByChild(cc.Sprite, "bg");
      o instanceof $2MovingBGSprite.default && o.setMoveSpeed(cc.v2(0, $2ModeChainsModel.default.instance.isMovingBG ? .06 : 0));
      $2Manager.Manager.loader.loadSpriteToSprit(this.game.miniGameCfg.sceneMap, o).then(function (e) {
        var n = Math.min($2GameUtil.GameUtil.getDesignSize.width, 2e3) / t.game.cameraZoomRatio;
        t.game.passType == i.Move && (n *= 1.3);
        var r = n / e.getRect().width * e.getRect().height;
        o.node.setAttribute({
          width: n,
          height: r,
          active: true
        });
      });
    };
    Object.defineProperty(s.prototype, "role", {
      get: function () {
        return this.game.mainRole;
      },
      enumerable: false,
      configurable: true
    });
    s.prototype.changeGameStatus = function (e) {
      var t = this;
      switch (e) {
        case o.BATTLE:
          this.batchNum++;
          $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
          break;
        case o.BOSSCOMEON:
          $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
          var i = JSON.parse(JSON.stringify($2Game.ModeCfg.MonsterLv.filter({
            lv: this.game.pathData.dragonLvId,
            round: 999
          })));
          var n = $2Manager.Manager.vo.switchVo.dragonDiff.filter(function (e) {
            return e[0] == t.game.pathData.dragonLvId;
          });
          n.length > 0 && i.forEach(function (e, t) {
            var o = n.find(function (e) {
              return t >= e[1] && t < e[2];
            });
            o && (e.hp *= 1 + o[3]);
          });
          var a = [cc.v2(-400, this.game.scenceSize[3] - 700), cc.v2(0, this.game.scenceSize[3] - 650), cc.v2(400, this.game.scenceSize[3] - 700)];
          this.game.bossMaxHp = 0;
          i.forEach(function (e, o) {
            var i = $2GameUtil.GameUtil.randomArr(e.monId);
            var n = cc__assign({
              letTime: 0,
              createNum: 0,
              type: $2Cfg.Cfg.Monster.get(i).type,
              monsterID: i
            }, e);
            t.game.bossMaxHp += e.hp;
            var s = a[o];
            t.createMonster(n, s.add(cc.v2(0, 600)), $2MCBoss.MCBoss).then(function (e) {
              var t;
              e.colliderScaleSet = e.isHead ? {
                w: .4,
                h: .4
              } : {
                w: .15,
                h: .15
              };
              if (2 == o) {
                e.roleNode.scaleX *= -1;
                e.roleDir = -1;
              }
              e.forwardDirection.setVal(0, -1);
              e.toIdle();
              null === (t = e.node.getChildByName("role_shadow")) || undefined === t || t.destroy();
              cc.tween(e.node).delay(.5).to(2, {
                position: s
              }, {
                easing: cc.easing.sineIn
              }).start();
            });
          });
          this.game.bossHp = this.game.bossMaxHp;
          this.scheduleOnce(function () {
            t.changeGameStatus(o.BATTLE);
            t.game.uiView.BossLifeBar.node.parent.setActive(true);
          }, 3);
          break;
        case o.END:
          $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
          $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, !(this.game.chainsList.length > 0));
      }
      this.cutStatus = e;
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_GameRoundType, e);
    };
    s.prototype.onUpdate = function (t) {
      var n = this;
      if (this.cutStatus == o.BATTLE && (this._dtTime += t) >= .1 && this.role) {
        this._dtTime = 0;
        if (0 == this.game.chainsList.length) {
          if (this.game.passType != i.Move) {
            return this.game.bronMonsterMgr.changeGameStatus(e.RoundStatus.END);
          }
          if (this.game.bossHp <= 0) {
            return this.game.bronMonsterMgr.changeGameStatus(e.RoundStatus.END);
          }
        } else {
          if (this.game.passType == i.ForwardMoveExtend) {
            return;
          }
          this.game.chainsList.forEach(function (e) {
            if (e.loadIndex > 30 && e.bodyList.length > 0) {
              if (n.game.passType == i.Forward && e.bodyList[0].position.y < n.role.haedPosition.y + 70) {
                return n.role.toDead();
              }
              if (cc.Vec2.squaredDistance(e.bodyList[0].position, n.role.haedPosition) < Math.pow(155, 2)) {
                return n.role.behit(e.getHurt());
              }
            }
          });
        }
      }
    };
    return s;
  }($2BronMonsterManger.BronMonsterManger);
  e.SpawningMgr = B;
})(exports.MChains || (exports.MChains = {}));