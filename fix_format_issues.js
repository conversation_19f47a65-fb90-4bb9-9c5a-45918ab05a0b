const fs = require('fs');
const path = require('path');

// 修复格式问题的脚本
class FormatFixer {
    constructor() {
        this.outputDir = './output';
        this.fixedCount = 0;
        this.errors = [];
    }

    readFile(filePath) {
        try {
            return fs.readFileSync(filePath, 'utf8');
        } catch (error) {
            this.errors.push(`读取文件失败: ${filePath} - ${error.message}`);
            return null;
        }
    }

    writeFile(filePath, content) {
        try {
            fs.writeFileSync(filePath, content, 'utf8');
            return true;
        } catch (error) {
            this.errors.push(`写入文件失败: ${filePath} - ${error.message}`);
            return false;
        }
    }

    // 修复属性定义中的格式问题
    fixPropertyFormat(content) {
        let fixedContent = content;
        let hasChanges = false;

        // 修复 getter 函数中多余的逗号和错误的格式
        const patterns = [
            // 修复 return 语句后的多余逗号
            /return\s+([^;]+);,\s*\n\s*override:\s*true\s*\n\s*\d+,/g,
            // 修复错误的 override 位置
            /},\s*\n\s*override:\s*true\s*\n\s*\d+,\s*\n\s*visible:/g
        ];

        // 修复第一种模式：return 语句后的逗号问题
        fixedContent = fixedContent.replace(
            /return\s+([^;]+);,\s*\n\s*override:\s*true\s*\n\s*\d+,\s*\n\s*visible:\s*false/g,
            (match, returnValue) => {
                hasChanges = true;
                return `return ${returnValue};\n            },\n            visible: false,\n            override: true`;
            }
        );

        // 修复第二种模式：错误的 override 位置
        fixedContent = fixedContent.replace(
            /},\s*\n\s*override:\s*true\s*\n\s*\d+,\s*\n\s*visible:\s*false/g,
            () => {
                hasChanges = true;
                return '},\n            visible: false,\n            override: true';
            }
        );

        return { content: fixedContent, hasChanges };
    }

    // 修复特定的 getter 属性格式
    fixGetterProperties(content) {
        let fixedContent = content;
        let hasChanges = false;

        // 查找并修复 type 属性的 getter 定义
        const typeGetterPattern = /(type:\s*{\s*get\(\)\s*{\s*return\s+[^;]+);,(\s*override:\s*true\s*\d+,\s*visible:\s*false)/g;
        
        fixedContent = fixedContent.replace(typeGetterPattern, (match, beforeComma, afterComma) => {
            hasChanges = true;
            return beforeComma + ';\n            },\n            visible: false,\n            override: true';
        });

        // 更精确的模式匹配
        const precisePattern = /(type:\s*{\s*get\(\)\s*{\s*return\s+[^;]+;),\s*override:\s*true\s*\d+,\s*visible:\s*false\s*}/g;
        
        fixedContent = fixedContent.replace(precisePattern, (match, getterPart) => {
            hasChanges = true;
            return getterPart.replace(/;,/, ';\n            },\n            visible: false,\n            override: true\n        }');
        });

        return { content: fixedContent, hasChanges };
    }

    // 清理多余的数字和格式错误
    cleanupFormatErrors(content) {
        let fixedContent = content;
        let hasChanges = false;

        // 移除属性定义中的多余数字
        fixedContent = fixedContent.replace(/override:\s*true\s*\n\s*\d+,/g, () => {
            hasChanges = true;
            return 'override: true';
        });

        // 修复多余的逗号
        fixedContent = fixedContent.replace(/;,(\s*})/g, (match, closeBrace) => {
            hasChanges = true;
            return ';' + closeBrace;
        });

        return { content: fixedContent, hasChanges };
    }

    // 处理单个文件
    processFile(filePath) {
        const fileName = path.basename(filePath);
        console.log(`处理文件: ${fileName}`);
        
        const content = this.readFile(filePath);
        if (!content) {
            return false;
        }

        let fixedContent = content;
        let totalChanges = false;
        let changesList = [];

        // 1. 修复属性格式问题
        const formatResult = this.fixPropertyFormat(fixedContent);
        if (formatResult.hasChanges) {
            fixedContent = formatResult.content;
            totalChanges = true;
            changesList.push('属性格式问题');
        }

        // 2. 修复 getter 属性格式
        const getterResult = this.fixGetterProperties(fixedContent);
        if (getterResult.hasChanges) {
            fixedContent = getterResult.content;
            totalChanges = true;
            changesList.push('getter 属性格式');
        }

        // 3. 清理格式错误
        const cleanupResult = this.cleanupFormatErrors(fixedContent);
        if (cleanupResult.hasChanges) {
            fixedContent = cleanupResult.content;
            totalChanges = true;
            changesList.push('格式清理');
        }

        // 如果有修改，写入文件
        if (totalChanges) {
            if (this.writeFile(filePath, fixedContent)) {
                this.fixedCount++;
                console.log(`  ✓ 修复了: ${changesList.join(', ')}`);
                console.log(`  ✓ 文件修复完成\n`);
                return true;
            }
        } else {
            console.log(`  - 无需修复\n`);
        }

        return false;
    }

    // 运行修复
    run() {
        console.log('开始修复格式问题...\n');

        // 需要修复格式问题的文件
        const problemFiles = [
            'FBoxCollider.js',
            'FCircleCollider.js', 
            'FPolygonCollider.js'
        ];

        problemFiles.forEach(fileName => {
            const filePath = path.join(this.outputDir, fileName);
            if (fs.existsSync(filePath)) {
                this.processFile(filePath);
            } else {
                console.log(`文件不存在: ${fileName}`);
            }
        });

        // 输出结果
        console.log('\n=== 格式修复完成 ===');
        console.log(`修复的文件数量: ${this.fixedCount}`);
        
        if (this.errors.length > 0) {
            console.log('\n错误信息:');
            this.errors.forEach(error => console.log(`  - ${error}`));
        }

        console.log('\n格式问题已修复，建议重新启动 Cocos Creator。');
    }
}

// 运行修复脚本
const fixer = new FormatFixer();
fixer.run();
