Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SdkAlertAdapter = undefined;
var i = function () {
  function e() {}
  e.prototype.setAdapter = function (e) {
    this._adapter = e;
  };
  e.prototype.showAlert = function (e) {
    if (this._adapter) {
      this._adapter.showSelectAlert(e);
    } else {
      e.confirm && e.confirm();
    }
  };
  e.prototype.showCommonAlert = function (e) {
    this._adapter && this._adapter.showCommonAlert(e);
  };
  e.prototype.showNormalTipsOnce = function (e, t, o, i, n) {
    undefined === t && (t = null);
    undefined === o && (o = .7);
    undefined === i && (i = 50);
    undefined === n && (n = cc.Vec3.ZERO);
    this._adapter.showNormalTipsOnce(e, t, o, i, n);
  };
  return e;
}();
exports.SdkAlertAdapter = new i();