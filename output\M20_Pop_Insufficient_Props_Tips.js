/**
 * M20_Pop_Insufficient_Props_Tips
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2UIManager = require('UIManager');
const $2EaseScaleTransition = require('EaseScaleTransition');
const $2Game = require('Game');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    ctor: function () {
        this.currencyType = $2CurrencyConfigCfg.CurrencyConfigDefine.Coin
    },

    // use this for initialization
    onLoad: function () {
    },

    setInfo: function () {
        var e = this;
        this.currencyType = this.param.currencyType;
        var t = $2Cfg.Cfg.CurrencyConfig.get(this.currencyType);
        this.labelArr[0].string = t.name;
        this.labelArr[1].string = t.desc;
        $2Manager.Manager.loader.loadSpriteAsync(t.icon).then(function (t) {
        e.nodeArr[5].getComponent(cc.Sprite).spriteFrame = t;
        });
        var o = $2GameSeting.GameSeting.getRarity(this.mode.buffmap[t.rarity]).blockImg;
        $2Manager.Manager.loader.loadSpriteAsync(o).then(function (t) {
        e.nodeArr[7].getComponent(cc.Sprite).spriteFrame = t;
        });
        this.nodeArr[4].children.forEach(function (e) {
        e.active = false;
        });
        this.nodeArr[0].active = true;
        this.nodeArr[1].active = this.mode.rVo.curPassLv > 0 && (this.mode.rVo.dailyData.sweep_count > 0 || $2Manager.Manager.vo.switchVo.lvSweep[2] > 0);
        if (this.currencyType == $2CurrencyConfigCfg.CurrencyConfigDefine.Coin) {
        this.nodeArr[6].active = false;
        this.labelArr[2].string = "灵石不足";
        this.nodeArr[0].getComByChild(cc.Label).string = "商城获取灵石";
        this.nodeArr[2].active = this.mode.CurChallengeLv - 1 >= $2Cfg.Cfg.activity.get(3).unlockChapter;
        } else {
        this.nodeArr[6].active = true;
        this.labelArr[2].string = "碎片不足";
        this.nodeArr[0].getComByChild(cc.Label).string = "商城开箱子";
        console.log("unlockChapter: " + $2Cfg.Cfg.activity.get(2).unlockChapter);
        this.nodeArr[3].active = this.mode.CurChallengeLv - 1 >= $2Cfg.Cfg.activity.get(2).unlockChapter;
        }
    },

    onBtn: function (e, t) {
        console.log("onBtn: " + t);
        switch (t) {
        case "shop":
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_BackToMain);
        var o = $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView);
        o.initMenu(o.toggleContainer.toggleItems[0]);
        var i = $2Notifier.Notifier.call($2CallID.CallID.M20_GetShopView);
        var n = null == i ? undefined : i.getComponentInChildren(cc.ScrollView);
        n && n.scrollToPercentVertical(0, .2);
        this.close();
        $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_EquipInfo");
        $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_RoleInfo");
        break;
        case "sweep":
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_BackToMain);
        (o = $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView)).initMenu(o.toggleContainer.toggleItems[2]);
        this.close();
        $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_EquipInfo");
        $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_RoleInfo");
        break;
        case "coin_challenge":
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_BackToMain);
        (o = $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView)).initMenu(o.toggleContainer.toggleItems[4]);
        this.close();
        $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_EquipInfo");
        $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_RoleInfo");
        var r = $2Cfg.Cfg.activity.get(3).prefab;
        r.includes("ui/") && $2UIManager.UIManager.OpenInQueue(r, $2MVC.MVC.openArgs());
        break;
        case "challenge":
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_BackToMain);
        (o = $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView)).initMenu(o.toggleContainer.toggleItems[4]);
        this.close();
        $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_EquipInfo");
        $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_RoleInfo");
        (r = $2Cfg.Cfg.activity.get(2).prefab).includes("ui/") && $2UIManager.UIManager.OpenInQueue(r, $2MVC.MVC.openArgs());
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
