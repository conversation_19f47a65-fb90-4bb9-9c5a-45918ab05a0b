Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.NodePool = exports.NodePoolItem = exports.NodeState = undefined;
var i;
var $2Pool = require("Pool");
var $2Manager = require("Manager");
var $2Time = require("Time");
(function (e) {
  e[e.WaitLoad = 0] = "WaitLoad";
  e[e.StartLoad = 1] = "StartLoad";
  e[e.Loaded = 2] = "Loaded";
  e[e.TimeOut = 3] = "TimeOut";
  e[e.Die = 4] = "Die";
})(i = exports.NodeState || (exports.NodeState = {}));
var exp_NodePoolItem = function () {
  function _ctor(e, t) {
    this._state = i.WaitLoad;
    this._target = null;
    this._nodePath = e;
    this.spawner = t;
  }
  _ctor.prototype.unuse = function () {
    if (this._state == i.Loaded) {
      this._node && this._node.parent && this._node.removeFromParent(false);
    } else {
      this._state == i.StartLoad && this.setAssetState(i.TimeOut);
    }
    this._target = null;
    this._onAssetFinishHandler = null;
  };
  _ctor.prototype.reuse = function () {
    var e = this;
    if (this._node && this._state == i.Loaded) {
      $2Time.Time.delay(.01, function () {
        null != e._onAssetFinishHandler && e._onAssetFinishHandler.call(e._target, e._node);
      });
    } else {
      this._loadAsset();
    }
  };
  _ctor.prototype.destroy = function () {
    this._target = null;
    this._state = i.Die;
    this._onAssetFinishHandler = null;
    if (this._node) {
      this._node.destroy();
      this._node = null;
    }
  };
  _ctor.prototype.setAssetState = function (e) {
    this._state = e;
  };
  _ctor.prototype.setNodeAssetFinishCall = function (e, t) {
    this._onAssetFinishHandler = e;
    this._target = t;
  };
  Object.defineProperty(_ctor.prototype, "node", {
    get: function () {
      return this._node;
    },
    set: function (e) {
      this._node = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype._loadAsset = function () {
    var e = this;
    this.setAssetState(i.StartLoad);
    $2Manager.Manager.loader.loadPrefab(this._nodePath).then(function (t) {
      e._node = t;
      e._node.nodeItem = e;
      if (e._state == i.StartLoad) {
        e.setAssetState(i.Loaded);
        null != e._onAssetFinishHandler && e._onAssetFinishHandler.call(e._target, t);
      }
    }).catch(function (t) {
      cc.error(t);
      e.setAssetState(i.TimeOut);
      null != e._onAssetFinishHandler && e._onAssetFinishHandler.call(e._target, null);
    });
  };
  return _ctor;
}();
exports.NodePoolItem = exp_NodePoolItem;
var c = function () {
  function e() {
    this._poolType = "NodePool";
    this.initPool();
  }
  e.prototype.initPool = function () {
    $2Pool.PoolManager.isExist(this._poolType) || (this._pool = $2Pool.PoolManager.create(this._poolType, this));
  };
  e.prototype.nodeCreateDelegate = function (e, t) {
    return new exp_NodePoolItem(e, t);
  };
  e.prototype.despawn = function (e, t) {
    undefined === t && (t = false);
    if (null != this._pool) {
      this._pool.despawn(e, t);
    } else {
      null != e && e.destroy();
    }
  };
  e.prototype.spawn = function (e) {
    if (null == this._pool) {
      return null;
    } else {
      return this._pool.isExitsSpawner(e) || (this._pool.createSpawner(e, this.nodeCreateDelegate, null, 0, 1), this.path = e), this._pool.spawn(e);
    }
  };
  e.prototype.spawnASync = function (e) {
    var t = this;
    return new Promise(function (i, n) {
      var r = t.spawn(e);
      r.setNodeAssetFinishCall(function (e) {
        if (e) {
          i(r);
        } else {
          exports.NodePool.despawn(r);
          n(null);
        }
      });
    });
  };
  e.prototype.clear = function () {
    null != this._pool && this._pool._clear();
  };
  return e;
}();
exports.NodePool = new c();