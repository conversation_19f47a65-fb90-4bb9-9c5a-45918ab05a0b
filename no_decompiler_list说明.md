# no_decompiler_list.txt 使用说明

## 功能说明

`no_decompiler_list.txt` 文件用于指定哪些脚本文件不需要进行转换，这些文件将直接复制到 `origin` 目录中，而不会被转换为 cc.Class 格式。

## 使用方法

### 1. 文件格式

- 每行一个文件名
- 只需要文件名，不需要路径
- 支持注释行（以 `#` 开头）
- 空行会被忽略

### 2. 示例文件内容

```
# 不需要转换的文件列表
# 以 # 开头的行为注释行，会被忽略
# 每行一个文件名，只需要文件名，不需要路径

# 复杂的技能模块，包含多个类定义
SkillModule.js

# 包含多个类定义的文件
BuffList.js

# 其他不需要转换的文件
SomeComplexFile.js
```

### 3. 适用场景

以下类型的文件建议添加到不转换列表中：

1. **包含多个 cc.Class 定义的文件**
   - 如 `BuffList.js`、`SkillModule.js` 等
   - 转换脚本会自动检测并复制这类文件，但手动指定更可靠

2. **复杂的继承关系文件**
   - 包含复杂继承链的文件
   - 转换可能出现问题的文件

3. **特殊语法的文件**
   - 使用了转换脚本不支持的特殊语法
   - 需要保持原始格式的文件

4. **已经是正确格式的文件**
   - 已经是 cc.Class 格式的文件
   - 不需要再次转换的文件

## 工作流程

1. 转换脚本启动时会自动读取 `no_decompiler_list.txt`
2. 对于列表中的文件，直接复制到 `origin` 目录
3. 对于不在列表中的文件，进行正常的转换处理
4. 控制台会显示加载的不转换文件数量和列表

## 注意事项

1. **文件名必须完全匹配**：确保文件名拼写正确，包括大小写
2. **只需要文件名**：不要包含路径，只写文件名即可
3. **支持动态修改**：可以随时修改此文件，下次运行转换脚本时会生效
4. **备份重要文件**：建议在修改此列表前备份重要文件

## 示例输出

当文件在不转换列表中时，控制台会显示：

```
已加载 1 个不需要转换的文件
不转换文件列表: [ 'SkillModule.js' ]
Converting file: scripts/SkillModule.js
文件 SkillModule.js 在不转换列表中，直接复制到 origin 目录
```

## 文件位置

- **配置文件**：项目根目录下的 `no_decompiler_list.txt`
- **输出目录**：不转换的文件会被复制到 `origin/` 目录
- **转换目录**：正常转换的文件会输出到 `output/` 目录
