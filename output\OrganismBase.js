/**
 * OrganismBase
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2GameatrCfg = require('GameatrCfg');
const $2Notifier = require('Notifier');
const $2AlertManager = require('AlertManager');
const $2Game = require('Game');
const $2Buff = require('Buff');
const $2SkillManager = require('SkillManager');
const $2PropertyVo = require('PropertyVo');
const $2BaseEntity = require('BaseEntity');
const $2MoveEntity = require('MoveEntity');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.nullMap = cc.Class({
    extends: $2MoveEntity.default,

    properties: {
        canSkill: {
            get() {
                return this.game.canSkill;
            },
            visible: false
        },
        canFire: {
            get() {
                return true;
            },
            visible: false
        },
        firstSkill: {
            get() {
                var e;
                if (null === (e = this.skillMgr) || undefined === e) {
                return undefined;
                } else {
                return e.skills[0];
                }
            },
            visible: false
        },
        isBanMove: {
            get() {
                var e;
                return (null === (e = this.buffMgr) || undefined === e ? undefined : e.isBanMove) || false;
            },
            visible: false
        },
        isInvincible: {
            get() {
                var e;
                if (null === (e = this.buffMgr) || undefined === e) {
                return undefined;
                } else {
                return e.isInvincible;
                }
            },
            visible: false
        },
        curStateTag: {
            get() {
                return this._stateMachine.curStateTag;
            },
            visible: false
        },
        fsm: {
            get() {
                return this._stateMachine;
            },
            visible: false
        },
        parent: {
            get() {
                return this._parent;
            },
            set(value) {
                var t;
                var o;
                null === (o = null === (t = this._parent) || undefined === t ? undefined : t.petList) || undefined === o || o.delete(this);
                e && e.petList.push(this);
                this._parent = e;
            },
            visible: false
        },
        curHp: {
            get() {
                return this._curHp;
            },
            set(value) {
                this._curHp = Math.min(this.property.cut.hp, e);
                this.curHpProgress = this._curHp / this.property.cut.hp;
                this.LifeBar && (this.LifeBar.fillRange = this.curHpProgress);
            },
            visible: false
        },
        curArmor: {
            get() {
                return this._curArmor;
            },
            set(value) {
                this._curArmor = e;
            },
            visible: false
        },
        horDir: {
            get() {
                return this._horDir;
            },
            set(value) {
                this._horDir = e;
                this.node && this.roleNode && (this.roleNode.scaleX = this._horDir * Math.abs(this.roleNode.scaleX) * this.roleDir);
            },
            visible: false
        }
    },

    ctor: function () {
        this.LifeBar = null
        this._stateMachine = null
        this.forwardDirection = cc.Vec2.ZERO
        this._level = 1
        this._nextLevelExp = 60
        this._levelExp = 1
        this._curHp = 0
        this.curHpProgress = 1
        this._curArmor = 0
        this._dt = 0
        this._logicTime = .1
    },

    initHp: function () {
        var e;
        this.curHp = this.property.cut.hp;
        null === (e = this.roleNode) || undefined === e || e.setActive(true);
    },

    init: function () {
        var t;
        this._super();
        null === (t = this.node) || undefined === t || t.setActive(true);
    },

    updateProperty: function () {
        var e;
        null === (e = this.property) || undefined === e || e.updateVo();
    },

    removeEffectBox: function () {
        if (this.botEffectBox) {
        for (var e = this.botEffectBox.childrenCount - 1; e >= 0; e--) {
        if (o = (t = this.botEffectBox.children[e]).getComponent($2BaseEntity.default)) {
        o.removeEntityToUpdate();
        } else {
        t.destroy();
        }
        }
        }
        if (this.topEffectBox) {
        for (e = this.topEffectBox.childrenCount - 1; e >= 0; e--) {
        var t;
        var o;
        if (o = (t = this.topEffectBox.children[e]).getComponent($2BaseEntity.default)) {
        o.removeEntityToUpdate();
        } else {
        t.destroy();
        }
        }
        }
    },

    resetCollider: function (e) {
        this.collider.size = cc.size(e, e);
    },

    behit: function (e) {
        if (this.isDead) {
        return null;
        } else {
        if (this.hurtMgr.checkHurt(e)) {
        return this.curHp -= e.val, this.materialTwinkle(), e;
        } else {
        return null;
        }
        }
    },

    treat: function (e) {
        var t = e.baseVal;
        t *= 1 + this.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.hpPer, 0);
        this.curHp += t;
        $2AlertManager.AlertManager.showHurtTips(Math.round(t), {
        position: this.bodyPosition,
        color: cc.Color.GREEN
        });
    },

    addArmor: function (e, t) {
        undefined === t && (t = true);
        this.curArmor += e;
    },

    changeListener: function (t) {
        this._super(t);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_OnGameState, this.onGameState, this);
        this.node.changeListener(t, $2ListenID.ListenID.Fight_OnBuff, this.OnBuff, this);
        this.node.changeListener(t, $2ListenID.ListenID.Fight_OnSkill, this.onSkill, this);
    },

    onGameState: function (e) {
        var t;
        var o;
        var i;
        var n;
        if (e == $2Game.Game.State.PAUSE) {
        null === (t = this.myAm) || undefined === t || t.pause();
        null === (o = this.mySkeleton) || undefined === o || o.setPause(true);
        } else {
        null === (i = this.myAm) || undefined === i || i.resume();
        null === (n = this.mySkeleton) || undefined === n || n.setPause(false);
        }
    },

    materialTwinkle: function () {
        this._mals;
    },

    setAnimation: function (e, t) {
        undefined === t && (t = false);
        if (!t || this.mySkeleton.animation != e) {
        return this.mySkeleton.setAnimation(0, e, t);
        }
    },

    onLoad: function () {
        var t;
        var o;
        var i;
        this._super();
        this.roleNode = this.node.getChildByName("role");
        this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
        this._mals = (null === (t = this.mySkeleton) || undefined === t ? undefined : t.getMaterial(0)) || (null === (i = null === (o = this.roleNode) || undefined === o ? undefined : o.getComponent(cc.Sprite)) || undefined === i ? undefined : i.getMaterial(0));
    },

    onEnable: function () {
        this._super();
        this.mySkeleton && this.game && (this.mySkeleton.paused = this.game.gameState == $2Game.Game.State.PAUSE);
    },

    onNewSize: function () {
        // TODO: 实现方法逻辑
    },

    onUpdate: function (t) {
        var o;
        var i;
        var n;
        var r;
        if (this.isActive && (this._dt += t) > this._logicTime && this.canSkill) {
        null === (o = this.buffMgr) || undefined === o || o.onUpdate(this._dt);
        null === (i = this.skillMgr) || undefined === i || i.onUpdate(this._dt);
        null === (n = this.hurtMgr) || undefined === n || n.onUpdate(this._dt);
        this._dt = 0;
        }
        this.node.emit($2ListenID.ListenID.Fight_EntityUpdate, t);
        null === (r = this._stateMachine) || undefined === r || r.update(t);
        this._super(t);
    },

    OnBuff: function (e) {
        var t;
        var o = this.property.cut.hp;
        var i = this.curHpProgress;
        this.updateProperty();
        null === (t = this.skillMgr) || undefined === t || t.onBuff(e);
        if (null == e ? undefined : e.attr.includes($2GameatrCfg.GameatrDefine.hp)) {
        var n = this.property.cut.hp - o;
        if (n > 0) {
        this.curHp += n * i;
        } else {
        this.curHp = Math.min(this.curHp, this.property.cut.hp);
        }
        }
    },

    onSkill: function () {
        // TODO: 实现方法逻辑
    },

    onKill: function () {
        // TODO: 实现方法逻辑
    },

    setAmClip: function () {
        // TODO: 实现方法逻辑
    },

    getAmClip: function (e, t, o, i) {
        var n = this;
        undefined === i && (i = 10);
        return new Promise(function (r) {
        n.myAm || (n.myAm = n.roleNode.getORaddComponent(cc.Animation));
        o = JSON.parse(o);
        n.myAmName = e;
        var a = [];
        var s = o[1].split("-");
        for (var c = +s[0]; c <= +s[1]; c++) {
        a.push("" + o[0] + c);
        }
        $2Game.Game.Mgr.instance.getAmClip({
        assetName: e + "_" + t,
        amName: t,
        path: a,
        frame_time: i
        }).then(function (e) {
        e.wrapMode = cc.WrapMode.Loop;
        n.myAm.addClip(e, t);
        r(e);
        });
        });
    },

    cleanAmClip: function () {
        if (this.myAm) {
        var e = this.myAm.getClips();
        for (var t = e.length - 1; t >= 0; t--) {
        this.myAm.removeClip(e[t], true);
        }
        }
    },

    playAction: function (e, t) {
        if (this.myAm) {
        this.myAm.play(e);
        } else {
        this.mySkeleton && this.setAnimation(e, t);
        }
    },

    getHurt: function (e) {
        var t;
        undefined === e && (e = 1);
        this._tempHurtData || (this._tempHurtData = $2PropertyVo.Hurt.Pool.pop());
        var o = this.property.cut.atk * e;
        (null === (t = this.buffMgr) || undefined === t ? undefined : t.isDisarm) && (o = 0);
        return this._tempHurtData.set({
        baseVal: o,
        critValRate: this.property.cut.CritNum,
        critRate: this.property.cut.crit,
        owner: this,
        bulletVo: null,
        hurCd: 0
        });
    },

    addBuff: function (e, t) {
        undefined === t && (t = 1);
        this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
        return this.buffMgr.add(e, t);
    },

    addBuffByData: function (e, t) {
        undefined === t && (t = 1);
        this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
        return this.buffMgr.addByData(e, t);
    },

    addSkill: function (e, t, o) {
        undefined === t && (t = true);
        undefined === o && (o = false);
        this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
        return this.skillMgr.add(e, t, o);
    },

    addSkillByData: function (e, t, o) {
        this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
        return this.skillMgr.addByData(e, t, o);
    },

    unuse: function () {
        var t;
        var o;
        this._tempHurtData && $2PropertyVo.Hurt.Pool.push(this._tempHurtData);
        this._tempHurtData = null;
        this.isActive = false;
        null === (t = this.skillMgr) || undefined === t || t.destroy();
        null === (o = this.buffMgr) || undefined === o || o.destroy();
        this.removeEffectBox();
        this.changeListener(false);
        this._super();
    },

    getShowTier: function (e) {
        if (1 == e) {
        return this.botEffectBox;
        } else {
        return this.topEffectBox;
        }
    },

    cleanEvent: function () {
        this._super();
        this.parent = null;
        this.skillMgr = null;
        this.buffMgr = null;
        this.property = null;
        this.hurtMgr = null;
    },

    enforceNonPeretration: function () {
        // TODO: 实现方法逻辑
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
