Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MinSortList = undefined;
var exp_MinSortList = function () {
  function _ctor(e) {
    this._count = 0;
    this.m_emptyNum = 0;
    this.m_log = "";
    this._compareEv = e;
    this._element = new Array();
  }
  Object.defineProperty(_ctor.prototype, "element", {
    get: function () {
      return this._element;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "count", {
    get: function () {
      return this._count;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.clear = function (e, t) {
    undefined === e && (e = null);
    undefined === t && (t = null);
    this._count = 0;
    null != e && this._element.forEach(function (o) {
      e.call(t, o);
    });
    this._element.splice(0, this._element.length);
  };
  _ctor.prototype.frist = function (e, t) {
    undefined === t && (t = null);
    if (null == e) {
      return null;
    }
    if (null == this._element) {
      return null;
    }
    for (var o = 0; o < this._element.length; o++) {
      var i = this._element[o];
      if (e.call(t, i)) {
        return i;
      }
    }
    return null;
  };
  _ctor.prototype.forEach = function (e, t) {
    null != this._element && this._element.forEach(function (o) {
      e.call(t, o);
    });
  };
  _ctor.prototype.add = function (e) {
    this._element.push(e);
    this.sortDown(this._count, e);
    ++this._count;
  };
  _ctor.prototype.del = function (e) {
    var t = this._element.indexOf(e);
    if (t < 0) {
      return null;
    } else {
      return --this._count, this._element.splice(t, 1)[0];
    }
  };
  _ctor.prototype.peek = function () {
    if (this._count <= 0) {
      return null;
    } else {
      return this._element[0];
    }
  };
  _ctor.prototype.pop = function () {
    if (this._count <= 0) {
      return null;
    } else {
      return --this._count, this._element.shift();
    }
  };
  _ctor.prototype.has = function (e) {
    return this._element.includes(e);
  };
  _ctor.prototype.sort = function (e) {
    var t = this._element.indexOf(e);
    if (!(t < 0)) {
      if (0 == t) {
        this.sortUp(t, e);
      } else if (t == this.count - 1) {
        this.sortDown(t, e);
      } else {
        var o = this._element[t - 1];
        if (this._compareEv(o, e) > 0) {
          this.sortDown(t, e);
        } else {
          this.sortUp(t, e);
        }
      }
    }
  };
  _ctor.prototype.sortDown = function (e, t) {
    var o;
    for (var i = e - 1; i >= 0 && (o = this._element[i], !(this._compareEv(o, t) <= 0)); i--) {
      this._element[i] = t;
      this._element[i + 1] = o;
    }
  };
  _ctor.prototype.sortUp = function (e, t) {
    var o;
    for (var i = e + 1; i < this._count && (o = this._element[i], !(this._compareEv(o, t) >= 0)); i++) {
      this._element[i - 1] = o;
      this._element[i] = t;
    }
  };
  _ctor.prototype.toStringT = function (e) {
    if (null != e) {
      this.m_log += e.toString() + "\n";
    } else {
      ++this.m_emptyNum;
    }
  };
  _ctor.prototype.toString = function () {
    this.m_emptyNum = 0;
    this.m_log = "";
    this.forEach(this.toStringT, this);
    this.m_emptyNum > 0 && (this.m_log += "empty:" + this.m_emptyNum);
    return this.m_log;
  };
  return _ctor;
}();
exports.MinSortList = exp_MinSortList;