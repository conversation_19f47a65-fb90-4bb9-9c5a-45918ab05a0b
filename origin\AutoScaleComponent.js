Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AutoScaleComponent = undefined;
var $2GridView = require("GridView");
var exp_AutoScaleComponent = function () {
  function _ctor() {
    this.parentSize = cc.Vec2.ZERO;
    this.itemSize = cc.Vec2.ZERO;
    this.space = cc.Vec2.ZERO;
    this.keyCount = 0;
  }
  _ctor.prototype.getScale = function () {
    var e = 1;
    var t = 0;
    var o = 0;
    var n = 0;
    switch (this.type) {
      case $2GridView.GRID_TYPE.GRID_VERTICAL:
        t = this.itemSize.x;
        o = this.space.x;
        n = this.parentSize.x;
        break;
      case $2GridView.GRID_TYPE.GRID_HORIZONTAL:
        t = this.itemSize.y;
        o = this.space.y;
        n = this.parentSize.y;
    }
    t * this.keyCount + o * (this.keyCount - 1) > n && (e = (n - o * (this.keyCount - 1)) / this.keyCount / t);
    return e;
  };
  return _ctor;
}();
exports.AutoScaleComponent = exp_AutoScaleComponent;