/**
 * LigatureBullet
 * 组件类 - 从编译后的JS反编译生成
 */

const $2BulletBase = require('BulletBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2BulletBase.default,

    properties: {
    },

    ctor: function () {
        this.connect = new Set()
    },

    // use this for initialization
    onLoad: function () {
    },

    setBulletVo: function (t) {
        this._super(t);
        this.connect.clear();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
