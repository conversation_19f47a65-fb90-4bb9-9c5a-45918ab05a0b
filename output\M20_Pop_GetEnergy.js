/**
 * M20_Pop_GetEnergy
 * 组件类 - 从编译后的JS反编译生成
 */

const $2ListenID = require('ListenID');
const $2VideoButton = require('VideoButton');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2EaseScaleTransition = require('EaseScaleTransition');
const $2AlertManager = require('AlertManager');
const $2Game = require('Game');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        coincost: {
            type: cc.Label,
            default: null
        },
        coinget: {
            type: cc.Label,
            default: null
        },
        coincount: {
            type: cc.Label,
            default: null
        },
        adcost: {
            type: cc.Label,
            default: null
        },
        adget: {
            type: cc.Label,
            default: null
        },
        adcount: {
            type: cc.Label,
            default: null
        },
        adgray: {
            type: cc.Node,
            default: null
        },
        costgray: {
            type: cc.Button,
            default: null
        },
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    ctor: function () {
        this.coincost = null
        this.coinget = null
        this.coincount = null
        this.adcost = null
        this.adget = null
        this.adcount = null
        this.adgray = null
        this.costgray = null
    },

    // use this for initialization
    onLoad: function () {
    },

    changeListener: function (t) {
        this._super(t);
    },

    onOpen: function () {
        this.mode.dailyAdpack.has("energycoin") || this.mode.dailyAdpack.addGoods("energycoin", $2Manager.Manager.vo.switchVo.diamondBuyStamina[2]);
        this.mode.dailyAdpack.has("energyad") || this.mode.dailyAdpack.addGoods("energyad", $2Manager.Manager.vo.switchVo.adBuyStamina[2]);
        this.data = {
        coin: {
        cost: $2Manager.Manager.vo.switchVo.diamondBuyStamina[0],
        get: $2Manager.Manager.vo.switchVo.diamondBuyStamina[1],
        count: this.mode.dailyAdpack.getVal("energycoin")
        },
        ad: {
        cost: $2Manager.Manager.vo.switchVo.adBuyStamina[0],
        get: $2Manager.Manager.vo.switchVo.adBuyStamina[1],
        count: this.mode.dailyAdpack.getVal("energyad")
        }
        };
        this.adgray.getChildByName("gray").active = this.data.ad.count <= 0;
        this.costgray.node.getChildByName("gray").active = this.data.coin.count <= 0;
        this.adgray.getComponent($2VideoButton.default).interactable = this.data.ad.count > 0;
        this.costgray.interactable = this.data.coin.count > 0;
        this.coincost.string = this.data.coin.cost;
        this.coincount.string = cc.js.formatStr("今日剩余%d次", this.data.coin.count);
        this.coinget.string = this.data.coin.get;
        this.adcount.string = cc.js.formatStr("今日剩余%d次", this.data.ad.count);
        this.adget.string = this.data.ad.get;
    },

    costGet: function () {
        if (this.mode.currencyIsEnough($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond, this.data.coin.cost)) {
        this.mode.dailyAdpack.useUp("energycoin");
        $2Manager.Manager.vo.knapsackVo.useUp($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond, this.data.coin.cost);
        $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Energy, this.data.coin.get);
        $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("体力+%d", this.data.coin.get));
        $2Notifier.Notifier.send($2ListenID.ListenID.Item_GoodsChange);
        this.close();
        } else {
        $2AlertManager.AlertManager.showNormalTips("灵币不足~");
        }
    },

    adGet: function () {
        cc.log(this.data.ad);
        this.mode.dailyAdpack.useUp("energyad");
        $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Energy, this.data.ad.get);
        $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("体力+%d", this.data.ad.get));
        $2Notifier.Notifier.send($2ListenID.ListenID.Item_GoodsChange);
        this.close();
    },

    onClose: function () {
        // TODO: 实现方法逻辑
    },

    setInfo: function () {
        // TODO: 实现方法逻辑
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
