/**
 * Effect_Behit
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Game = require('Game');
const $2GameEffect = require('GameEffect');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: $2GameEffect.default,

    properties: {
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        var t = this;
        this._super();
        this.deadTime = 1;
        var o = this.node.scale;
        $2Game.Game.tween(this.node).set({
        angle: $2Game.Game.random(0, 360),
        scale: 0,
        opacity: 255
        }).to(.05, {
        scale: o
        }).parallel(cc.tween().by(.2, {
        angle: 10
        }), cc.tween().to(.2, {
        scale: 0,
        opacity: 0
        })).call(function () {
        t.setDead();
        }).start();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
