/**
 * M20_ShopPartItem_daily
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Cfg = require('Cfg');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2Manager = require('Manager');
const $2GameUtil = require('GameUtil');
const $2M20_PartItem = require('M20_PartItem');
const $2M20_ShopPartItem = require('M20_ShopPartItem');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: $2M20_ShopPartItem.default,

    properties: {
        btnrefresh: {
            type: cc.Node,
            default: null
        },
        refreshTick: {
            type: cc.Label,
            default: null
        },
        refreshCount: {
            type: cc.Label,
            default: null
        }
    },

    ctor: function () {
        this.cacheTick = 0
        this.btnrefresh = null
        this.refreshTick = null
        this.refreshCount = null
    },

    // use this for initialization
    onLoad: function () {
    },

    update: function (e) {
        if (this.data && this.data.refreshCd && this.cloneitem && (this.cacheTick -= e, this.cacheTick < 0)) {
        this.cacheTick = 1;
        $2Manager.Manager.vo.userVo.dailyData.shop_refreshCd -= 1;
        var t = $2GameUtil.GameUtil.formatSeconds($2Manager.Manager.vo.userVo.dailyData.shop_refreshCd);
        this.refreshTick.string = t.str;
        $2Manager.Manager.vo.userVo.shop_refreshTimestamp = new Date().getTime();
        if ($2Manager.Manager.vo.userVo.dailyData.shop_refreshCd <= 0) {
        $2Manager.Manager.vo.userVo.shop_refreshTimestamp = 0;
        $2Manager.Manager.vo.userVo.dailyData.shop_refreshCd = this.data.refreshCd;
        this.cacheTick = 0;
        this.mode.dailyAdpack.del("30000adlimit");
        cc.sys.localStorage.setItem("saveShopList", []);
        this.refreshData(true);
        }
        $2Manager.Manager.vo.saveUserData();
        }
    },

    refreshData: function (e) {
        var t = this;
        undefined === e && (e = true);
        this.mode.cleanShopdata();
        var o = this.mode.shopGoodspck.getList();
        var i = cc.sys.localStorage.getItem("saveShopList", []);
        var n = [];
        i && "" != i && (n = (n = JSON.parse(i)) || []);
        if (n.length > 0) {
        this.content = n;
        } else if (o && o.length > 0) {
        o.forEach(function (e) {
        t.content.push($2Cfg.Cfg.BagShopItem.find({
        id: e.id
        }));
        });
        } else {
        this.content = this.getList(e);
        }
        if (0 == $2Manager.Manager.vo.userVo.dailyData.shop_refreshCd) {
        $2Manager.Manager.vo.userVo.dailyData.shop_refreshCd = this.data.refreshCd;
        $2Manager.Manager.vo.saveUserData();
        }
        if (0 == $2Manager.Manager.vo.userVo.dailyData.shop_refreshCount) {
        $2Manager.Manager.vo.userVo.dailyData.shop_refreshCount = this.data.refreshCount;
        $2Manager.Manager.vo.saveUserData();
        }
        this.resetView();
    },

    getList: function (e) {
        var t = this;
        undefined === e && (e = true);
        var o = false;
        var i = $2Manager.Manager.vo.userVo.dailyData.shopRefreshList;
        var n = i.indexOf(0);
        if (e && -1 != n) {
        o = true;
        i[n] = 1;
        }
        var r = $2GameUtil.GameUtil.weightGetList($2Cfg.Cfg.BagShopItem.getArray().filter(function (e) {
        return e.weight && e.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Equipfragments && $2Cfg.Cfg.RoleUnlock.getArray().filter(function (e) {
        return t.mode.userEquipPack.has(e.id) && 1 == e.type;
        }).map(function (e) {
        return e.id;
        }).includes(e.equipId);
        }), o ? 4 : 5, "weight");
        // if (o) {
        //   var u = $2GameUtil.GameUtil.random(0, 4);
        //   r.splice(u, 0, $2Cfg.Cfg.BagShopItem.find({
        //     type: 22
        //   }));
        // }
        r.splice(0, 0, $2Cfg.Cfg.BagShopItem.find({
        type: $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond
        }));
        if (o) {
        var p = JSON.stringify(r);
        cc.sys.localStorage.setItem("saveShopList", p);
        }
        return r;
    },

    resetView: function () {
        var t;
        var o;
        this._super();
        this.node.getChildByName("middle").active = true;
        this.btnrefresh.parent.active = this.data.refreshCount && $2Manager.Manager.vo.userVo.dailyData.shop_refreshCount >= 0;
        this.refreshTick.node.parent.parent.active = this.data.refreshCd;
        var i = null !== (t = $2Manager.Manager.vo.userVo.dailyData.shop_refreshCount) && undefined !== t ? t : null === (o = this.data) || undefined === o ? undefined : o.refreshCount;
        this.refreshCount.string = cc.js.formatStr("今日剩余%d次", i);
        for (var n = 0; n < this.content.length; n++) {
        var r = this.content[n];
        var a = 1;
        this.mode.shopGoodspck.has(r.id) || this.mode.shopGoodspck.addGoods(r.id);
        n > 0 && (a = $2GameUtil.GameUtil.getRandomByWeightInArray($2Manager.Manager.vo.switchVo.shopDiscount, 1)[0]);
        var s = this.contentnode.children[n] || cc.instantiate(this.cloneitem);
        s.setAttribute({
        parent: this.contentnode
        });
        s.getComponent($2M20_PartItem.default).setdata(r, false, a);
        }
    },

    refresh: function () {
        cc.sys.localStorage.setItem("saveShopList", []);
        $2Manager.Manager.vo.userVo.dailyData.shop_refreshCount--;
        0 == $2Manager.Manager.vo.userVo.dailyData.shop_refreshCount && ($2Manager.Manager.vo.userVo.dailyData.shop_refreshCount = -1);
        $2Manager.Manager.vo.saveUserData();
        this.refreshData(false);
    }
});
