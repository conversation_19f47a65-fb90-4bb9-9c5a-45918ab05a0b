Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.KnapsackVo = undefined;
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2Time = require("Time");
(function (e) {
  e.KMap = new Map();
  var t = function () {
    function t(t, o) {
      undefined === t && (t = true);
      this.name = "KnapsackData";
      this.isAutoSave = true;
      this.isSendMsg = true;
      this.isAutoSave = t;
      this.name = o;
      if (this.isAutoSave) {
        var i = $2Manager.Manager.storage.getString(this.name) || "{}";
        this.list = JSON.parse(i);
        this.SaveData();
      } else {
        this.list = new Map();
      }
      e.KMap.has(o) && e.KMap.delete(o);
      e.KMap.set(o, this);
    }
    t.prototype.has = function (e) {
      if (this.checkExpire(e)) {
        return 0;
      } else {
        if (this.list[e]) {
          return this.list[e].num;
        } else {
          return 0;
        }
      }
    };
    t.prototype.getItem = function (e) {
      if (this.checkExpire(e)) {
        return null;
      } else {
        return this.list[e] || null;
      }
    };
    t.prototype.getVal = function (e) {
      var t;
      return (null === (t = this.getItem(e)) || undefined === t ? undefined : t.num) || 0;
    };
    t.prototype.checkExpire = function (e) {
      var t = this.list[e];
      return !t || (t.expireTime && $2Time.Time.serverTimeMs >= t.expireTime ? (this.del(t.id), true) : undefined);
    };
    t.prototype.add = function (e) {
      var t = this.list[e.id];
      if (t) {
        t.num += e.num;
      } else {
        this.list[e.id] = e;
      }
      this.SaveData();
      this.SendEvent(e.id);
    };
    t.prototype.set = function (e) {
      this.list[e.id] = e;
      this.SaveData();
      this.SendEvent(e.id);
    };
    t.prototype.setVal = function (e, t) {
      undefined === t && (t = 1);
      if (this.has(e)) {
        this.getItem(e).num = t;
        this.SaveData();
        this.SendEvent(e);
      } else {
        this.addGoods(e, t);
      }
    };
    t.prototype.addGoods = function (e, t, o) {
      undefined === t && (t = 1);
      undefined === o && (o = $2GameSeting.GameSeting.GoodsType.Money);
      this.add({
        type: o,
        id: e,
        num: t
      });
    };
    t.prototype.useUp = function (e, t) {
      var o = this;
      undefined === t && (t = 1);
      return new Promise(function (i) {
        if (o.list[e] && o.list[e].num >= t) {
          var s = o.list[e];
          if (e == $2CurrencyConfigCfg.CurrencyConfigDefine.Energy) {
            $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 7, t);
          } else if (e == $2CurrencyConfigCfg.CurrencyConfigDefine.Coin) {
            $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 6, t);
          } else {
            e == $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond && $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 5, t);
          }
          s.num -= t;
          0 == s.num && delete o.list[e];
          o.SaveData();
          o.SendEvent(e);
          i(true);
        } else {
          i(false);
        }
      });
    };
    t.prototype.SendEvent = function (e) {
      this.isSendMsg && $2Notifier.Notifier.send($2ListenID.ListenID.Item_GoodsChange, this.name, e, this.getVal(e));
    };
    t.prototype.SaveData = function () {
      var e = this;
      if (this.isAutoSave) {
        $2Time.Time.timeDelay.cancelBy(this.sTimer);
        this.sTimer = $2Time.Time.delay(1, function () {
          var t = JSON.stringify(e.list);
          $2Manager.Manager.storage.setString(e.name, t);
          $2Manager.Manager.vo.pushRemoteDataSave();
        }).id;
      }
    };
    t.prototype.del = function (e) {
      delete this.list[e];
      this.SaveData();
    };
    t.prototype.clear = function () {
      $2Manager.Manager.storage.remove(this.name);
    };
    t.prototype.getList = function () {
      var e = [];
      for (var t in this.list) {
        e.push(this.list[t]);
      }
      return e;
    };
    t.prototype.forEach = function (e) {
      var t = 0;
      for (var o in this.list) {
        e(this.list[o], t, o);
        t++;
      }
    };
    Object.defineProperty(t.prototype, "keys", {
      get: function () {
        var e = [];
        for (var t in this.list) {
          e.push(t);
        }
        return e;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "size", {
      get: function () {
        return this.keys.length;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.filter = function (e) {
      var t = [];
      this.forEach(function (o) {
        e(o) && t.push(o);
      });
      return t;
    };
    return t;
  }();
  e.Mgr = t;
})(exports.KnapsackVo || (exports.KnapsackVo = {}));