/**
 * M20_PrePare_Activity
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2GameSeting = require('GameSeting');
const $2Cfg = require('Cfg');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2Time = require('Time');
const $2UIManager = require('UIManager');
const $2GameUtil = require('GameUtil');
const $2ModeChainsModel = require('ModeChainsModel');
const $2AlertManager = require('AlertManager');
const $2Game = require('Game');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        mode: {
            get() {
                return $2ModeChainsModel.default.instance;
            },
            visible: false
        }
    },

    // use this for initialization
    onLoad: function () {
    },

    setInfo: function () {
        this.nodeArr[0].getChildByName("MoreGames").zIndex = 999;
        this.nodeArr[1].active = 1 == $2Manager.Manager.vo.switchVo.GameKnife;
    },

    onEnable: function () {
        this.resetState();
    },

    resetState: function () {
        var e = this;
        this.unscheduleAllCallbacks();
        var t = 0;
        $2Cfg.Cfg.activity.forEach(function (o) {
        e.setItem(e.mode.rVo.activityList[o.id], t);
        t++;
        });
    },

    setItem: function (e, t) {
        var o = $2Cfg.Cfg.activity.get(e.id);
        var i = this.nodeArr[0].filterChild({
        name: "item"
        })[t] || cc.instantiate(this.nodeArr[0].children[0]).setAttribute({
        parent: this.nodeArr[0]
        });
        i.getComByPath(cc.Label, "name").string = o.actName;
        i.zIndex = o.unlockChapter;
        var n = i.getChildByName("btn");
        n.targetOff(this);
        n.on(cc.Node.EventType.TOUCH_END, function () {
        if ($2ModeBackpackHeroModel.default.instance.userEquipPack.filter(function (e) {
        return e.isFitOut;
        }).length < 8) {
        return $2AlertManager.AlertManager.showNormalTips("装备数量不足");
        } else {
        if (0 == e.cgNum) {
        return $2AlertManager.AlertManager.showNormalTips("挑战次数不足");
        } else {
        return void $2Notifier.Notifier.call($2CallID.CallID.Item_User, {
        type: $2CurrencyConfigCfg.CurrencyConfigDefine.Energy,
        val: $2Manager.Manager.vo.switchVo.fightStamina,
        call: function (t) {
        if (t == $2GameSeting.GameSeting.ProgressCode.COMPLETE) {
        e.cgNum--;
        var i = $2Game.Game.getMouth($2Game.Game.Mode.CHAINS);
        $2Notifier.Notifier.send(i.mouth, $2Game.Game.Mode.CHAINS, $2MVC.MVC.openArgs().setParam({
        id: o.lvId
        }));
        }
        }
        });
        }
        }
        }, this);
        $2Manager.Manager.loader.loadSpriteToSprit(o.actIcon, i.getComponent(cc.Sprite));
        var r = i.getChildByName("rewardList");
        o.reward.forEach(function (e, t) {
        var o = $2Cfg.Cfg.CurrencyConfig.get(e);
        var i = r.children[t] || cc.instantiate(r.children[0]).setAttribute({
        parent: r
        });
        $2Manager.Manager.loader.loadSpriteToSprit(o.icon, i.getComByChild(cc.Sprite, "icon"));
        $2Manager.Manager.loader.loadSpriteToSprit($2GameSeting.GameSeting.getRarity(o.rarity).blockImg, i.getComponent(cc.Sprite));
        });
        r.spliceNode(o.reward.length, 10);
        var p = function () {
        i.getComByChild(cc.Label, "time").string = cc.js.formatStr("剩余时间:%s", $2GameUtil.GameUtil.formatSeconds(($2Time.Time.midnight - $2Time.Time.serverTimeMs) / 1e3).str);
        i.getComByPath(cc.Label, "mask/lockMsg").string = cc.js.formatStr("通关第%d章解锁", o.unlockChapter);
        i.getComByChild(cc.Label, "num").string = cc.js.formatStr("今日剩余挑战次数:%d", e.cgNum);
        i.getChildByName("mask").setActive($2Manager.Manager.leveMgr.vo.curPassLv < o.unlockChapter);
        };
        p();
        this.schedule(p, 1);
    },

    onShowFinish: function () {
        var e;
        var t;
        null === (t = null === (e = this.param) || undefined === e ? undefined : e.showCb) || undefined === t || t.call(e, this.node);
        this.node.opacity = 255;
    },

    onOpen: function () {
        this.node.opacity = 0;
    },

    getThis: function () {
        return this;
    },

    onBtn: function (e, t) {
        if (t.includes("ui/")) {
        var o = $2MVC.MVC.openArgs();
        if ("ui/setting/MoreGamesView" == t) {
        var i = $2MVC.MVC.openArgs();
        i.setParam({
        pageIndex: $2Game.Game.Mode.CHAINS
        });
        return void $2UIManager.UIManager.Open("ui/setting/MoreGamesView", i);
        }
        $2UIManager.UIManager.OpenInQueue(t, o);
        }
    },

    call: function (t) {
        if (t == $2GameSeting.GameSeting.ProgressCode.COMPLETE) {
        e.cgNum--;
        var i = $2Game.Game.getMouth($2Game.Game.Mode.CHAINS);
        $2Notifier.Notifier.send(i.mouth, $2Game.Game.Mode.CHAINS, $2MVC.MVC.openArgs().setParam({
        id: o.lvId
        }));
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
