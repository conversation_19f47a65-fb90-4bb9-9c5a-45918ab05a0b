var cc__awaiter = __awaiter;
var cc__generator = __generator;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SdkLauncher = undefined;
var $2Cfg = require("Cfg");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2Time = require("Time");
var $2AlertManager = require("AlertManager");
var $2StorageID = require("StorageID");
var $2GameUtil = require("GameUtil");
var $2KnapsackVo = require("KnapsackVo");
var exp_SdkLauncher = function () {
  function _ctor(e, t) {
    this.isLoadData = false;
    this.finishWatch = null;
    this.launchDesc = null;
    this.pBar = null;
    this._pNum = 10;
    this._50state = false;
    this.launchDesc = e;
    this.pBar = t;
    wonderSdk.setAlertAdpater($2AlertManager.AlertManager);
    wonderSdk.setAudioAdapter($2Manager.Manager.audio);
    $2Manager.Manager.net.setAppName(wonderSdk.BMS_APP_NAME);
    this._pNum = 10;
    this.showPrivacy();
    this.login();
  }
  _ctor.prototype.login = function () {
    return cc__awaiter(this, undefined, undefined, function () {
      var e;
      return cc__generator(this, function (t) {
        switch (t.label) {
          case 0:
            return [4, Promise.all([this.loadTime(), this.loadGameSwitchConfig(), this.loadView()])];
          case 1:
            t.sent();
            e = new Date($2Time.Time.serverTimeMs);
            $2GameUtil.GameUtil.dateFormat2(e, "yyyy-MM-dd HH:mm:ss.fff");
            return [4, this.loadAllData()];
          case 2:
            t.sent();
            $2Notifier.Notifier.send($2ListenID.ListenID.Login_Finish);
            console.log("[Login_Finish]");
            return [2];
        }
      });
    });
  };
  _ctor.prototype.loadAllData = function () {
    return cc__awaiter(this, undefined, undefined, function () {
      return cc__generator(this, function (e) {
        switch (e.label) {
          case 0:
            console.log("[loadAllData]");
            return [4, this.sdkLogin()];
          case 1:
            e.sent();
            return [4, this.requestSaveData()];
          case 2:
            e.sent();
            return [4, this.loadUserData()];
          case 3:
            e.sent();
            return [4, Promise.all([this.loadShareConfig()])];
          case 4:
            e.sent();
            $2Manager.Manager.vo._knapsackVo = new $2KnapsackVo.KnapsackVo.Mgr(true, $2StorageID.StorageID.MCatGame);
            this.initData();
            wonderSdk.preLoadRewardVideo();
            return [2];
        }
      });
    });
  };
  _ctor.prototype.initData = function () {
    var e = new Date($2Time.Time.serverTimeMs).getDate();
    if (e != $2Manager.Manager.vo.userVo.day) {
      $2Manager.Manager.vo.userVo.day = e;
      $2Manager.Manager.vo.userVo.loginDay++;
      $2Manager.Manager.vo.userVo.dailyData.freeCoin = $2Manager.Manager.vo.userVo.dailyData.freeDiamond = true;
      $2Notifier.Notifier.send($2ListenID.ListenID.Game_SecondDay);
    }
    $2Manager.Manager.vo.isNewUser = $2Manager.Manager.vo.userVo.loginDay <= 1;
  };
  _ctor.prototype.checkLogin = function () {
    if (this.isLoadData && $2Manager.Manager.vo.userVo.isAcceptPrivacy) {
      $2Time.Time.doCancel(this.finishWatch);
      $2Notifier.Notifier.send($2ListenID.ListenID.Login_Finish);
      this.finishWatch = null;
    }
  };
  _ctor.prototype.loadView = function () {
    return cc__awaiter(this, undefined, Promise, function () {
      var e;
      var t;
      var o;
      var i;
      var r = this;
      return cc__generator(this, function () {
        t = .45 / (e = ["ui/loading/LoadingView", "ui/ModeBackpackHero/M20_PrePare_Fight", "ui/main/MainView"]).length;
        o = 0;
        i = e.length;
        return [2, new Promise(function (n) {
          if (e.length <= 0) {
            r.addProgress(.4);
            n();
          } else {
            for (var a = 0; a < e.length; a++) {
              var s = e[a];
              $2Manager.Manager.loader.loadRes(s, cc.Prefab, function (e, o) {
                r.addProgress(t * (1 / o));
              }, function () {
                o++;
                $2Manager.Manager.storage.getNumber($2StorageID.StorageID.TempisFirst, 1);
                o >= i && n(null);
              });
            }
          }
        })];
      });
    });
  };
  _ctor.prototype.loadTime = function () {
    return cc__awaiter(this, undefined, Promise, function () {
      var e = this;
      return cc__generator(this, function () {
        return [2, new Promise(function (t) {
          wonderSdk.requestServerTime().then(function (o) {
            if (!wonderSdk.isTest && o && o.data.time) {
              o && o.data.time && $2Time.Time.setServerTime(1e3 * o.data.time);
            } else {
              var i = Date.now();
              $2Time.Time.setServerTime(i);
            }
            e.addProgress(.03);
            t(null);
          }).catch(function () {
            var o = Date.now();
            $2Time.Time.setServerTime(o);
            e.addProgress(.03);
            t(null);
          });
        })];
      });
    });
  };
  _ctor.prototype.loadMusic = function () {
    return cc__awaiter(this, undefined, Promise, function () {
      var e;
      var t;
      var o;
      var i = this;
      return cc__generator(this, function () {
        e = $2Cfg.Cfg.Sound.filter({
          loop: true
        });
        t = e.length;
        o = 0;
        return [2, new Promise(function (n) {
          var r = function (r) {
            var a = e[r];
            var s = a.id;
            $2Manager.Manager.loader.loadRes(a.path, cc.AudioClip, function (e, r) {
              o++;
              if (!e) {
                var a = r;
                $2Manager.Manager.audio.setMusicClip(s, a);
              }
              if (o >= t) {
                i.addProgress(.1);
                n(null);
              }
            });
          };
          for (var a = 0; a < e.length; a++) {
            r(a);
          }
        })];
      });
    });
  };
  _ctor.prototype.loadUserData = function () {
    return cc__awaiter(this, undefined, Promise, function () {
      var e = this;
      return cc__generator(this, function () {
        return [2, new Promise(function (t) {
          var o;
          var i = $2Manager.Manager.storage.getObject($2StorageID.StorageID.UserData, null);
          cc.log("[loadUserData]", i);
          i && $2Manager.Manager.vo.userVo.updatetUserVo(i);
          (o = $2Manager.Manager.vo.userVo).openId || (o.openId = $2Manager.Manager.vo.openId);
          $2Manager.Manager.vo.isGetData = true;
          e.addProgress(.1);
          t(null);
        })];
      });
    });
  };
  _ctor.prototype.requestSaveData = function () {
    return cc__awaiter(this, undefined, Promise, function () {
      return cc__generator(this, function () {
        return [2, new Promise(function (e) {
          if ($2Manager.Manager.vo.openId) {
            var t = $2Manager.Manager.storage.getObject($2StorageID.StorageID.UserData, null);
            wonderSdk.requestSaveData([$2StorageID.StorageID.GameTag]).then(function (o) {
              console.log("[requestSaveData]获取到远端数据Size:", o.length);
              if (o) {
                var i = (o = JSON.parse(o))[$2StorageID.StorageID.UserData];
                if (i && (!t || !t.saveDataTime || i.saveDataTime > t.saveDataTime)) {
                  for (var n in o) {
                    o[n] && "string" == typeof o[n] && $2Manager.Manager.storage.setString(n, o[n]);
                  }
                }
              }
              e(null);
            }).catch(function () {
              e(null);
            });
          } else {
            e(null);
          }
        })];
      });
    });
  };
  _ctor.prototype.loadGameSwitchConfig = function () {
    return cc__awaiter(this, undefined, Promise, function () {
      var e = this;
      return cc__generator(this, function () {
        return [2, new Promise(function (t) {
          wonderSdk.requestSwitchConfig().then(function (o) {
            $2Manager.Manager.vo.updateSwitchVo(o.data);
            e.addProgress(.12);
            t(null);
          }).catch(function () {
            e.addProgress(.12);
            t(null);
          });
        })];
      });
    });
  };
  _ctor.prototype.sdkLogin = function () {
    return cc__awaiter(this, undefined, Promise, function () {
      return cc__generator(this, function () {
        return [2, new Promise(function (e) {
          wonderSdk.login().then(function () {
            e(null);
          }).catch(function () {
            e(null);
          });
        })];
      });
    });
  };
  _ctor.prototype.loadShareConfig = function () {
    return cc__awaiter(this, undefined, undefined, function () {
      return cc__generator(this, function () {
        return [2, new Promise(function (e) {
          wonderSdk.requestShareConfig().then(function () {
            e(null);
          }).catch(function () {
            e(null);
          });
        })];
      });
    });
  };
  _ctor.prototype.showPrivacy = function () {
    wonderSdk.showPrivacy(function (e) {
      $2Manager.Manager.vo.userVo.isAcceptPrivacy = !!e;
    });
  };
  Object.defineProperty(_ctor.prototype, "pNum", {
    get: function () {
      if (this._pNum > 100) {
        return 100;
      } else {
        return this._pNum;
      }
    },
    set: function (e) {
      this._pNum = e;
      this._pNum > 100 && (this._pNum = 100);
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.addProgress = function (e) {
    if (this.pBar) {
      this.pNum = this.pNum + 100 * e;
      this.pBar.progress = this.pNum / 100;
      this.pBar.progress >= .5 && !this._50state && (this._50state = true);
      this.launchDesc.string = cc.js.formatStr("载入中...%d%", Math.round(this.pNum));
    }
  };
  return _ctor;
}();
exports.SdkLauncher = exp_SdkLauncher;