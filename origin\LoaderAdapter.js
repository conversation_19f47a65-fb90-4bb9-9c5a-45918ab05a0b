var cc__awaiter = __awaiter;
var cc__generator = __generator;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.LoaderAdapter = undefined;
var $2GameSkeleton = require("GameSkeleton");
var $2AssetLoader = require("AssetLoader");
var $2ResUtil = require("ResUtil");
var c = function () {
  function e() {
    this.globalUseId = 0;
  }
  e.prototype.loadRes = function () {
    var e = $2AssetLoader.default.makeLoadResArgs.apply(this, arguments);
    $2AssetLoader.assetLoader.loadRes(e.url, e.type, e.onProgess, e.onCompleted, e.bundle);
  };
  e.prototype.preloadRes = function () {
    var e = $2AssetLoader.default.makeLoadResArgs.apply(this, arguments);
    $2AssetLoader.assetLoader.preloadRes(e.url, e.type, e.onProgess, e.onCompleted, e.bundle);
  };
  e.prototype.loadResDir = function () {
    var e = $2AssetLoader.default.makeLoadResArgs.apply(this, arguments);
    $2AssetLoader.assetLoader.loadResDir(e.url, e.type, e.onProgess, e.onCompleted, e.bundle);
  };
  e.prototype.preloadResDir = function () {
    var e = $2AssetLoader.default.makeLoadResArgs.apply(this, arguments);
    $2AssetLoader.assetLoader.preloadResDir(e.url, e.type, e.onProgess, e.onCompleted, e.bundle);
  };
  e.prototype.releaseAsset = function (e) {
    $2AssetLoader.assetLoader.releaseAsset(e);
  };
  e.prototype.loadSpriteAsync = function (e, t) {
    var o = this;
    return new Promise(function (i, n) {
      if (!e) {
        return cc.warn("缺失路径");
      }
      o.loadRes(e, cc.SpriteFrame, function (e, r) {
        if (e) {
          n(e);
        } else {
          var a = r;
          r instanceof cc.Texture2D && (a = new cc.SpriteFrame(r));
          t && cc.isValid(t) && o.setAutoRelease(a, t, true);
          i(a);
        }
      });
    });
  };
  e.prototype.loadSpriteToSprit = function (e, t, o) {
    var i = this;
    return new Promise(function (n) {
      if (e) {
        (null == t ? undefined : t.isValid) && i.loadSpriteAsync(e, o).then(function (e) {
          if (null == t ? undefined : t.isValid) {
            t.spriteFrame = e;
            n(e);
          }
        });
      } else {
        t.spriteFrame = null;
      }
    });
  };
  e.prototype.loadSpriteImg = function (e, t, o) {
    var i = this;
    return new Promise(function (n) {
      var r = new cc.Node("img");
      var a = r.addComponent(cc.Sprite);
      i.loadSpriteAsync(e, o).then(function (e) {
        a.spriteFrame = e;
        (null == t ? undefined : t.nodeAttr) && r.setAttribute(t.nodeAttr);
        n(r);
      });
    });
  };
  e.prototype.loadSpine = function (e, t) {
    var o = this;
    return new Promise(function (i, n) {
      o.loadRes(e, sp.SkeletonData, function (e, r) {
        if (e) {
          n(e);
        } else {
          t && cc.isValid(t) && o.setAutoRelease(r, t, true);
          i(r);
        }
      });
    });
  };
  e.prototype.loadSpineNode = function (e, t, o) {
    var i = this;
    return new Promise(function (n) {
      var a = new cc.Node("Spine");
      var s = a.addComponent($2GameSkeleton.default);
      i.loadSpine(e, o).then(function (o) {
        s.reset(o);
        if (!t.nodeAttr.parent.isValid) {
          n(s);
          return a.destroy();
        }
        null == t.spAttr.isPlayerOnLoad && (t.spAttr.isPlayerOnLoad = true);
        t.spAttr.path = e;
        s.setAttribute(t.spAttr);
        (null == t ? undefined : t.nodeAttr) && a.setAttribute(t.nodeAttr);
        t.delayRemove && cc.tween(a).delay(t.delayRemove).to(.2, {
          opacity: 0
        }).call(function () {
          a.isValid && a.destroy();
        }).start();
        n(s);
      });
    });
  };
  e.prototype.loadSpriteAltasAsync = function (e, t) {
    var o = this;
    return new Promise(function (i, n) {
      o.loadRes(e, function (e, r) {
        if (e) {
          n(e);
        } else {
          t && cc.isValid(t) && o.setAutoRelease(r, t);
          i(r);
        }
      });
    });
  };
  e.prototype.loadRelease = function (e, t) {
    var o = this;
    return new Promise(function (i, n) {
      o.loadRes(e, function (e, r) {
        if (e) {
          n(e);
        } else {
          t && cc.isValid(t) && o.setAutoRelease(r, t);
          i(r);
        }
      });
    });
  };
  e.prototype.loadPrefab = function (e) {
    var t = this;
    return new Promise(function (o, i) {
      t.loadRes(e, cc.Prefab, function (e, n) {
        if (e) {
          console.warn("[动态加载Prefab资源]错误,", e);
          i(e);
        } else {
          var r = t.instantiate(n);
          o(r);
        }
      });
    });
  };
  e.prototype.loadPrefabRes = function (e, t) {
    var o = this;
    return new Promise(function (i, n) {
      o.loadRes(e, cc.Prefab, function (e, r) {
        if (e) {
          n(e);
        } else {
          t && cc.isValid(t) && o.setAutoRelease(r, t, true);
          i(r);
        }
      });
    });
  };
  e.prototype.loadSpriteFrameList = function (e, t) {
    var o = this;
    return new Promise(function (r, a) {
      return cc__awaiter(o, undefined, undefined, function () {
        var o;
        var i;
        var s;
        var c;
        var l = this;
        return cc__generator(this, function (n) {
          switch (n.label) {
            case 0:
              o = [];
              i = [];
              s = function (n) {
                i.push(new Promise(function (i) {
                  var r = n;
                  l.loadSpriteAsync(e[n], t).then(function (e) {
                    o[r] = e;
                    i();
                  }).catch(function (e) {
                    a(e);
                  });
                }));
              };
              for (c = 0; c < e.length; c++) {
                s(c);
              }
              return [4, Promise.all(i)];
            case 1:
              n.sent();
              r(o);
              return [2];
          }
        });
      });
    });
  };
  e.sequencePromise = function (e) {
    return cc__awaiter(this, undefined, undefined, function () {
      var t;
      var o;
      return cc__generator(this, function (i) {
        switch (i.label) {
          case 0:
            t = 0;
            o = e;
            i.label = 1;
          case 1:
            if (t < o.length) {
              return [4, (0, o[t])()];
            } else {
              return [3, 4];
            }
          case 2:
            i.sent();
            i.label = 3;
          case 3:
            t++;
            return [3, 1];
          case 4:
            return [2];
        }
      });
    });
  };
  e.prototype.instantiate = function (e) {
    var t = cc.instantiate(e);
    this.setAutoRelease(e, t, true);
    return t;
  };
  e.prototype.loadDragonBones = function (e, t, o) {
    var i = this;
    return new Promise(function (n, r) {
      i.loadRes(e, dragonBones.DragonBonesAsset, function (e, a) {
        if (e) {
          r();
        } else {
          var s = a;
          if (o && cc.isValid(o)) {
            i.setAutoRelease(a, o, true);
            i.loadRes(t, dragonBones.DragonBonesAtlasAsset, function (e, t) {
              if (e) {
                r();
              } else {
                i.setAutoRelease(t, o, true);
                n({
                  asset: s,
                  atlasAsset: t
                });
              }
            });
          }
        }
      });
    });
  };
  e.prototype.loadDragonBonesByPath = function (e, t, o, i) {
    var n = t.getComponent(dragonBones.ArmatureDisplay);
    if (n) {
      var r = this;
      $2AssetLoader.assetLoader.loadResDir(e, function (e, a) {
        if (!(e || a.length <= 0)) {
          n.dragonAsset = null;
          n.dragonAtlasAsset = null;
          for (var s in a) {
            if (a[s] instanceof dragonBones.DragonBonesAsset) {
              n.dragonAsset = a[s];
              r.setAutoRelease(a[s], t, true);
            }
            if (a[s] instanceof dragonBones.DragonBonesAtlasAsset) {
              n.dragonAtlasAsset = a[s];
              r.setAutoRelease(a[s], t, true);
            }
          }
          n.armatureName = "Armature";
          if (o) {
            n.playAnimation(n.getAnimationNames(n.getArmatureNames()[0])[0], -1);
            n.timeScale = 1;
          }
          i && i();
        }
      });
    }
  };
  e.prototype.setAutoRelease = function (e, t, o) {
    var i = $2ResUtil.ResUtil.getResKeeper(t, o);
    if (e && i) {
      return i.autoReleaseRes({
        asset: e
      }), e;
    } else {
      return null;
    }
  };
  e.prototype.makeUseKey = function () {
    return "@useKey_" + this.globalUseId++;
  };
  return e;
}();
exports.LoaderAdapter = new c();