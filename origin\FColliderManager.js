Object.defineProperty(exports, "__esModule", {
  value: true
});
var i;
var $2FCollider = require("FCollider");
var $2Intersection = require("Intersection");
var $2QuadTree = require("QuadTree");
var s = cc.v2();
var c = cc.mat4();
var l = [];
var u = 0;
function p(e, t, o, i, n, r) {
  var a = e.x;
  var s = e.y;
  var c = e.width;
  var l = e.height;
  var u = t.m;
  var p = u[0];
  var f = u[1];
  var h = u[4];
  var d = u[5];
  var g = p * a + h * s + u[12];
  var y = f * a + d * s + u[13];
  var m = p * c;
  var _ = f * c;
  var v = h * l;
  var M = d * l;
  i.x = g;
  i.y = y;
  n.x = m + g;
  n.y = _ + y;
  o.x = v + g;
  o.y = M + y;
  r.x = m + v + g;
  r.y = _ + M + y;
}
(function (e) {
  e.onEnter = "onCollisionEnter";
  e.onStay = "onCollisionStay";
  e.onExit = "onCollisionExit";
})(i || (i = {}));
var def_FColliderManager = function () {
  function _ctor() {
    this._tree = null;
    this._treeDirty = true;
    this._maxDepth = 4;
    this._maxChildren = 10;
    this._treeRect = cc.rect(0, 0, cc.winSize.width, cc.winSize.height);
    this._enable = false;
    this._colliders = [];
    this._enableDebugDraw = false;
    this._enableQuadTreeDraw = false;
    this._debugDrawer = null;
    this._tree = new $2QuadTree.QuadTree(this._treeRect, 0, this._maxDepth, this._maxChildren);
  }
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      this._instance || (this._instance = new _ctor());
      return this._instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "maxDepth", {
    get: function () {
      return this._maxDepth;
    },
    set: function (e) {
      if (e != this._maxDepth) {
        this._maxDepth = e;
        this._treeDirty = true;
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "maxChildren", {
    get: function () {
      return this._maxChildren;
    },
    set: function (e) {
      if (this._maxChildren != e) {
        this._maxChildren = e;
        this._treeDirty = true;
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "treeRect", {
    get: function () {
      return this._treeRect;
    },
    set: function (e) {
      if (!(this._treeRect && this._treeRect.equals(e))) {
        this._treeRect.set(e);
        this._treeDirty = false;
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "enable", {
    get: function () {
      return this._enable;
    },
    set: function (e) {
      this._enable = e;
      if (e) {
        cc.director.getScheduler().enableForTarget(this);
        cc.director.getScheduler().scheduleUpdate(this, cc.Scheduler.PRIORITY_NON_SYSTEM, false);
      }
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.clearMgr = function () {
    l.length = 0;
    this._tree.clear();
  };
  Object.defineProperty(_ctor.prototype, "colliders", {
    get: function () {
      return this._colliders;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.addCollider = function (e) {
    var t = this._colliders;
    this.initCollider(e);
    t.push(e);
  };
  _ctor.prototype.removeCollider = function (e) {
    var t = this;
    for (var o = this._colliders.length - 1; o >= 0; o--) {
      var i = this._colliders[o];
      if (e.colliderId === i.colliderId) {
        this._colliders.splice(o, 1);
        var n = e.contactMap;
        n && n.forEach(function (o) {
          t.updateCollideExit(e, o.other);
        });
        break;
      }
    }
  };
  _ctor.prototype.initCollider = function (e) {
    e.initCollider();
  };
  _ctor.prototype.updateCollider = function (e) {
    e.node.getWorldMatrix(c);
    e.cindex.length = 0;
    if (e.type === $2FCollider.ColliderType.Box) {
      var t = (_ = e).size;
      _.aabb.x = _.offset.x - t.width / 2;
      _.aabb.y = _.offset.y - t.height / 2;
      _.aabb.width = t.width;
      _.aabb.height = t.height;
      var o = _.worldPoints;
      var i = o[0];
      var r = o[1];
      var a = o[2];
      var l = o[3];
      p(_.aabb, c, i, r, a, l);
      var u = Math.min(i.x, r.x, a.x, l.x);
      var f = Math.min(i.y, r.y, a.y, l.y);
      var h = Math.max(i.x, r.x, a.x, l.x);
      var d = Math.max(i.y, r.y, a.y, l.y);
      var g = _.worldEdge;
      var y = 0;
      for (var m = o.length; y < m; y++) {
        g[y] || (g[y] = cc.v2());
        cc.Vec2.subtract(g[y], o[(y + 1) % m], o[y]);
      }
      _.aabb.x = u;
      _.aabb.y = f;
      _.aabb.width = h - u;
      _.aabb.height = d - f;
    } else if (e.type == $2FCollider.ColliderType.Circle) {
      var _ = e;
      cc.Vec2.transformMat4(s, _.offset, c);
      _.worldPosition.x = s.x;
      _.worldPosition.y = s.y;
      var v = c.m;
      var M = v[12];
      var b = v[13];
      v[12] = v[13] = 0;
      s.x = _.radius;
      s.y = 0;
      cc.Vec2.transformMat4(s, s, c);
      var C = Math.sqrt(s.x * s.x + s.y * s.y);
      _.worldRadius = C;
      _.aabb.x = _.worldPosition.x - C;
      _.aabb.y = _.worldPosition.y - C;
      _.aabb.width = 2 * C;
      _.aabb.height = 2 * C;
      v[12] = M;
      v[13] = b;
    } else if (e.type == $2FCollider.ColliderType.Polygon) {
      var w = (_ = e).points;
      var S = _.worldPoints;
      g = _.worldEdge;
      S.length = w.length;
      u = Number.MAX_SAFE_INTEGER;
      f = Number.MAX_SAFE_INTEGER;
      h = -Number.MAX_SAFE_INTEGER;
      d = -Number.MAX_SAFE_INTEGER;
      y = 0;
      for (m = w.length; y < m; y++) {
        S[y] || (S[y] = cc.v2());
        s.x = w[y].x + _.offset.x;
        s.y = w[y].y + _.offset.y;
        cc.Vec2.transformMat4(s, s, c);
        var k = s.x;
        var P = s.y;
        S[y].set(s);
        k > h && (h = k);
        k < u && (u = k);
        P > d && (d = P);
        P < f && (f = P);
      }
      if (e.isConvex) {
        y = 0;
        for (m = S.length; y < m; y++) {
          g[y] || (g[y] = cc.v2());
          cc.Vec2.subtract(g[y], S[(y + 1) % m], S[y]);
        }
      }
      _.aabb.x = u;
      _.aabb.y = f;
      _.aabb.width = h - u;
      _.aabb.height = d - f;
    }
  };
  _ctor.prototype.shouldCollide = function (e, t) {
    var o = e.node;
    var i = t.node;
    var n = cc.game.collisionMatrix;
    return o !== i && n[o.groupIndex][i.groupIndex];
  };
  _ctor.prototype.update = function (e) {
    this.enable && this.oneTest(e);
  };
  _ctor.prototype.getColliderCompsInRange = function (e) {
    var t = [];
    for (var o = 0; o < e.cindex.length; o++) {
      if (l[e.cindex[o]]) {
        for (var i = 0; i < l[e.cindex[o]].length; i++) {
          var n = l[e.cindex[o]][i];
          n.uuid != e.uuid && t.push(n);
        }
      }
    }
    return t;
  };
  Object.defineProperty(_ctor.prototype, "tempList", {
    get: function () {
      return l;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.oneTest = function () {
    var e = this;
    if (this._treeDirty) {
      this._tree = new $2QuadTree.QuadTree(this._treeRect, 0, this._maxDepth, this._maxChildren);
      this._treeDirty = false;
    }
    this._tree.clear();
    for (var t = this._colliders.length - 1; t >= 0; t--) {
      var o = this._colliders[t];
      if (o.isActive) {
        if (o && o.isValid) {
          o.contactMap.forEach(function (e) {
            e.state = $2FCollider.StateType.NoTest;
          });
          this.updateCollider(this._colliders[t]);
          this._tree.insert(this._colliders[t]);
        } else {
          this._colliders.splice(t, 1);
        }
      }
    }
    l.length = 0;
    this._tree.getAllNeedTestColliders(l);
    var i = 0;
    for (var s = l.length; i < s; i++) {
      var c = l[i];
      t = 0;
      for (var p = c.length; t < p; t++) {
        var f = c[t];
        for (var h = t + 1; h < p; h++) {
          var d = c[h];
          if (this.shouldCollide(f, d)) {
            switch (f.type) {
              case $2FCollider.ColliderType.Circle:
                if (d.type === $2FCollider.ColliderType.Circle) {
                  u = $2Intersection.Intersection.circleCircle(f, d) ? 1 : 0;
                } else {
                  d.type !== $2FCollider.ColliderType.Box && d.type !== $2FCollider.ColliderType.Polygon || (u = $2Intersection.Intersection.polygonCircle(d.worldPoints, f) ? 1 : 0);
                }
                break;
              case $2FCollider.ColliderType.Box:
                if (d.type === $2FCollider.ColliderType.Circle) {
                  u = $2Intersection.Intersection.polygonCircle(f.worldPoints, d) ? 1 : 0;
                } else if (d.type === $2FCollider.ColliderType.Box) {
                  u = 0 === f.node.angle && 0 === d.node.angle ? $2Intersection.Intersection.rectRect(f.aabb, d.aabb) ? 1 : 0 : $2Intersection.Intersection.satPolygonPolygon(f.worldPoints, d.worldPoints, f.worldEdge, d.worldEdge) ? 1 : 0;
                } else {
                  d.type === $2FCollider.ColliderType.Polygon && (u = d.isConvex ? $2Intersection.Intersection.satPolygonPolygon(f.worldPoints, d.worldPoints, f.worldEdge, d.worldEdge) ? 1 : 0 : $2Intersection.Intersection.polygonPolygon(f.worldPoints, d.worldPoints) ? 1 : 0);
                }
                break;
              case $2FCollider.ColliderType.Polygon:
                u = d.type === $2FCollider.ColliderType.Circle ? $2Intersection.Intersection.polygonCircle(f.worldPoints, d) ? 1 : 0 : f.isConvex && d.isConvex ? $2Intersection.Intersection.satPolygonPolygon(f.worldPoints, d.worldPoints, f.worldEdge, d.worldEdge) ? 1 : 0 : $2Intersection.Intersection.polygonPolygon(f.worldPoints, d.worldPoints) ? 1 : 0;
            }
            if (1 == u) {
              this.updateCollideContact(f, d);
            } else {
              this.updateCollideExit(f, d);
            }
          }
        }
      }
    }
    var g = function (t) {
      var o = y._colliders[t];
      o.contactMap.forEach(function (t) {
        t.state === $2FCollider.StateType.NoTest && e.updateCollideExit(o, t.other);
      });
    };
    var y = this;
    for (t = this._colliders.length - 1; t >= 0; t--) {
      g(t);
    }
    this.drawColliders();
    this.drawQuadTree();
  };
  _ctor.prototype.checkCollider = function (e, t) {
    t = t || [];
    var o = [];
    this._tree.retrieve(e, o);
    var i = 0;
    for (var a = o.length; i < a; i++) {
      var s = o[i];
      if (s.colliderId !== e.colliderId && this.shouldCollide(e, s)) {
        switch (e.type) {
          case $2FCollider.ColliderType.Circle:
            if (s.type === $2FCollider.ColliderType.Circle) {
              $2Intersection.Intersection.circleCircle(e, s) && t.push(s);
            } else {
              s.type !== $2FCollider.ColliderType.Box && s.type !== $2FCollider.ColliderType.Polygon || $2Intersection.Intersection.polygonCircle(s.worldPoints, e) && t.push(s);
            }
            break;
          case $2FCollider.ColliderType.Box:
            if (s.type === $2FCollider.ColliderType.Circle) {
              $2Intersection.Intersection.polygonCircle(e.worldPoints, s) && t.push(s);
            } else if (s.type === $2FCollider.ColliderType.Box) {
              if (0 === e.node.angle && 0 === s.node.angle) {
                $2Intersection.Intersection.rectRect(e.aabb, s.aabb) && t.push(s);
              } else {
                $2Intersection.Intersection.satPolygonPolygon(e.worldPoints, s.worldPoints, e.worldEdge, s.worldEdge) && t.push(s);
              }
            } else if (s.type === $2FCollider.ColliderType.Polygon) {
              if (s.isConvex) {
                $2Intersection.Intersection.satPolygonPolygon(e.worldPoints, s.worldPoints, e.worldEdge, s.worldEdge) && t.push(s);
              } else {
                $2Intersection.Intersection.polygonPolygon(e.worldPoints, s.worldPoints) && t.push(s);
              }
            }
            break;
          case $2FCollider.ColliderType.Polygon:
            if (s.type === $2FCollider.ColliderType.Circle) {
              $2Intersection.Intersection.polygonCircle(e.worldPoints, s) && t.push(s);
            } else if (e.isConvex && s.isConvex) {
              $2Intersection.Intersection.satPolygonPolygon(e.worldPoints, s.worldPoints, e.worldEdge, s.worldEdge) && t.push(s);
            } else {
              $2Intersection.Intersection.polygonPolygon(e.worldPoints, s.worldPoints) && t.push(s);
            }
        }
      }
    }
    return t;
  };
  _ctor.prototype.updateCollideContact = function (e, t) {
    var o = e.contactMap.get(t.colliderId);
    if (o) {
      o.state = $2FCollider.StateType.IsTest;
      this._doCollide(e, t, i.onStay);
    } else {
      e.contactMap.set(t.colliderId, {
        other: t,
        state: $2FCollider.StateType.IsTest
      });
      this._doCollide(e, t, i.onEnter);
    }
    var r = t.contactMap.get(e.colliderId);
    if (r) {
      r.state = $2FCollider.StateType.IsTest;
      this._doCollide(t, e, i.onStay);
    } else {
      t.contactMap.set(e.colliderId, {
        other: e,
        state: $2FCollider.StateType.IsTest
      });
      this._doCollide(t, e, i.onEnter);
    }
  };
  _ctor.prototype.updateCollideExit = function (e, t) {
    var o;
    var n;
    (null === (o = e.contactMap) || undefined === o ? undefined : o.delete(t.colliderId)) && this._doCollide(e, t, i.onExit);
    (null === (n = t.contactMap) || undefined === n ? undefined : n.delete(e.colliderId)) && this._doCollide(t, e, i.onExit);
  };
  _ctor.prototype._doCollide = function (e, t, o) {
    var i;
    (null === (i = e.comp) || undefined === i ? undefined : i[o]) && e.comp[o](t, e);
  };
  Object.defineProperty(_ctor.prototype, "enableDebugDraw", {
    get: function () {
      return this._enableDebugDraw;
    },
    set: function (e) {
      if (e && !this._enableDebugDraw) {
        this._checkDebugDrawValid();
        this._debugDrawer.node.active = true;
      } else if (!e && this._enableDebugDraw) {
        this._debugDrawer.clear(true), this._debugDrawer.node.active = false;
      }
      this._enableDebugDraw = e;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "enableQuadTreeDraw", {
    get: function () {
      return this._enableQuadTreeDraw;
    },
    set: function (e) {
      if (e && !this._enableQuadTreeDraw) {
        this._checkDebugDrawValid();
        this._debugDrawer.node.active = true;
      } else if (!e && this._enableQuadTreeDraw) {
        this._debugDrawer.clear(true), this._debugDrawer.node.active = false;
      }
      this._enableQuadTreeDraw = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype._checkDebugDrawValid = function () {
    if (!this._debugDrawer || !this._debugDrawer.isValid) {
      var e = new cc.Node("FCOLLISION_MANAGER_DEBUG_DRAW");
      e.zIndex = cc.macro.MAX_ZINDEX;
      cc.game.addPersistRootNode(e);
      this._debugDrawer = e.addComponent(cc.Graphics);
      this._debugDrawer.lineWidth = 16;
    }
  };
  _ctor.prototype.drawColliders = function () {
    if (this._enableDebugDraw) {
      this._checkDebugDrawValid();
      var e = this._debugDrawer;
      e.clear();
      var t = this._colliders;
      var o = 0;
      for (var i = t.length; o < i; o++) {
        var r = t[o];
        if (r.isActive) {
          e.strokeColor = cc.Color.RED;
          if (r.type === $2FCollider.ColliderType.Box || r.type === $2FCollider.ColliderType.Polygon) {
            var a = r.worldPoints;
            if (a.length > 0) {
              cc.Vec2.set(s, a[0].x, a[0].y);
              e.moveTo(s.x, s.y);
              for (var c = 1; c < a.length; c++) {
                cc.Vec2.set(s, a[c].x, a[c].y);
                e.lineTo(s.x, s.y);
              }
              e.close();
              e.stroke();
            }
          } else if (r.type === $2FCollider.ColliderType.Circle) {
            e.circle(r.worldPosition.x, r.worldPosition.y, r.worldRadius);
            e.stroke();
          }
        }
      }
    }
  };
  _ctor.prototype.drawQuadTree = function () {
    if (this._enableQuadTreeDraw) {
      this._checkDebugDrawValid();
      var e = this._debugDrawer;
      this._enableDebugDraw || e.clear(true);
      this._tree.render(e);
    }
  };
  _ctor._instance = null;
  return _ctor;
}();
exports.default = def_FColliderManager;