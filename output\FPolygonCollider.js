/**
 * FPolygonCollider
 * 组件类 - 从编译后的JS反编译生成
 */

const $2FCollider = require('FCollider');
const $2Intersection = require('Intersection');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2FCollider.default,

    properties: {
        _points: {
            type: [cc.Vec2],
            default: []
        },
        type: {
            get() {
                return $2FCollider.ColliderType.Polygon;,
            override: true
        528,
            visible: false
        },
        points: {
            get() {
                return this._points;
            },
            set(value) {
                this._points = e;
            },
            visible: false
        }
    },

    ctor: function () {
        this.worldPoints = [cc.v2(-100, 0), cc.v2(0, 50), cc.v2(100, 0)]
        this.worldEdge = []
        this._points = [cc.v2(-50, -50), cc.v2(50, -50), cc.v2(50, 50), cc.v2(-50, 50)]
    },

    // use this for initialization
    onLoad: function () {
    },

    initCollider: function () {
        // this._super(); // 已注释：父类无此方法
        this.isConvex = !$2Intersection.Intersection.isConcavePolygon(this.points);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
