var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RBadgeController = undefined;
var $2CallID = require("CallID");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var $2RBadgeModel = require("RBadgeModel");
var $2RBadgePoint = require("RBadgePoint");
var exp_RBadgeController = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t.look = new Set();
    t.setup($2RBadgeModel.default.instance);
    t.changeListener(true);
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {
    this._model.reset();
  };
  Object.defineProperty(_ctor.prototype, "classname", {
    get: function () {
      return "RBadgeController";
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerAllProtocol = function () {};
  Object.defineProperty(_ctor.prototype, "BagMode", {
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Login_Finish, this.onLogin_Finish, this, -300);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Badge_Set, this.onBadge_Set, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Badge_Add, this.onBadge_Add, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Item_GoodsChange, this.onItem_GoodsChange, this);
    $2Notifier.Notifier.changeCall(e, $2CallID.CallID.Badge_Get, this.getBadge, this);
  };
  _ctor.prototype.onLogin_Finish = function () {
    this.mode.onLogin_Finish();
    this.resetPoint();
  };
  _ctor.prototype.onBadge_Set = function (e, t, o) {
    this.mode.setPoint(e, t, o);
  };
  _ctor.prototype.onBadge_Add = function (e, t, o) {
    var i = this;
    undefined === o && (o = 0);
    var n = e.getComByChild($2RBadgePoint.default);
    if (n) {
      n.resetPoint({
        myKey: t,
        myID: o
      });
    } else if ($2RBadgeModel.RBadge.Key[t]) {
      var r = e.uuid;
      if (!this.look.has(r)) {
        this.look.add(r);
        $2Manager.Manager.loader.loadPrefab("ui/common/RBadgePoint").then(function (a) {
          if (e.isValid) {
            i.look.delete(r);
            (n = a.getComponent($2RBadgePoint.default)).resetPoint({
              myKey: t,
              myID: o
            });
            a.setAttribute({
              parent: e,
              x: e.width / 2 - 10,
              y: e.height / 2 - 10
            });
          } else {
            a.destroy();
          }
        });
      }
    }
  };
  _ctor.prototype.getBadge = function (e, t) {
    undefined === t && (t = 0);
    return this.mode.getItem(e, t);
  };
  _ctor.prototype.onItem_GoodsChange = function () {
    this.resetPoint();
  };
  _ctor.prototype.resetPoint = function () {
    $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, $2RBadgeModel.RBadge.Key.Shop_FreeCoin, $2Manager.Manager.vo.userVo.dailyData.freeCoin);
    $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, $2RBadgeModel.RBadge.Key.Shop_FreeDiamond, $2Manager.Manager.vo.userVo.dailyData.freeDiamond);
  };
  return _ctor;
}($2MVC.MVC.MController);
exports.RBadgeController = exp_RBadgeController;