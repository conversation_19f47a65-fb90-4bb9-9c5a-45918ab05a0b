/**
 * MonsterTidal
 * 组件类 - 从编译后的JS反编译生成
 */

const $2StateMachine = require('StateMachine');
const $2Game = require('Game');
const $2SkillManager = require('SkillManager');
const $2MonsterState = require('MonsterState');
const $2MonsterTidalState = require('MonsterTidalState');
const $2Monster = require('Monster');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var f = cc._decorator.ccclass;

exports.default = cc.Class({
    extends: $2Monster.Monster,

    properties: {
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        var t;
        var o = this;
        this._super();
        null === (t = this.monCfg.skill) || undefined === t || t.forEach(function (e) {
        return o.addSkill(e);
        });
    },

    registerState: function () {
        if (!this._stateMachine) {
        this._stateMachine = new $2StateMachine.State.Machine(this);
        this._stateMachine.addState(new $2MonsterState.MonsterState.IdleState(this, false));
        this._stateMachine.addState(new $2MonsterTidalState.MonsterTidalState.AppearState(this, false));
        this._stateMachine.addState(new $2MonsterTidalState.MonsterTidalState.MoveState(this, false));
        this._stateMachine.addState(new $2MonsterState.MonsterState.AttackState(this, true));
        this._stateMachine.addState(new $2MonsterState.MonsterState.DeadState(this, false));
        this._stateMachine.addState(new $2MonsterState.MonsterState.BeHit(this, false));
        this._stateMachine.registerGlobalState(new $2MonsterState.MonsterState.GlobalState(this));
        }
    },

    isOffScreen: function () {
        return false;
    },

    isInAttackRange: function () {
        if (!this.steering.targetAgent1) {
        return -1;
        }
        var e = Math.abs(this.node.y - this.steering.targetAgent1.position.y);
        var t = this.property.cut.atkArea + this.steering.targetAgent1.radius;
        if (e > t) {
        return -1;
        } else {
        if (e < .1 * t) {
        return 1;
        } else {
        return 0;
        }
        }
    },

    toDead: function () {
        var e;
        if (!this._stateMachine.isInState($2StateMachine.State.Type.DEAD)) {
        this._stateMachine.changeState($2StateMachine.State.Type.DEAD);
        (null === (e = this.skillMgr) || undefined === e ? undefined : e.hasMainID(2060)) && this.skillMgr.use(2060);
        }
    },

    onSkill: function (e) {
        if ($2SkillManager.Skill.SelfDestructSkill.includes(e.id)) {
        this.curHp -= 990;
        this.toDead();
        }
    },

    setPosition: function (t) {
        t.y = Math.max(t.y, this.game.scenceSize[2]);
        this._super(t);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
