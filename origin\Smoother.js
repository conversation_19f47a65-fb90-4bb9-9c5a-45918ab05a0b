Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Smoother = undefined;
var i = cc.v2();
var exp_Smoother = function () {
  function _ctor(e) {
    this._list = [];
    this._nextUpdateSlot = 0;
    this._maxSampleSize = 0;
    this._nextUpdateSlot = 0;
    this._maxSampleSize = e;
  }
  _ctor.prototype.onUpdate = function (e) {
    var t = this._list[this._nextUpdateSlot];
    if (t) {
      t.set(e);
    } else {
      t = cc.v2(e);
      this._list[this._nextUpdateSlot] = t;
    }
    this._nextUpdateSlot++;
    this._nextUpdateSlot >= this._maxSampleSize && (this._nextUpdateSlot = 0);
    i.set(cc.Vec2.ZERO);
    var o = 0;
    for (var n = this._list.length; o < n; o++) {
      cc.Vec2.add(i, this._list[o], i);
    }
    i.divSelf(this._list.length);
    return i;
  };
  return _ctor;
}();
exports.Smoother = exp_Smoother;