/**
 * M20Prop_Equip
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2GameatrCfg = require('GameatrCfg');
const $2SoundCfg = require('SoundCfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2UIManager = require('UIManager');
const $2GameUtil = require('GameUtil');
const $2AlertManager = require('AlertManager');
const $2SkillManager = require('SkillManager');
const $2PropertyVo = require('PropertyVo');
const $2ItemModel = require('ItemModel');
const $2MBackpackHero = require('MBackpackHero');
const $2M20Prop = require('M20Prop');
const $2M20Prop_Gemstone = require('M20Prop_Gemstone');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__spreadArrays = __spreadArrays;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: $2M20Prop.default,

    properties: {
        isWeapon: {
            get() {
                return $2MBackpackHero.MBPack.equipWeapon.includes(this.roleID);
            },
            visible: false
        },
        isArmor: {
            get() {
                return $2MBackpackHero.MBPack.equipArmor.includes(this.roleID);
            },
            visible: false
        },
        isGemMax: {
            get() {
                return this.gemStones.length >= this.mergeCfg.lv;
            },
            visible: false
        },
        prorSkillId: {
            get() {
                return (this.mergeCfg.skill || this.equipCfg.skill || [])[0];
            },
            visible: false
        },
        figureData: {
            get() {
                return {
                Atk: this.firstSkill ? this.property.cut.atk / this.firstSkill.cutVo.cd : 0,
                Hp: this.firstSkill ? this.extraProperty.heal / this.firstSkill.cutVo.cd : 0,
                Def: this.firstSkill ? this.extraProperty.armor / this.firstSkill.cutVo.cd : 0
                };
            },
            visible: false
        },
        figureNextData: {
            get() {
                if (!this.nextLv) {
                return this.figureData;
                }
                var e = this.getPropertyCfg(this.equipCfg, this.nextLv);
                var t = e.property;
                var o = e.extraProperty;
                return {
                Atk: this.firstSkill ? t.atk / this.firstSkill.cutVo.cd : 0,
                Hp: this.firstSkill ? o.heal / this.firstSkill.cutVo.cd : 0,
                Def: this.firstSkill ? o.armor / this.firstSkill.cutVo.cd : 0
                };
            },
            visible: false
        },
        nextLv: {
            get() {
                return $2Cfg.Cfg.EquipMergeLv.find({
                equipId: this.mergeCfg.equipId,
                lv: this.mergeCfg.lv + 1
                });
            },
            visible: false
        },
        isMaxLv: {
            get() {
                return !this.nextLv;
            },
            visible: false
        },
        saveData: {
            get() {
                var e;
                var t;
                var o;
                return {
                id: this.mergeCfg.id,
                pos: this.position,
                curHp: this.curHp,
                isUnlock: this.isUnlock,
                gemStones: this.gemStones.map(function (e) {
                return e.gemData;
                }),
                blockType: this.blockType,
                buffList: (null === (e = this.buffMgr) || undefined === e ? undefined : e.bufflist.map(function (e) {
                return e.saveData;
                })) || [],
                skillList: (null === (t = this.skillMgr) || undefined === t ? undefined : t.skills.map(function (e) {
                return e.id;
                })) || [],
                property: {
                extra: null === (o = this.property) || undefined === o ? undefined : o.extra
                },
                extraProperty: this.extraProperty
                };
            },
            visible: false
        }
    },

    ctor: function () {
        this.gemStones = []
        this.getExhibitMsg = {
            [$2GameatrCfg.GameatrDefine.cdequip]: function () {
                var e;
                return ((null === (e = this.firstSkill) || undefined === e ? undefined : e.cutVo.cd.getFixed()) || 0) + "s";
                },
            [$2GameatrCfg.GameatrDefine.atkequip]: function () {
                var e;
                return (this.property.cut.atk * ((null === (e = this.firstSkill) || undefined === e ? undefined : e.cutVo.dam) || 0)).getFixed();
                },
            [$2GameatrCfg.GameatrDefine.hpequip]: function () {
                return this.property.cut.hp.getFixed();
                },
            [$2GameatrCfg.GameatrDefine.rangeequip]: function () {
                var e;
                return (null === (e = this.firstSkill) || undefined === e ? undefined : e.dis.getFixed(0)) || 0;
                },
            [$2GameatrCfg.GameatrDefine.healequip]: function () {
                return this.extraProperty.heal.getFixed();
                },
            [$2GameatrCfg.GameatrDefine.shieldequip]: function () {
                return this.extraProperty.armor.getFixed();
                },
            [$2GameatrCfg.GameatrDefine.silvercoinequip]: function () {
                return this.extraProperty.sliverCoin.getFixed();
                },
            [$2GameatrCfg.GameatrDefine.healeratequip]: function () {
                return Math.round(100 * this.extraProperty.healRate) + "%";
                }
        }
    },

    // use this for initialization
    onLoad: function () {
    },

    resetGemStones: function () {
        var e = this;
        var t = this.node.getChildByName("gemStones");
        t.setActive(this.propType == $2MBackpackHero.MBPack.PropType.MergeEquipment);
        t.setPosition(cc.v2(.5 * -this.blockSize.width + 10, .5 * -this.blockSize.height + 10));
        t.getComponent(cc.Layout).setAttribute({
        type: [21, 31].includes(this.blockType) ? cc.Layout.Type.HORIZONTAL : cc.Layout.Type.VERTICAL
        });
        t.children.forEach(function (t, o) {
        var i = e.gemStones[o];
        t.setAttribute({
        x: 0,
        y: 0,
        active: o < e.mergeCfg.lv && !!i
        });
        i && $2Manager.Manager.loader.loadSpriteToSprit(i ? i.mergeCfg.res : "img/ModeBackpackHero/bg_talk", t.getComponent(cc.Sprite), e.packView.node);
        });
    },

    resetState: function (t) {
        this._super(t);
        this.resetGemStones();
    },

    onFight_RoundState: function () {
        if (this.game.bronMonsterMgr.cutStatus == $2MBackpackHero.MBPack.RoundStatus.SELECTEQUIP) {
        this.clearSpecialBuff();
        } else {
        this.game.bronMonsterMgr.cutStatus == $2MBackpackHero.MBPack.RoundStatus.BATTLE && this.checkBuffEffect(null, true);
        }
    },

    onTouchEnd: function (t) {
        this._super(t);
        this.packView.operationID == this.ID && this._frameNum < 6 && this.propType == $2MBackpackHero.MBPack.PropType.MergeEquipment && this.isUnlock && $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_EquipMergeInfo", $2MVC.MVC.openArgs().setParam(this));
    },

    onTouchStart: function (t) {
        var i = this;
        if (this.isCanClick) {
        if (this.propType == $2MBackpackHero.MBPack.PropType.Block) {
        $2GameUtil.GameUtil.deleteArrItem(this.packView.prepareBlockList, this);
        } else {
        this.propType == $2MBackpackHero.MBPack.PropType.MergeEquipment && this.packView.propList.forEach(function (e) {
        if (e instanceof o) {
        if (e.mergeCfg.id != i.mergeCfg.id) {
        return;
        }
        if (e == i) {
        return;
        }
        if (e.isMaxLv) {
        return;
        }
        cc.tween(e.node).stopLast().sequence(cc.tween().to(.2, {
        scale: 1.1
        }, {
        easing: "sineInOut"
        }), cc.tween().to(.2, {
        scale: 1
        }, {
        easing: "sineInOut"
        })).repeatForever().start();
        }
        });
        }
        this._super(t);
        }
    },

    set: function (t, o) {
        var i;
        var n;
        var r = this;
        this._super(t, o);
        this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
        $2Manager.Manager.loader.loadSpriteToSprit(this.mergeCfg.res, this.img, this.node.parent).then(function (e) {
        r.resetState(e.getRect());
        });
        this.gemStones.length = 0;
        this.equipCfg = $2Cfg.Cfg.EquipLv.find({
        equipId: this.roleCfg.id,
        lv: (null === (i = this.mode.userEquipPack.getItem(t.equipId)) || undefined === i ? undefined : i.lv) || 1
        });
        if (this.propType == $2MBackpackHero.MBPack.PropType.MergeEquipment && (this.setProperty(), this.initHp(), null === (n = t.buff) || undefined === n || n.forEach(function (e) {
        return r.addBuff(e);
        }), this.position.y < -$2GameUtil.GameUtil.getDesignSize.height / 2 + 300)) {
        var a = [null, "bones/ui/fx_promotel_blue", "bones/ui/fx_promotel_yellow", "bones/ui/fx_promotel_purple"][t.lv - 1];
        a && $2Manager.Manager.loader.loadSpineNode(a, {
        nodeAttr: {
        parent: this.node,
        position: cc.Vec2.ZERO
        },
        spAttr: {
        animation: "animation",
        loop: true,
        type: $2GameSeting.GameSeting.TweenType.Not
        }
        }, this.game.gameNode).then(function (e) {
        r.promotelEffect = e.node;
        });
        }
        this.isMerge = $2Cfg.Cfg.EquipMergeLv.filter({
        lv: t.lv,
        equipId: t.equipId
        }).findIndex(function (e) {
        return e.id == t.id;
        }) > 0;
    },

    getPropertyCfg: function (e, t) {
        var o = {
        atk: 0,
        hp: 0,
        atkArea: 1,
        crit: 0
        };
        for (var i in e) {
        null != o[i] && (o[i] = e[i]);
        }
        for (var i in t) {
        null != o[i] && (o[i] = Math.ceil(o[i] * (t[i] || 1)));
        }
        var n = {
        armor: 0,
        armorRate: 0,
        heal: 0,
        healRate: 0,
        sliverCoin: 0
        };
        for (var i in n) {
        e[i] && (n[i] = e[i]);
        t[i] && (n[i] = n[i] * (t[i] || 1));
        ["armor", "heal", "sliverCoin"].includes(i) && (n[i] = Math.ceil(n[i]));
        }
        return {
        property: o,
        extraProperty: n
        };
    },

    setProperty: function () {
        var e;
        var t = this;
        this.property || (this.property = new $2PropertyVo.Property.Vo(this));
        var o = this.getPropertyCfg(this.equipCfg, this.mergeCfg);
        var i = o.property;
        var n = o.extraProperty;
        this.extraProperty = n;
        this.property.set(i);
        this.skillMgr.clearAll();
        null === (e = this.mergeCfg.skill || this.equipCfg.skill) || undefined === e || e.forEach(function (e) {
        var o = t.skillMgr.add(e, false);
        t.mergeCfg.bulletId && (o.skillCfg.bulletId = o.cutVo.bulletId = t.mergeCfg.bulletId);
        });
        this.isMerge = $2Cfg.Cfg.EquipMergeLv.filter({
        lv: this.mergeCfg.lv,
        equipId: this.mergeCfg.equipId
        }).findIndex(function (e) {
        return e.id == t.mergeCfg.id;
        }) > 0;
    },

    upgrade: function (e) {
        var t;
        var o;
        var i = this;
        var n = true;
        if (!e) {
        var r = $2Cfg.Cfg.EquipMergeLv.filter({
        equipId: this.mergeCfg.equipId,
        lv: this.mergeCfg.lv + 1
        });
        var a = $2Manager.Manager.vo.switchVo.equip3SelectShow[this.roleCfg.rarity];
        1 == $2Manager.Manager.vo.switchVo.equip3SelectShow[0] && (n = this.equipCfg.lv >= a);
        if (1 == r.length) {
        e || (e = r[0]);
        } else if (r.length >= 2) {
        if (n) {
        return void $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Fight_Equip5View", $2MVC.MVC.openArgs().setParam({
        prop: this,
        list: r
        }));
        }
        e || (e = r[0]);
        }
        }
        var s = this.property.cut.hp;
        this.mergeCfg = e;
        this.setProperty();
        this.curHp += this.property.cut.hp - s;
        null === (t = e.buff) || undefined === t || t.forEach(function (e) {
        return i.addBuff(e);
        });
        $2Manager.Manager.loader.loadSpriteToSprit(e.res, this.img, this.node.parent).then(function (e) {
        i.resetState(e.getRect());
        });
        cc.tween(this.node).to(.1, {
        scale: 1.2
        }).to(.2, {
        scale: 1
        }).start();
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_OnSkillChange);
        $2Manager.Manager.loader.loadSpineNode(["bones/ui/fx_compound_blue", "bones/ui/fx_compound_yellow", "bones/ui/fx_compound_purple"][e.lv - 2], {
        nodeAttr: {
        parent: this.node,
        position: cc.Vec2.ZERO
        },
        spAttr: {
        defaultAnimation: "animation",
        loop: false
        },
        delayRemove: 1
        }, this.game.gameNode);
        this.removePromotelEffect();
        $2Manager.Manager.audio.playAudio([$2SoundCfg.SoundDefine.ui_mergelv2, $2SoundCfg.SoundDefine.ui_mergelv3, $2SoundCfg.SoundDefine.ui_mergelv4][e.lv - 2]);
        this.mode.canMergeEquip.includes(this.roleCfg.id) && 3 == this.mergeCfg.lv && n && this.showTips(cc.js.formatStr("<outline color=black width=2>合成至<color=#00ff00>4级</c>解锁进阶<color=#0fffff>%s</color></outline>", this.roleCfg.roleName));
        null === (o = this.buffMgr) || undefined === o || o.clearBuffByID(5e4);
    },

    downgrading: function () {
        var e = this;
        var t = $2Cfg.Cfg.EquipMergeLv.find({
        equipId: this.mergeCfg.equipId,
        lv: this.mergeCfg.lv - 1
        });
        if (t) {
        var o = this.property.cut.hp;
        this.mergeCfg = t;
        this.setProperty();
        this.curHp += this.property.cut.hp - o;
        $2Manager.Manager.loader.loadSpriteToSprit(t.res, this.img, this.node.parent).then(function (t) {
        e.resetState(t.getRect());
        });
        cc.tween(this.node).to(.1, {
        scale: 1.2
        }).to(.2, {
        scale: 1
        }).start();
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_OnSkillChange);
        $2Manager.Manager.loader.loadSpineNode("bones/ui/fx_down", {
        nodeAttr: {
        parent: this.node,
        position: cc.Vec2.ZERO
        },
        spAttr: {
        animation: "animation",
        type: $2GameSeting.GameSeting.TweenType.Not,
        loop: false
        },
        delayRemove: 1
        }, this.game.gameNode);
        }
    },

    update: function () {
        var e;
        var t = this;
        this.isValid && this.propType == $2MBackpackHero.MBPack.PropType.Block && (null === (e = this.packView) || undefined === e ? undefined : e.prepareBlockList.includes(this)) && this.curPoint.forEach(function (e) {
        t.packView.checkMap(e, 2).forEach(function () {
        t.packView.setInSpareBox(t);
        });
        });
    },

    excute: function () {
        var e = this;
        switch (this.equipCfg.equipId) {
        case 1e4:
        cc.tween(this.node).delay(.3).shake({
        lv: 2,
        loopNum: 1,
        scaleTime: .5
        }).call(function () {
        $2ItemModel.default.instance.showItemTips({
        itemID: $2CurrencyConfigCfg.CurrencyConfigDefine.silver,
        timeScale: 2,
        nodeAttr: {
        position: e.node.wordPos,
        scale: .8
        },
        call: function () {
        var t;
        if (e.game) {
        var o = e.extraProperty.sliverCoin;
        null === (t = e.buffMgr) || undefined === t || t.use(111, false, function (e) {
        var t = [];
        e.cutVo.value[0].forEach(function (o, i) {
        return t.push({
        id: o,
        w: e.otherValue[i]
        });
        });
        var i = $2GameUtil.GameUtil.weightGetValue(t).id;
        o += i * e.buffLayer;
        });
        e.game.knapsackMgr.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.silver, o);
        }
        }
        });
        }).start();
        $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.ui_collectsilverCoin);
        }
    },

    checkBuffEffect: function (e, t) {
        var o = this;
        undefined === t && (t = false);
        var i = new Set();
        1001 == this.equipCfg.equipId && this.role.buffMgr.getAttrByIDs([10200, 10203], function (n) {
        var r = {
        id: 102e9,
        name: "手套相邻冷兵器",
        desc: "手套相邻冷兵器-CD光环属性",
        type: 3,
        time: -1,
        attr: [$2GameatrCfg.GameatrDefine.cd],
        value: [[n.getor($2GameatrCfg.GameatrDefine.bagGridCd, 0)]],
        ishideUI: 1,
        isOverlay: 10
        };
        o.getAroundEquip(e).forEach(function (e) {
        if ([1e3, 1002, 1004, 1007, 1008, 1013, 1017, 1019, 1021].includes(e.equipCfg.equipId)) {
        i.add(e);
        t && e.addBuffByData(r);
        }
        });
        });
        1006 == this.equipCfg.equipId && this.role.buffMgr.getAttrByIDs([11200, 11202], function (n) {
        var r = {
        id: 112e9,
        name: "背心相邻的枪械",
        desc: "背心相邻的枪械-伤害光环属性",
        type: 3,
        time: -1,
        attr: [$2GameatrCfg.GameatrDefine.skilldam],
        value: [[n.getor($2GameatrCfg.GameatrDefine.bagGridDam, 0)]],
        ishideUI: 1,
        isOverlay: 10
        };
        o.getAroundEquip(e).forEach(function (e) {
        if ([1005, 1010, 1012].includes(e.equipCfg.equipId)) {
        i.add(e);
        t && e.addBuffByData(r);
        }
        });
        });
        if (this.buffMgr) {
        var n = this.buffMgr.isHasID([10500]) ? 10 : 0;
        this.buffMgr.use(15e4, false, function (r) {
        var a = {
        id: 1e7 * r.cutVo.id,
        name: "戒指相邻的武器",
        desc: "戒指相邻的武器-暴击率光环属性",
        type: 1,
        time: -1,
        attr: [$2GameatrCfg.GameatrDefine.cirtrate],
        value: [[r.attrMap.getor($2GameatrCfg.GameatrDefine.bagGridCritRate, 0)]],
        ishideUI: 1,
        isOverlay: 1 + n
        };
        o.getAroundEquip(e).forEach(function (e) {
        if (e.isWeapon) {
        i.add(e);
        t && e.addBuffByData(a);
        }
        });
        });
        this.buffMgr.use(15001, false, function (r) {
        var a = {
        id: 1e7 * r.cutVo.id,
        name: "戒指相邻的武器",
        desc: "戒指相邻的武器-伤害光环属性",
        type: 3,
        time: -1,
        attr: [$2GameatrCfg.GameatrDefine.skilldam],
        value: [[r.attrMap.getor($2GameatrCfg.GameatrDefine.bagGridDam, 0)]],
        ishideUI: 1,
        isOverlay: 1 + n
        };
        o.getAroundEquip(e).forEach(function (e) {
        if (e.isWeapon) {
        i.add(e);
        t && e.addBuffByData(a);
        }
        });
        });
        this.buffMgr.use(15002, false, function (r) {
        var a = {
        id: 1e7 * r.cutVo.id,
        name: "戒指相邻的武器",
        desc: "戒指相邻的武器-暴击伤害光环属性",
        type: 1,
        time: -1,
        attr: [$2GameatrCfg.GameatrDefine.cirtdam],
        value: [[r.attrMap.getor($2GameatrCfg.GameatrDefine.bagGridCritDam, 0)]],
        ishideUI: 1,
        isOverlay: 1 + n
        };
        o.getAroundEquip(e).forEach(function (e) {
        if (e.isWeapon) {
        i.add(e);
        t && e.addBuffByData(a);
        }
        });
        });
        }
        1019 == this.equipCfg.equipId && this.role.buffMgr.use(13801, false, function (n) {
        var r = n.cutVo;
        var a = {
        id: 1380100,
        name: "包中长矛如果相邻，相邻的长矛攻击时有概率二连发",
        type: 3,
        time: -1,
        ishideUI: 1,
        attr: r.attr,
        value: r.value,
        weight: r.weight,
        className: "Buff_MaoMaoXL"
        };
        var s = false;
        o.getAroundEquip(e).forEach(function (e) {
        if ([1019].includes(e.equipCfg.equipId)) {
        i.add(e);
        s = true;
        t && e.addBuffByData(a);
        }
        });
        s && i.add(o);
        s && t && o.addBuffByData(a);
        });
        return i;
    },

    checkReaction: function (e, t) {
        var i;
        undefined === t && (t = false);
        if (this != e && e.isUnlock && this.propType == $2MBackpackHero.MBPack.PropType.MergeEquipment) {
        var n = cc.Vec2.squaredDistance(this.position, e.position);
        if (n > Math.pow($2MBackpackHero.MBPack.BlockSize, 1.8)) {
        return null;
        }
        if (e instanceof o) {
        if (this.mergeCfg.id == e.mergeCfg.id && e.nextLv) {
        return {
        type: $2MBackpackHero.MBPack.ReactionType.Upgrade,
        item: e,
        d: n
        };
        }
        if (null === (i = e.buffMgr) || undefined === i ? undefined : i.isHasID([8e3])) {
        return {
        type: $2MBackpackHero.MBPack.ReactionType.SWALLOW,
        item: e,
        d: n
        };
        }
        } else if (e instanceof $2M20Prop_Gemstone.default && e.roleID == $2MBackpackHero.MBPack.ReactionType.Sacrifice) {
        return {
        type: $2MBackpackHero.MBPack.ReactionType.Sacrifice,
        item: e,
        d: n
        };
        }
        }
    },

    setReaction: function (e) {
        var t;
        var i = this;
        var n = e.item;
        switch (e.type) {
        case $2MBackpackHero.MBPack.ReactionType.SWALLOW:
        if (n instanceof o) {
        var r = n.buffMgr.get(8e3).tempData.SwallowIDs || [];
        if (r.includes(this.mergeCfg.id)) {
        this.packView.setInSpareBox(this);
        return $2AlertManager.AlertManager.showNormalTips("不能吞噬2个相同4级进阶装备");
        }
        if (r.length >= 3) {
        this.packView.setInSpareBox(this);
        return $2AlertManager.AlertManager.showNormalTips("吞噬达到上限");
        }
        if (!this.isMerge) {
        this.packView.setInSpareBox(this);
        return $2AlertManager.AlertManager.showNormalTips("只能吞噬4级进阶装备");
        }
        null === (t = n.buffMgr) || undefined === t || t.use(8e3, false, function (e) {
        e.tempData.SwallowIDs || (e.tempData.SwallowIDs = []);
        e.tempData.SwallowIDs.push(i.mergeCfg.id);
        });
        n.cloneBy(this);
        $2Manager.Manager.loader.loadSpineNode("bones/ui/fx_compound_purple", {
        nodeAttr: {
        parent: this.packView.topEffectNode,
        position: this.position
        },
        spAttr: {
        animation: "animation",
        type: $2GameSeting.GameSeting.TweenType.Not,
        loop: false
        },
        delayRemove: 1
        }, this.game.gameNode);
        this.unuse();
        }
        break;
        case $2MBackpackHero.MBPack.ReactionType.Upgrade:
        if (n instanceof o) {
        var c = n.gemStones.length + this.gemStones.length - n.nextLv.lv;
        if (c > 0) {
        $2AlertManager.AlertManager.showSelectAlert({
        title: "提示",
        desc: cc.js.formatStr("合成后的装备宝石数量超过上限，会随机销毁%d个宝石，确定合成吗?", c),
        confirmText: "确认",
        confirm: function () {
        $2GameUtil.GameUtil.getRandomInArray(cc__spreadArrays(n.gemStones, i.gemStones), c).forEach(function (e) {
        return e.unuse();
        });
        for (var e = n.gemStones.length - 1; e >= 0; e--) {
        !n.gemStones[e].isActive && n.gemStones.splice(e, 1);
        }
        for (e = i.gemStones.length - 1; e >= 0; e--) {
        !i.gemStones[e].isActive && i.gemStones.splice(e, 1);
        }
        i.setCompound(n, i);
        },
        cancel: function () {
        i.packView.setInSpareBox(i);
        }
        });
        } else {
        this.setCompound(n, this);
        }
        }
        break;
        case $2MBackpackHero.MBPack.ReactionType.Sacrifice:
        n.setReaction({
        type: e.type,
        item: this,
        d: e.d
        });
        }
        this.game.saveRecordVo();
    },

    setMosaic: function (e) {
        var t;
        var o = this;
        e.isValid && (this.isGemMax || (this.gemStones.push(e), e.node.setAttribute({
        parent: this.node,
        active: false
        }), this.resetGemStones(), null === (t = e.mergeCfg.buff) || undefined === t || t.forEach(function (e) {
        o.addBuff(e);
        })));
    },

    setCompound: function (e, t) {
        var o;
        var i = this;
        cc.tween(e.node).stopLast().to(.1, {
        angle: 0
        }).start();
        e.upgrade();
        null === (o = t.gemStones) || undefined === o || o.forEach(function (t) {
        e.setMosaic(t);
        });
        this.role.buffMgr.use(3003, false, function (t) {
        t.isWeight && i.packView.scheduleOnce(function () {
        e.upgrade();
        }, .2);
        });
        this.role.buffMgr.use(3080, false, function () {
        var t = e.mergeCfg.lv;
        i.game.knapsackMgr.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.silver, t);
        $2ItemModel.default.instance.showItemTips({
        itemID: $2CurrencyConfigCfg.CurrencyConfigDefine.silver,
        timeScale: 2,
        imgNum: t,
        nodeAttr: {
        position: e.node.wordPos,
        scale: .8
        }
        });
        });
        3 == $2Manager.Manager.vo.userVo.guideIndex && $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
        if (4 == $2Manager.Manager.vo.userVo.guideIndex && this.packView.SpareBox.childrenCount <= 0) {
        var n = this.packView.propList.arr.find(function (e) {
        return 1e4 == e.roleID;
        });
        n && $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
        targetNode: n.node,
        enableclick: true,
        blockevent: true
        });
        }
        t.unuse();
    },

    readData: function (t) {
        var o = this;
        this._super(t);
        this.propType == $2MBackpackHero.MBPack.PropType.MergeEquipment && (this.curHp = t.curHp);
        t.gemStones.forEach(function (e) {
        var t = $2Cfg.Cfg.EquipMergeLv.get(e.id);
        o.packView.newProp(t).then(function (t) {
        t.node.setAttribute({
        parent: o.packView.MoveBox,
        position: cc.v2()
        });
        t.readData({
        gemData: e
        });
        o.setMosaic(t);
        t.changeToch(false);
        t.node.setActive(false);
        });
        });
        t.buffList.forEach(function (e) {
        if (![50020, 50040, 50060, 50080].includes(e.ID)) {
        var t = o.addBuffByData(e.cfg);
        null == t || t.setAttribute({
        curBuffTime: e.curBuffTime,
        buffLayer: e.buffLayer,
        tempData: e.tempData
        });
        }
        });
        t.skillList.forEach(function (e) {
        var t;
        null !== (t = o.skillMgr) && undefined !== t && t.skillIDs.includes(e) || o.addSkill(e);
        });
        if (t.property.extra && this.property) {
        for (var i in t.property.extra) {
        this.property.extra[i] = t.property.extra[i];
        }
        }
        this.extraProperty = t.extraProperty;
        this.updateProperty();
        this.clearSpecialBuff();
    },

    clearSpecialBuff: function () {
        var e;
        var t = this;
        null === (e = this.buffMgr) || undefined === e || e.forEach(function (e) {
        var o;
        e.cutVo.id > 1e7 && (null === (o = t.buffMgr) || undefined === o || o.clearBuffByID(e.cutVo.id));
        });
    },

    cloneBy: function (e) {
        var t;
        var o;
        var i = this;
        for (var n in e.property.base) {
        this.property.extra[n] += e.property.base[n];
        }
        for (var n in e.extraProperty) {
        this.extraProperty[n] += e.extraProperty[n];
        }
        null === (t = e.skillMgr) || undefined === t || t.skills.forEach(function (e) {
        return i.addSkill(e.id);
        });
        null === (o = e.buffMgr) || undefined === o || o.bufflist.forEach(function (e) {
        i.addBuffByData(e.cutVo).setLayer(e.buffLayer);
        });
        this.updateProperty();
        this.curHp += e.curHp;
        this.resetState();
    },

    setMin: function () {
        var e;
        var t = this.blockList[0];
        this.packView.removeInPack(this);
        this.resetBlockType(1);
        t && this.packView.setInPack(this, [t]);
        this.resetState(null === (e = this.img.spriteFrame) || undefined === e ? undefined : e.getRect());
    },

    call: function () {
        var t;
        if (e.game) {
        var o = e.extraProperty.sliverCoin;
        null === (t = e.buffMgr) || undefined === t || t.use(111, false, function (e) {
        var t = [];
        e.cutVo.value[0].forEach(function (o, i) {
        return t.push({
        id: o,
        w: e.otherValue[i]
        });
        });
        var i = $2GameUtil.GameUtil.weightGetValue(t).id;
        o += i * e.buffLayer;
        });
        e.game.knapsackMgr.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.silver, o);
        }
    },

    confirm: function () {
        $2GameUtil.GameUtil.getRandomInArray(cc__spreadArrays(n.gemStones, i.gemStones), c).forEach(function (e) {
        return e.unuse();
        });
        for (var e = n.gemStones.length - 1; e >= 0; e--) {
        !n.gemStones[e].isActive && n.gemStones.splice(e, 1);
        }
        for (e = i.gemStones.length - 1; e >= 0; e--) {
        !i.gemStones[e].isActive && i.gemStones.splice(e, 1);
        }
        i.setCompound(n, i);
    },

    cancel: function () {
        i.packView.setInSpareBox(i);
    }
});
