var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.NotifyCaller = undefined;
var $2Log = require("Log");
var $2CallID = require("CallID");
var exp_NotifyCaller = function () {
  function _ctor() {
    this._calls = {};
  }
  _ctor.prototype.Register = function (e, t, o) {
    if (null == t) {
      $2Log.Log.error("[NotifyCaller].Register:" + e + " callback null");
      return false;
    }
    var i = this._calls[e];
    if (null != i) {
      $2Log.Log.error("[NotifyCaller].Register:" + e + " register repeat " + i + " " + t);
      return false;
    }
    this._calls[e] = {
      func: t,
      context: o
    };
  };
  _ctor.prototype.Unregister = function (e, t, o) {
    var i = this._calls[e];
    if (null == i || i.func !== t || i.context != o) {
      return $2Log.Log.warn("[NotifyCaller].Unregister can't find: " + $2CallID.CallID[e] + " callback " + i), false;
    } else {
      return delete this._calls[e], true;
    }
  };
  _ctor.prototype.Call = function (e) {
    var t;
    var o = [];
    for (var a = 1; a < arguments.length; a++) {
      o[a - 1] = arguments[a];
    }
    var s = this._calls[e];
    if (null != s) {
      return (t = s.func).call.apply(t, cc__spreadArrays([s.context], o));
    }
    $2Log.Log.error("[NotifyCaller].Call can't find: " + $2CallID.CallID[e]);
  };
  return _ctor;
}();
exports.NotifyCaller = exp_NotifyCaller;