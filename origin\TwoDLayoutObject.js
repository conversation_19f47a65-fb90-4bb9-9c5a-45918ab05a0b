var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TwoDLayoutObject = exports.LAYOUT_VERTICAL_TYPE = exports.LAYOUT_HORIZONTAL_TYPE = undefined;
var r;
var a;
var $2LayoutObject = require("LayoutObject");
(function (e) {
  e[e.LEFT = 0] = "LEFT";
  e[e.RIGHT = 1] = "RIGHT";
  e[e.CENTER = 2] = "CENTER";
})(r = exports.LAYOUT_HORIZONTAL_TYPE || (exports.LAYOUT_HORIZONTAL_TYPE = {}));
(function (e) {
  e[e.CENTER = 0] = "CENTER";
  e[e.TOP = 1] = "TOP";
  e[e.BOTTOM = 2] = "BOTTOM";
})(a = exports.LAYOUT_VERTICAL_TYPE || (exports.LAYOUT_VERTICAL_TYPE = {}));
var exp_TwoDLayoutObject = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.key_count = 0;
    t.item_size = cc.Vec2.ZERO;
    t.parent_size = cc.Vec2.ZERO;
    t.item_anchor_point = cc.Vec2.ZERO;
    t.count = 0;
    t.horizontal_layout_type = r.LEFT;
    t.vertical_layout_type = a.TOP;
    t.space = cc.Vec2.ZERO;
    t.left = 0;
    t.right = 0;
    t.top = 0;
    t.bottom = 0;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.getRowByIndex = function (e) {
    return Math.floor(e / this.key_count);
  };
  _ctor.prototype.getColumnByIndex = function (e) {
    return e % this.key_count;
  };
  _ctor.prototype.rows = function () {
    return Math.ceil(this.count / this.key_count);
  };
  _ctor.prototype.columns = function () {
    if (this.count <= this.key_count - 1) {
      return this.count;
    } else {
      return this.key_count;
    }
  };
  _ctor.prototype.getBoundingRect = function () {
    var e = this.rows();
    var t = this.columns();
    var o = e * this.item_size.y + (e - 1) * this.space.y + this.top + this.bottom;
    var i = t * this.item_size.x + (t - 1) * this.space.x + this.left + this.right;
    return new cc.Vec2(i, o);
  };
  _ctor.prototype.getIndex = function (e, t) {
    return e * this.key_count + t;
  };
  _ctor.prototype.doLayout = function (e) {
    var t = this.getRowByIndex(e);
    var o = this.getColumnByIndex(e);
    var i = 0;
    var n = 0;
    switch (this.horizontal_layout_type) {
      case r.LEFT:
        i = this.left + o * (this.item_size.x + this.space.x) + this.item_anchor_point.x * this.item_size.x;
        break;
      case r.CENTER:
        if (1 === this.key_count) {
          var s = (this.parent_size.x - this.item_size.x - this.left - this.right) / 2;
          i = this.left + s + this.item_anchor_point.x * this.item_size.x;
        } else {
          s = (this.parent_size.x - (this.left + this.right) - this.key_count * this.item_size.x) / (this.key_count - 1);
          i = this.left + o * (this.item_size.x + s) + this.item_anchor_point.x * this.item_size.x;
        }
        break;
      case r.RIGHT:
        var c = this.parent_size.x - this.getBoundingRect().x;
        i = this.left + o * (this.item_size.x + this.space.x) + this.item_anchor_point.x * this.item_size.x + c;
    }
    switch (this.vertical_layout_type) {
      case a.TOP:
        n = this.top + t * (this.item_size.y + this.space.y) + (1 - this.item_anchor_point.y) * this.item_size.y;
        break;
      case a.CENTER:
        if (1 === this.key_count) {
          var l = (this.parent_size.y - this.item_size.y - this.top - this.bottom) / 2;
          n = this.top + l + (1 - this.item_anchor_point.y) * this.item_size.y;
        } else {
          l = (this.parent_size.y - (this.top + this.bottom) - this.key_count * this.item_size.y) / (this.key_count - 1);
          n = this.top + t * (this.item_size.y + l) + (1 - this.item_anchor_point.y) * this.item_size.y;
        }
        break;
      case a.BOTTOM:
        var u = this.parent_size.y - this.getBoundingRect().y;
        n = this.top + t * (this.item_size.y + this.space.y) + (1 - this.item_anchor_point.y) * this.item_size.y + u;
    }
    return new cc.Vec2(i, -n);
  };
  return _ctor;
}($2LayoutObject.LayoutObject);
exports.TwoDLayoutObject = exp_TwoDLayoutObject;