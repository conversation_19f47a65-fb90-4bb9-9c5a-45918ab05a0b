/**
 * MoveEntity
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Smoother = require('Smoother');
const $2BaseEntity = require('BaseEntity');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: $2BaseEntity.default,

    properties: {
        Picking: {
            get() {
                var e;
                return (null === (e = this.property) || undefined === e ? undefined : e.cut.Picking) || 200;
            },
            visible: false
        },
        maxAllHp: {
            get() {
                return this.property.cut.hp;
            },
            visible: false
        },
        velocity: {
            get() {
                return this._velocity;
            },
            set(value) {
                this._velocity.set(e);
            },
            visible: false
        },
        side: {
            get() {
                return this._side;
            },
            set(value) {
                this._side = e;
            },
            visible: false
        },
        heading: {
            get() {
                return this._heading;
            },
            set(value) {
                if (e.magSqr() < 1e-5) {
                console.error("new heading is zero", this.ID);
                } else {
                this._heading.set(e);
                cc.Vec2.set(this._side, -e.y, e.x);
                if (!this.isSmoother) {
                var t = this._heading.signAngle(cc.Vec2.UP);
                var o = cc.misc.radiansToDegrees(t);
                this.node && (this.node.angle = -o);
                }
                }
            },
            visible: false
        },
        maxSpeed: {
            get() {
                return this._maxSpeed;
            },
            set(value) {
                this._maxSpeed = e;
            },
            visible: false
        },
        maxForce: {
            get() {
                return this._maxForce;
            },
            set(value) {
                this._maxForce = e;
            },
            visible: false
        }
    },

    ctor: function () {
        this._velocity = cc.v2(0, 0)
        this._side = cc.v2(0, 0)
        this._heading = cc.v2(0, 1)
        this._maxSpeed = 250
        this._maxForce = 400
        this._timeElapsed = 0
        this.isSmoother = true
        this._headingSmoother = null
    },

    // use this for initialization
    onLoad: function () {
    },

    timeElapse: function () {
        return this._timeElapsed;
    },

    onUpdate: function (t) {
        this._super(t);
        this._timeElapsed = t;
    },

    onEnable: function () {
        this.changeListener(true);
    },

    onDisable: function () {
        this.changeListener(false);
    },

    updateDir: function () {
        var e = this._headingSmoother.onUpdate(this._heading);
        this.horDir = e.x > 0 ? -1 : 1;
    },

    init: function () {
        this._super();
        this._headingSmoother || (this._headingSmoother = new $2Smoother.Smoother(10));
    },

    tagVehiclesWithinViewRange: function () {
        // TODO: 实现方法逻辑
    },

    addBuff: function () {
        // TODO: 实现方法逻辑
    },

    addBuffByData: function (e, t) {
        undefined === t && (t = 1);
    },

    changeListener: function () {
        // TODO: 实现方法逻辑
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
