/**
 * M20_PrePare_Equip
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');
const $2M20EquipitemBlock = require('M20EquipitemBlock');
const $2M20EquipitemList = require('M20EquipitemList');

var i;
var s;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__spreadArrays = __spreadArrays;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        equipBox: {
            type: cc.Node,
            default: null
        },
        unlockBox: {
            type: cc.Node,
            default: null
        },
        notUnlockBox: {
            type: cc.Node,
            default: null
        },
        equipGridItem: {
            type: cc.Prefab,
            default: null
        },
        equiplockitem: {
            type: cc.Prefab,
            default: null
        },
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        },
        equippack: {
            get() {
                return this.mode.userEquipPack.filter(function (e) {
                return e.isFitOut;
                }).sort(function (e, t) {
                return e.sort - t.sort;
                });
            },
            visible: false
        }
    },

    ctor: function () {
        this.equipBox = null
        this.unlockBox = null
        this.notUnlockBox = null
        this.equipGridItem = null
        this.equiplockitem = null
        this.stateType = s.Start
    },

    // use this for initialization
    onLoad: function () {
    },

    changeListener: function (e) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Item_GoodsChange, this.resetAll, this, -200);
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Refresh_List, this.resetAll, this);
        $2Notifier.Notifier.changeCall(e, $2CallID.CallID.M20_SelectEquip, this.getThis, this);
    },

    getThis: function () {
        return this;
    },

    setInfo: function () {
        this.stateType = s.Start;
        var e = cc.find("bg/usingnode/bgBox", this.node);
        for (var t = 0; t < 8; t++) {
        e.children[t] || cc.instantiate(e.children[0]).setAttribute({
        parent: e
        });
        }
        this.resetAll();
    },

    resetAll: function () {
        var e = this;
        this.resetEquip();
        this.resetUnlock();
        this.resetNotUnlock();
        if (11 == $2Manager.Manager.vo.userVo.guideIndex) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
        this.scheduleOnce(function () {
        $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
        targetNode: e.equipBox.children[0]
        });
        }, 0);
        }
    },

    onClickItem: function (e) {
        if (this.stateType == s.Equip && e.isEquip) {
        this.mode.userEquipPack.replace(e.equipID, this.cutSelectEquip.equipID);
        this.stateType = s.Start;
        $2Notifier.Notifier.send($2ListenID.ListenID.Refresh_List);
        } else if (this.stateType == s.Start && (cc__spreadArrays(this.equipBox.children, this.unlockBox.children).forEach(function (t) {
        var o = t.getComponent($2M20EquipitemList.default);
        o != e && o.resetState();
        }), 12 == $2Manager.Manager.vo.userVo.guideIndex || 16 == $2Manager.Manager.vo.userVo.guideIndex)) {
        var t = 12 == $2Manager.Manager.vo.userVo.guideIndex ? e.dropNode.children[0] : e.btnuse;
        $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
        targetNode: t,
        offset: cc.v2(0, 10)
        });
        }
    },

    onSelectItem: function (e) {
        16 == $2Manager.Manager.vo.userVo.guideIndex && $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
        if (this.equippack.length < 8) {
        this.mode.AssembleEquip(e.equipID);
        $2Notifier.Notifier.send($2ListenID.ListenID.Refresh_List);
        } else {
        this.cutSelectEquip = e;
        this.stateType = s.Equip;
        $2Notifier.Notifier.send($2ListenID.ListenID.Refresh_Item, e);
        }
    },

    resetEquip: function () {
        var e = this;
        this.equipBox.hideAllChildren();
        var t = this.equipBox.getComponent(cc.Layout);
        t.enabled = true;
        this.equippack.forEach(function (t, o) {
        var i = e.equipBox.children[o] || cc.instantiate(e.equipGridItem);
        i.setAttribute({
        parent: e.equipBox,
        active: true,
        zIndex: o
        });
        var n = i.getComponent($2M20EquipitemList.default);
        n.setInfo(+t.id);
        n.setClickCall(e.onClickItem.bind(e));
        });
        this.scheduleOnce(function () {
        t && (t.enabled = false);
        });
    },

    resetUnlock: function () {
        var e = this;
        var t = this.unlockBox.getComponent(cc.Layout);
        t.enabled = true;
        this.unlockBox.hideAllChildren();
        var o = this.mode.userEquipPack.filter(function (e) {
        return !e.isFitOut;
        }).map(function (e) {
        return $2Cfg.Cfg.RoleUnlock.get(e.id);
        });
        o.sort(function (e, t) {
        return t.Count - e.Count;
        });
        o.forEach(function (t, o) {
        var i = e.unlockBox.children[o] || cc.instantiate(e.equipGridItem);
        i.setAttribute({
        parent: e.unlockBox,
        active: true,
        zIndex: o
        });
        var n = i.getComponent($2M20EquipitemList.default);
        n.setInfo(+t.id);
        n.setClickCall(e.onClickItem.bind(e));
        });
        this.scheduleOnce(function () {
        t.enabled = false;
        });
    },

    resetNotUnlock: function () {
        var e = this;
        this.notUnlockBox.hideAllChildren();
        var t = $2Cfg.Cfg.RoleUnlock.getArray().filter(function (t) {
        return 1 == t.type && t.unlock && !e.mode.checkIsUnlock(t);
        });
        t.sort(function (e, t) {
        if (e.rarity == $2GameSeting.GameSeting.RarityType.S || t.rarity == $2GameSeting.GameSeting.RarityType.S) {
        return -1;
        } else {
        return e.Count - t.Count;
        }
        });
        t.forEach(function (t, o) {
        var i = e.notUnlockBox.children[o] || cc.instantiate(e.equiplockitem);
        i.setAttribute({
        parent: e.notUnlockBox,
        active: true
        });
        i.getComponent($2M20EquipitemBlock.default).setInfo(t.id);
        });
    },

    onShowFinish: function () {
        var e;
        var t;
        null === (t = null === (e = this.param) || undefined === e ? undefined : e.showCb) || undefined === t || t.call(e, this.node);
        this.node.opacity = 255;
    },

    onOpen: function () {
        this.node.opacity = 0;
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
