const fs = require('fs');
const path = require('path');

// 验证修复结果的脚本
class FixVerifier {
    constructor() {
        this.outputDir = './output';
        this.issues = [];
        this.successes = [];
    }

    readFile(filePath) {
        try {
            return fs.readFileSync(filePath, 'utf8');
        } catch (error) {
            this.issues.push(`读取文件失败: ${filePath} - ${error.message}`);
            return null;
        }
    }

    // 验证属性是否正确添加了 override: true
    verifyOverrideProperty(content, fileName, propertyName) {
        const propertyPattern = new RegExp(`${propertyName}\\s*:\\s*{[\\s\\S]*?override:\\s*true[\\s\\S]*?}`, 'g');
        const matches = content.match(propertyPattern);
        
        if (matches && matches.length > 0) {
            this.successes.push(`✓ ${fileName}: ${propertyName} 属性正确添加了 override: true`);
            return true;
        } else {
            this.issues.push(`✗ ${fileName}: ${propertyName} 属性缺少 override: true`);
            return false;
        }
    }

    // 验证 destroy 函数是否正确调用了 this._super()
    verifyDestroyFunction(content, fileName) {
        const destroyPattern = /destroy\s*:\s*function\s*\([^)]*\)\s*{\s*this\._super\(\);/;
        
        if (destroyPattern.test(content)) {
            this.successes.push(`✓ ${fileName}: destroy 函数正确调用了 this._super()`);
            return true;
        } else {
            this.issues.push(`✗ ${fileName}: destroy 函数缺少 this._super() 调用`);
            return false;
        }
    }

    // 验证数组默认值
    verifyArrayDefault(content, fileName, propertyName) {
        const arrayPattern = new RegExp(`${propertyName}\\s*:\\s*{[\\s\\S]*?default\\s*:\\s*\\[\\][\\s\\S]*?}`, 'g');
        
        if (arrayPattern.test(content)) {
            this.successes.push(`✓ ${fileName}: ${propertyName} 数组默认值正确`);
            return true;
        } else {
            this.issues.push(`✗ ${fileName}: ${propertyName} 数组默认值不正确`);
            return false;
        }
    }

    // 验证语法格式
    verifySyntax(content, fileName) {
        const syntaxIssues = [];
        
        // 检查多余的逗号
        if (content.includes(';,')) {
            syntaxIssues.push('存在多余的逗号 (;,)');
        }
        
        // 检查未闭合的大括号
        const openBraces = (content.match(/{/g) || []).length;
        const closeBraces = (content.match(/}/g) || []).length;
        if (openBraces !== closeBraces) {
            syntaxIssues.push(`大括号不匹配 (开: ${openBraces}, 闭: ${closeBraces})`);
        }
        
        // 检查多余的数字
        if (/override:\s*true\s*\d+/.test(content)) {
            syntaxIssues.push('override 属性后有多余的数字');
        }
        
        if (syntaxIssues.length === 0) {
            this.successes.push(`✓ ${fileName}: 语法格式正确`);
            return true;
        } else {
            this.issues.push(`✗ ${fileName}: 语法问题 - ${syntaxIssues.join(', ')}`);
            return false;
        }
    }

    // 验证单个文件
    verifyFile(fileName, checks) {
        const filePath = path.join(this.outputDir, fileName);
        console.log(`验证文件: ${fileName}`);
        
        const content = this.readFile(filePath);
        if (!content) {
            return false;
        }

        let allPassed = true;

        // 执行各种检查
        checks.forEach(check => {
            switch (check.type) {
                case 'override':
                    if (!this.verifyOverrideProperty(content, fileName, check.property)) {
                        allPassed = false;
                    }
                    break;
                case 'destroy':
                    if (!this.verifyDestroyFunction(content, fileName)) {
                        allPassed = false;
                    }
                    break;
                case 'array':
                    if (!this.verifyArrayDefault(content, fileName, check.property)) {
                        allPassed = false;
                    }
                    break;
                case 'syntax':
                    if (!this.verifySyntax(content, fileName)) {
                        allPassed = false;
                    }
                    break;
            }
        });

        return allPassed;
    }

    // 运行验证
    run() {
        console.log('开始验证修复结果...\n');

        // 定义需要验证的文件和检查项
        const verificationTasks = [
            {
                file: 'OrganismBase.js',
                checks: [
                    { type: 'override', property: 'horDir' },
                    { type: 'syntax' }
                ]
            },
            {
                file: 'FCircleCollider.js',
                checks: [
                    { type: 'override', property: 'type' },
                    { type: 'syntax' }
                ]
            },
            {
                file: 'FBoxCollider.js',
                checks: [
                    { type: 'override', property: 'type' },
                    { type: 'syntax' }
                ]
            },
            {
                file: 'FPolygonCollider.js',
                checks: [
                    { type: 'override', property: 'type' },
                    { type: 'syntax' }
                ]
            },
            {
                file: 'M33_FightBuffView.js',
                checks: [
                    { type: 'override', property: 'param' },
                    { type: 'syntax' }
                ]
            },
            {
                file: 'M33_FightScene.js',
                checks: [
                    { type: 'override', property: 'role' },
                    { type: 'syntax' }
                ]
            },
            {
                file: 'M33_FightUIView.js',
                checks: [
                    { type: 'override', property: 'game' },
                    { type: 'syntax' }
                ]
            },
            {
                file: 'BulletVo.js',
                checks: [
                    { type: 'destroy' },
                    { type: 'syntax' }
                ]
            },
            {
                file: 'Launcher.js',
                checks: [
                    { type: 'array', property: 'logos' },
                    { type: 'syntax' }
                ]
            }
        ];

        let totalFiles = 0;
        let passedFiles = 0;

        verificationTasks.forEach(task => {
            totalFiles++;
            if (this.verifyFile(task.file, task.checks)) {
                passedFiles++;
            }
        });

        // 输出结果
        console.log('\n=== 验证结果 ===');
        console.log(`总文件数: ${totalFiles}`);
        console.log(`通过验证: ${passedFiles}`);
        console.log(`失败文件: ${totalFiles - passedFiles}`);
        
        console.log('\n成功项目:');
        this.successes.forEach(success => console.log(`  ${success}`));
        
        if (this.issues.length > 0) {
            console.log('\n需要注意的问题:');
            this.issues.forEach(issue => console.log(`  ${issue}`));
        }

        if (passedFiles === totalFiles) {
            console.log('\n🎉 所有文件都通过了验证！');
        } else {
            console.log('\n⚠️  部分文件需要进一步检查。');
        }
    }
}

// 运行验证脚本
const verifier = new FixVerifier();
verifier.run();
