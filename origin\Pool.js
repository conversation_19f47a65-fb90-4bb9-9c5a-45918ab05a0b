Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PoolMgrBase = exports.PoolManager = exports.Pool = exports.PoolSpawner = undefined;
var $2Time = require("Time");
var exp_PoolSpawner = function () {
  function _ctor(e, t, o, i, n, r) {
    undefined === r && (r = -1);
    this.runNum = 0;
    this._preloadCount = 0;
    this._spawneds = [];
    this._clearing = false;
    this._cullWatcher = null;
    this.cullAbove = -1;
    this._isPreload = false;
    this._id = e;
    this._pool = t;
    this._spawnedCall = o;
    this._despawnedCall = i;
    this._preloadCount = n;
    this.cullAbove = r;
  }
  Object.defineProperty(_ctor.prototype, "Id", {
    get: function () {
      return this._id;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.clearPoolItem = function (e) {
    var t = this._spawneds.indexOf(e);
    if (-1 != t) {
      var o = this._spawneds.splice(t, 1);
      this._clear(o[0]);
    }
  };
  _ctor.prototype.smoothClear = function () {
    this._clearing = true;
    if (null == this._cullWatcher) {
      this._cullWatcher = $2Time.Time.delay(60, this.cullDespawned, null, this, -1);
    } else {
      this._cullWatcher.times = 60;
    }
  };
  _ctor.prototype.cullDespawned = function () {
    var e = this.cullAbove;
    (this._clearing || this.cullAbove <= 0) && (e = 0);
    var t = this._spawneds.length;
    if (t > e) {
      t > 5 && (t = 5);
      for (var o = t - 1; o >= 0; o--) {
        var i = this._spawneds.pop();
        this._clear(i);
      }
    }
  };
  _ctor.prototype.preloadInstances = function () {
    if (!this._isPreload) {
      this._isPreload = true;
      for (var e = 0; e < this._preloadCount; e++) {
        var t = this.spawnNew();
        t.unuse();
        this._spawneds.push(t);
      }
    }
  };
  _ctor.prototype.spawnInstance = function () {
    var e = [];
    for (var t = 0; t < arguments.length; t++) {
      e[t] = arguments[t];
    }
    this._clearing = false;
    var o = null;
    null != (o = this._spawneds.length > 0 ? this._spawneds.shift() : this.spawnNew()) && o.reuse.apply(o, e);
    this.runNum++;
    return o;
  };
  _ctor.prototype.despawnInstance = function (e, t, o) {
    undefined === o && (o = true);
    if (null == e) {
      return cc.error("despawnInstance data is null, Pool-", this._pool.poolType), false;
    } else {
      return e.unuse(), this.runNum--, t ? e.destroy() : this._spawneds.push(e), !(o && !this.despawnEvent(e));
    }
  };
  _ctor.prototype.destroy = function () {
    null != this._pool.onDelSpawner && this._pool.onDelSpawner.call(this._pool.target, this._id);
    if (this._cullWatcher) {
      this._cullWatcher.cancel();
      this._cullWatcher = null;
    }
    this.clear();
  };
  _ctor.prototype.clear = function () {
    var e = 0;
    for (var t = this._spawneds.length; e < t; e++) {
      this._clear(this._spawneds[e]);
    }
    this._spawneds.length = 0;
  };
  _ctor.prototype._clear = function (e) {
    e.destroy();
  };
  _ctor.prototype.despawnEvent = function (e) {
    try {
      null != this._despawnedCall && this._despawnedCall.call(this._pool.target, e);
      return true;
    } catch (t) {}
    return false;
  };
  _ctor.prototype.spawnNew = function () {
    if (null == this._pool) {
      return cc.warn(this._pool, "pool has destroy!!!", this._id), null;
    } else {
      if (null == this._spawnedCall) {
        return cc.error("spawnedCall is null", this._id), null;
      } else {
        return this._spawnedCall.call(this._pool.target, this._id, this);
      }
    }
  };
  return _ctor;
}();
exports.PoolSpawner = exp_PoolSpawner;
var exp_Pool = function () {
  function e(e, t) {
    this._spawners = cc.js.createMap();
    this.poolType = e;
    this.target = t;
  }
  e.prototype.getSpawner = function (e) {
    var t = this._spawners[e];
    if (null == t) {
      return cc.warn("Pool.getSpawner null", this.poolType, e), null;
    } else {
      return t;
    }
  };
  e.prototype._clear = function () {
    var e = toArray(this._spawners);
    var t = 0;
    for (var o = e.length; t < o; t++) {
      var i = e[t];
      delete this._spawners[i.Id];
      i.destroy();
    }
  };
  e.prototype._destroy = function () {
    for (var e in this._spawners) {
      this._spawners[e].destroy();
    }
    this._spawners = null;
  };
  e.prototype.createSpawner = function (e, t, o, i, r) {
    undefined === o && (o = null);
    undefined === i && (i = 0);
    undefined === r && (r = -1);
    var a = this._spawners[e];
    if (null != a) {
      cc.error("createSpawner already create id:", e);
      return a;
    }
    a = new exp_PoolSpawner(e, this, t, o, i, r);
    this._spawners[e] = a;
  };
  e.prototype.delSpawner = function (e) {
    var t = this._spawners[e];
    if (null != t) {
      t.destroy();
      delete this._spawners[e];
    }
  };
  e.prototype.isExitsSpawner = function (e) {
    return null != this._spawners[e];
  };
  e.prototype.despawn = function (e, t) {
    undefined === t && (t = false);
    if (null != e) {
      if (e.spawner) {
        e.spawner.despawnInstance(e, t), t || e.spawner.smoothClear();
      }
    } else {
      cc.warn(this.poolType, "data is null");
    }
  };
  e.prototype.spawn = function (e) {
    var t = [];
    for (var o = 1; o < arguments.length; o++) {
      t[o - 1] = arguments[o];
    }
    var i = this._spawners[e];
    null == i && cc.error(this.poolType, "can't find spawner id:", e);
    return i.spawnInstance.apply(i, t);
  };
  return e;
}();
exports.Pool = exp_Pool;
var a = function () {
  function e() {
    this._pools = new Map();
  }
  e.prototype.isExist = function (e) {
    return null != this._pools.get(e);
  };
  e.prototype.create = function (e, t) {
    var o = this._pools.get(e);
    if (null != o) {
      return cc.error("Pool has Created:", e), o;
    } else {
      return null == t && cc.error("PoolManager.create target null", e), o = new exp_Pool(e, t), this._pools.set(e, o), o;
    }
  };
  e.prototype.destroy = function (e) {
    var t = this._pools.get(e);
    if (null != t) {
      t._destroy();
      this._pools.delete(e);
    }
  };
  e.prototype.clear = function (e) {
    var t = this._pools.get(e);
    null != t && t._clear();
  };
  return e;
}();
exports.PoolManager = new a();
var exp_PoolMgrBase = function () {
  function e(e) {
    this.SpawnerName = "PoolItem";
    this._poolType = "PoolMgrBase";
    this._poolType = e;
    this.initPool();
  }
  e.prototype.initPool = function () {
    exports.PoolManager.isExist(this._poolType) || (this._pool = exports.PoolManager.create(this._poolType, this));
  };
  e.prototype.compCreateDelegate = function (e, t) {
    var o = e;
    var i = cc.js.getClassByName(o);
    if (i) {
      var n = new i();
      n.spawner = t;
      return n;
    }
    cc.error("compCreateDelegate can't find class by name", o);
    return null;
  };
  e.prototype.despawn = function (e) {
    if (null != this._pool) {
      this._pool.despawn(e);
    } else {
      null != e && e.destroy();
    }
  };
  e.prototype.spawn = function () {
    if (null == this._pool) {
      return null;
    } else {
      return this._pool.isExitsSpawner(this.SpawnerName) || this._pool.createSpawner(this.SpawnerName, this.compCreateDelegate, null, 0), this._pool.spawn(this.SpawnerName);
    }
  };
  e.prototype.clone = function (e) {
    var t = this.spawn();
    for (var o in e) {
      t[o] = e[o];
    }
    return t;
  };
  return e;
}();
exports.PoolMgrBase = exp_PoolMgrBase;