Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Time = exports.TimeDelay = undefined;
var $2Notifier = require("Notifier");
var $2NotifyID = require("NotifyID");
var $2Watcher = require("Watcher");
var $2ObjectPool = require("ObjectPool");
var $2MinSortList = require("MinSortList");
var c = function () {
  function e() {
    this._time = 0;
    this._deltaTime = 0;
    this._frameCount = 0;
    this._clientTimeMs = 0;
    this.timeDelay = new exp_TimeDelay();
    this._gameTime = 0;
    this._serverTimeMs = 0;
    this._serverInitMs = 0;
    this._serverUpdateMs = 0;
    this._clientDate = new Date();
    this._scaling = false;
    this._scale = 1;
    this._scaleDura = 0;
    this._scaleTimeout = 0;
    this._scaleSmooth = true;
  }
  Object.defineProperty(e.prototype, "time", {
    get: function () {
      return this._time;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "deltaTime", {
    get: function () {
      return this._deltaTime;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "frameCount", {
    get: function () {
      return this._frameCount;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "clientTimeMs", {
    get: function () {
      return Date.now();
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "gameTime", {
    get: function () {
      return this._gameTime;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "serverTimeMs", {
    get: function () {
      this._serverTimeMs = this._serverInitMs + Date.now() - this._serverUpdateMs;
      return this._serverTimeMs;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "serverTime", {
    get: function () {
      return Math.floor(this.serverTimeMs / 1e3);
    },
    enumerable: false,
    configurable: true
  });
  e.prototype.setServerTime = function (e) {
    this._serverTimeMs = e;
    this._serverInitMs = e;
    this._serverUpdateMs = Date.now();
    this.resetMidnight();
  };
  Object.defineProperty(e.prototype, "clientDate", {
    get: function () {
      return this._clientDate;
    },
    enumerable: false,
    configurable: true
  });
  e.prototype.resetMidnight = function () {
    var e = new Date(this._serverTimeMs + 864e5);
    e.setHours(0, 0, 0, 0);
    this.midnight = e.getTime();
  };
  e.prototype.update = function (e) {
    this._frameCount += 1;
    this._deltaTime = e;
    this._scaling && (e *= this.scale);
    this._time += e;
    this._gameTime += e;
    this.updateScale(e);
    $2Notifier.Notifier.isExist($2NotifyID.NotifyID.Game_Update) && $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_Update, e);
    if (this.serverTimeMs > this.midnight) {
      $2Notifier.Notifier.send($2NotifyID.NotifyID.Time_NewDay);
      this.resetMidnight();
    }
    this.timeDelay.onUpdate(e);
  };
  e.prototype.setScale = function (e, t, o) {
    undefined === o && (o = true);
    this._scaling = true;
    this._scale = e;
    this._scaleDura = t;
    this._scaleTimeout = this._time + t;
    this._scaleSmooth = o;
    cc.director.getScheduler().setTimeScale(e);
    $2Notifier.Notifier.send($2NotifyID.NotifyID.Time_Scale, e);
  };
  Object.defineProperty(e.prototype, "scale", {
    get: function () {
      return cc.director.getScheduler().getTimeScale();
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "isScaling", {
    get: function () {
      return this._scaling;
    },
    enumerable: false,
    configurable: true
  });
  e.prototype.updateScale = function () {
    if (this._scaling) {
      if (this._time > this._scaleTimeout) {
        this._scaling = false;
        cc.director.getScheduler().setTimeScale(1);
        return void $2Notifier.Notifier.send($2NotifyID.NotifyID.Time_Scale, 1);
      }
      if (this._scaleSmooth) {
        var e = cc.misc.lerp(this._scale, 1, 1 - (this._scaleTimeout - this._time) / this._scaleDura);
        cc.director.getScheduler().setTimeScale(e);
        $2Notifier.Notifier.send($2NotifyID.NotifyID.Time_Scale, e);
      }
    }
  };
  e.prototype.delay = function (e, t, o, i, n) {
    undefined === o && (o = null);
    undefined === i && (i = null);
    undefined === n && (n = 1);
    return this.timeDelay.delay(e, t, o, i, n);
  };
  e.prototype.doCancel = function (e) {
    this.timeDelay.doCancel(e);
  };
  e.prototype.reset = function () {
    this.timeDelay.reset();
  };
  e.prototype.startGameTimeLog = function () {
    this._gameTime = 0;
  };
  return e;
}();
var exp_TimeDelay = function () {
  function _ctor() {
    this._time = 0;
    this._index = 1;
    this._pool = new $2ObjectPool.ObjectPool(function () {
      return new $2Watcher.Watcher();
    });
    this._watchers = new $2MinSortList.MinSortList(this.compareTime);
  }
  Object.defineProperty(_ctor.prototype, "time", {
    get: function () {
      return this._time;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "watchers", {
    get: function () {
      return this._watchers;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.delay = function (e, t, o, i, n) {
    undefined === o && (o = null);
    undefined === i && (i = null);
    undefined === n && (n = 1);
    if (null == t) {
      return null;
    }
    if (null == e || e < 0) {
      return null;
    }
    var r = this._pool.pop();
    var a = ++this._index;
    r._setId(a);
    r.initWithCallback(this._time + e, e, t, o, i, n);
    this._watchers.add(r);
    return r;
  };
  _ctor.prototype.doCancel = function (e) {
    null != e && e.cancel();
  };
  _ctor.prototype.cancelBy = function (e) {
    if (e) {
      var t = this.watchers.element.find(function (t) {
        return t.id == e;
      });
      null != t && t.cancel();
    }
  };
  _ctor.prototype.cancelByTag = function (e) {
    var t = this.watchers.element.find(function (t) {
      return t.tag == e;
    });
    null != t && t.cancel();
  };
  _ctor.prototype.reset = function () {
    this._index = 0;
    this._watchers.clear(this.doCancel, this);
  };
  _ctor.prototype.destroy = function () {
    this.reset();
    this._pool.clear();
    this._pool = null;
  };
  _ctor.prototype.compareTime = function (e, t) {
    if (e.nextTime < t.nextTime) {
      return -1;
    } else {
      if (e.nextTime > t.nextTime) {
        return 1;
      } else {
        return 0;
      }
    }
  };
  _ctor.prototype.onUpdate = function (e) {
    this._time += e;
    for (var t = 0;;) {
      var o = this._watchers.peek();
      if (null == o || o.nextTime > this._time) {
        break;
      }
      var i = this._watchers.pop();
      i._callBack();
      if (i.enable) {
        this._watchers.add(i);
      } else {
        this._pool.push(i);
      }
      if (++t > 2e3) {
        break;
      }
    }
  };
  return _ctor;
}();
exports.TimeDelay = exp_TimeDelay;
exports.Time = new c();