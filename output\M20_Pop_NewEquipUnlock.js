/**
 * M20_Pop_NewEquipUnlock
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2UIManager = require('UIManager');
const $2WonderSdk = require('WonderSdk');
const $2EaseScaleTransition = require('EaseScaleTransition');
const $2Game = require('Game');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        img: {
            type: cc.Sprite,
            default: null
        },
        gname: {
            type: cc.Label,
            default: null
        },
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    ctor: function () {
        this.img = null
        this.gname = null
    },

    // use this for initialization
    onLoad: function () {
    },

    changeListener: function (t) {
        this._super(t);
    },

    onClickFrame: function () {
        this.close();
    },

    onOpen: function () {
        var e = this;
        var t = $2Cfg.Cfg.RoleUnlock.find({
        id: this.param.id
        });
        $2Manager.Manager.loader.loadSpriteAsync(t.icon).then(function (t) {
        e.img.spriteFrame = t;
        });
        this.gname.string = t.roleName;
    },

    onClose: function () {
        var e;
        var t;
        null === (t = (e = this.param).cb) || undefined === t || t.call(e);
        if (15 == $2Manager.Manager.vo.userVo.guideIndex && $2UIManager.UIManager.queue.length <= 1) {
        var o = $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView);
        $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
        targetNode: o.toggleContainer.toggleItems[3].node
        });
        }
        var i = $2WonderSdk.WonderSdk._instance.isGoogleAndroid;
        5 == this.mode.rVo.curPassLv && i && $2Manager.Manager.vo.userVo.ispoplikegame && $2UIManager.UIManager.OpenInQueue("ui/ModeCatGame/M3_PopLikeGameView", $2MVC.MVC.openArgs());
    },

    setInfo: function () {
        // TODO: 实现方法逻辑
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
