/**
 * TestItem
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Manager = require('Manager');
const $2AlertManager = require('AlertManager');
const $2Game = require('Game');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        itemName: {
            type: cc.Label,
            default: null
        },
        itemCount: {
            type: cc.Label,
            default: null
        },
        itemIcon: {
            type: cc.Sprite,
            default: null
        },
        leftBtn: {
            type: cc.Button,
            default: null
        },
        rightBtn: {
            type: cc.Button,
            default: null
        },
        game: {
            get() {
                return $2Game.Game.Mgr.instance;
            },
            visible: false
        }
    },

    ctor: function () {
        this.itemName = null
        this.itemCount = null
        this.itemIcon = null
        this.leftBtn = null
        this.rightBtn = null
        this.count = 1
        this.close = null
    },

    // use this for initialization
    onLoad: function () {
    },

    setCloseCallBack: function (e) {
        this.close = e;
    },

    setdata: function (e) {
        this.cfg = e;
        this.itemName.string = e.name;
        $2Manager.Manager.loader.loadSpriteToSprit(e.icon, this.itemIcon, this.node.parent);
        this.checkBtn();
    },

    checkBtn: function () {
        this.itemCount.string = this.count + "";
        this.leftBtn.interactable = this.count > 1;
    },

    onBtn: function (e, t) {
        switch (t) {
        case "minus":
        this.count -= 1;
        this.checkBtn();
        break;
        case "plus":
        this.count += 1;
        this.checkBtn();
        break;
        case "get":
        if (!this.game) {
        return void $2AlertManager.AlertManager.showNormalTips("请在战斗背包页面设置");
        }
        var o = this.game.packView;
        if (!o) {
        return void $2AlertManager.AlertManager.showNormalTips("请在战斗背包页面设置");
        }
        for (var i = 0; i < this.count; i++) {
        o.newProp(this.cfg).then(function (e) {
        o.setInSpareBox(e);
        o.checkViewoUnLock(e);
        });
        }
        this.close && this.close();
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
