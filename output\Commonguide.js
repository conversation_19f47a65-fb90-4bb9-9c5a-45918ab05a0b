/**
 * Commonguide
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2Time = require('Time');
const $2AlertManager = require('AlertManager');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2MVC.MVC.BaseView,

    properties: {
        focusArea: {
            type: cc.Node,
            default: null
        },
        bgnode: {
            type: cc.Node,
            default: null
        },
        handnode: {
            type: cc.Node,
            default: null
        },
        desc: {
            type: cc.Node,
            default: null
        },
        btnjump: {
            type: cc.Node,
            default: null
        }
    },

    ctor: function () {
        this.focusArea = null
        this.bgnode = null
        this.handnode = null
        this.desc = null
        this.btnjump = null
        this._cb = null
        this.descPos = {  }
    },

    // use this for initialization
    onLoad: function () {
    },

    setInfo: function () {
        this.offTouch();
    },

    onShowFinish: function () {
        // TODO: 实现方法逻辑
    },

    changeListener: function (e) {
        if (this._openArgs.param.isnewuser) {
        console.log("初始化新手引导");
        o.isinitGuide = true;
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Common_Guide_Forcus, this.forcus, this);
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Common_Guide_Anim, this.animationto, this);
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Common_Guide_OnlyDesc, this.descOnly, this);
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Common_Guide_Close, this.hideMask, this);
        this.btnjump.active = 1 == $2Manager.Manager.vo.switchVo.m20guideToggle[1];
        } else {
        $2Manager.Manager.vo.userVo.guideIndex = 17;
        $2Manager.Manager.vo.saveUserData();
        }
    },

    onOpen: function () {
        this.guideList = $2Cfg.Cfg.BagGuide.getArray();
    },

    onShow: function () {
        // TODO: 实现方法逻辑
    },

    onHide: function () {
        // TODO: 实现方法逻辑
    },

    onHideFinish: function () {
        // TODO: 实现方法逻辑
    },

    setdesc: function () {
        var e;
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
        this.desc.getComponentInChildren(cc.Button).interactable = false;
        var t = this.guideList[$2Manager.Manager.vo.userVo.guideIndex].btnClick;
        this.desc.getChildByName("fullbutton").active = t && 1 == t;
        this.desc.getChildByName("fullbutton").getComponent(cc.Button).interactable = t && 1 == t;
        this.desc.getChildByName("mask").active = false;
        this.desc.position = this.descPos[0];
        if (this.guideList[$2Manager.Manager.vo.userVo.guideIndex].desc) {
        this.desc.active = true;
        this.desc.getComponentInChildren(cc.Label).string = null === (e = this.guideList[$2Manager.Manager.vo.userVo.guideIndex]) || undefined === e ? undefined : e.desc;
        }
    },

    descOnly: function (e, t) {
        console.log("descOnly", e);
        this._cb = t;
        this.setdesc();
        this.desc.getComponentInChildren(cc.Button).interactable = true;
        e && (this.desc.position = this.descPos[e]);
        this.desc.getChildByName("mask").active = true;
    },

    animationto: function (e, t, o, i, n) {
        var r = this;
        undefined === o && (o = cc.v2(0, 0));
        undefined === n && (n = true);
        if (e && t) {
        this._cb = i;
        var a = e.wordPos;
        cc.Tween.stopAllByTarget(this.handnode);
        var s = t.wordPos;
        cc.tween(this.handnode).set({
        position: a.addSelf(cc.v2(60, 0))
        }).to(.5, {
        opacity: 255
        }).to(1, {
        position: s.addSelf(o.addSelf(cc.v2(60, 0)))
        }).union().repeat(3).call(function () {
        r.handnode.opacity = 0;
        }).start();
        n && $2Time.Time.delay(1.5, function () {
        cc.tween(r.handnode).to(.5, {
        scale: .8
        }).to(.5, {
        scale: 1
        }).union().repeatForever().start();
        });
        this.setdesc();
        } else {
        null == i || i();
        }
    },

    jumpGuide: function () {
        $2Manager.Manager.vo.userVo.guideIndex = 17;
        $2Manager.Manager.vo.saveUserData();
        this.hideMask();
        this.close();
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_HandleButton, true, [0, 1, 2]);
    },

    showJump: function () {
        var e = this;
        $2AlertManager.AlertManager.showAlert($2AlertManager.AlertType.SELECT, {
        desc: "是否跳过新手教程",
        confirm: function () {
        e.jumpGuide();
        }
        });
    },

    hideMask: function () {
        var e;
        var t = this;
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "Guide", {
        Step: $2Manager.Manager.vo.userVo.guideIndex
        });
        cc.Tween.stopAllByTarget(this.handnode);
        this.focusArea.active = false;
        this.focusArea.setContentSize(750, 1334);
        this.focusArea.setPosition(0, 0);
        $2Manager.Manager.vo.userVo.guideIndex++;
        $2Manager.Manager.vo.saveUserData();
        this.handnode.opacity = 0;
        this.node.getChildByName("block").active = false;
        this.desc.active = false;
        5 == $2Manager.Manager.vo.userVo.guideIndex && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_HandleButton, true, [0, 1]);
        if (15 == $2Manager.Manager.vo.userVo.guideIndex) {
        var o = $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView).toggleContainer.toggleItems[2].node;
        this.forcus({
        targetNode: o,
        tweencb: function () {
        t.animationto(o, o, cc.v2(0, 0), null, true);
        }
        });
        }
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
        null === (e = this._cb) || undefined === e || e.call(this);
        this._cb = null;
        17 == $2Manager.Manager.vo.userVo.guideIndex && this.close();
    },

    forcus: function (e) {
        var t;
        if (e.targetNode) {
        e.offset || (e.offset = cc.Vec2.ZERO);
        e.descpos || (e.descpos = 0);
        this._cb = e.cb;
        this.focusArea.active = true;
        var o = e.targetNode.wordPos.addSelf(e.offset);
        cc.tween(this.focusArea).to(.5, {
        width: e.targetNode.width,
        height: e.targetNode.height,
        position: o
        }).call(function () {
        var t;
        null === (t = e.tweencb) || undefined === t || t.call(e);
        }).start();
        var i = this.node.getChildByName("block");
        i.active = e.blockevent;
        i.setContentSize(e.targetNode.getContentSize());
        i.setPosition(o);
        this.setdesc();
        this.desc.position = this.descPos[e.descpos];
        this.desc.getComponentInChildren(cc.Button).interactable = e.enableclick;
        } else {
        null === (t = e.cb) || undefined === t || t.call(e);
        }
    },

    onBtn: function () {
        // TODO: 实现方法逻辑
    },

    close: function () {
        this._super();
    },

    onClose: function () {
        o.isinitGuide = false;
    },

    confirm: function () {
        e.jumpGuide();
    },

    tweencb: function () {
        t.animationto(o, o, cc.v2(0, 0), null, true);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
