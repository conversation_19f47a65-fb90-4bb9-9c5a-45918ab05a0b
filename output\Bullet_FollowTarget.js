/**
 * Bullet_FollowTarget
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameUtil = require('GameUtil');
const $2BulletBase = require('BulletBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var p = cc.v2();

exports.default = cc.Class({
    extends: $2BulletBase.default,

    properties: {
    },

    ctor: function () {
        this.curTargetPos = cc.v2(0, 0)
        this.curTargetDir = cc.v2(0, 0)
        this.randomTime = 1
        this._deltaTime = 0
    },

    // use this for initialization
    onLoad: function () {
    },

    setBulletVo: function (t) {
        this._super(t);
        this.changeFollow();
    },

    changeFollow: function () {
        if (!(this._deltaTime < this.randomTime)) {
        this.curTarget = $2GameUtil.GameUtil.randomArr(this.game.getTarget({
        pos: this.position,
        radius: this.vo.skillCfg.dis,
        ignoreID: 1 == this.game.monsterMap.size ? [] : [this.idIgnore],
        targetCamp: this.vo.atkCamp,
        target: this.vo.ower
        }));
        if (this.curTarget) {
        this.idIgnore = this.curTarget.ID, this.curTargetPos.set(this.curTarget.position), this.t = 0;
        }
        this._deltaTime = 0;
        }
    },

    onUpdate: function (t) {
        var o;
        this._super(t);
        if (this.isActive) {
        if (null === (o = this.curTarget) || undefined === o ? undefined : o.isActive) {
        this.curTargetPos.set(this.curTarget.bodyPosition), cc.Vec2.squaredDistance(this.position, this.curTargetPos) > Math.pow(100, 2) && this.curTargetDir.set(this.curTargetPos.sub(this.position).normalize()), this.t += .3 * t, this.t = cc.misc.clamp01(this.t), cc.Vec2.lerp(p, this.vo.shootDir, this.curTargetDir, this.t), this.vo.shootDir.addSelf(p).normalizeSelf();
        } else {
        this._deltaTime > this.randomTime && this.changeFollow();
        }
        this._deltaTime += t;
        this.updateDir(0);
        cc.Vec2.multiplyScalar(p, this._vo.shootDir, this.maxSpeed * t);
        cc.Vec2.add(p, this.position, p);
        this.setPosition(p);
        }
    },

    setHurt: function (t) {
        this._super(t);
        this.changeFollow();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
