var i;
var cc__extends = __extends;
var cc__awaiter = __awaiter;
var cc__generator = __generator;
var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.CCTool = exports.GameUtil = exports.getClassByName = exports.cacheFunction = exports.bufferCache = exports.MyRect = exports.MyMat4 = exports.MyVec2 = exports.MyVec3 = undefined;
var $2LanguageFun = require("LanguageFun");
var $2Time = require("Time");
var $2Md5 = require("Md5");
var $2lzstring = require("lzstring");
exports.MyVec3 = cc.Vec3;
exports.MyVec2 = cc.Vec2;
exports.MyMat4 = cc.Mat4;
exports.MyRect = cc.Rect;
var f = cc.v2();
exports.bufferCache = {};
exports.cacheFunction = function (e) {
  return function (t) {
    exports.bufferCache[e] = t;
  };
};
exports.getClassByName = function (e) {
  return exports.bufferCache[e];
};
var exp_GameUtil = function () {
  function _ctor() {}
  _ctor.getLogTimeDesc = function (e) {
    if (e < 60) {
      return cc.js.formatStr("%d秒前", e);
    } else {
      if (e < 3600) {
        return cc.js.formatStr("%d分钟前", Math.floor(e / 60));
      } else {
        if (e < 86400) {
          return cc.js.formatStr("%d小时前", Math.floor(e / 3600));
        } else {
          return cc.js.formatStr("%d天前", Math.floor(e / 3600 / 24));
        }
      }
    }
  };
  _ctor.setListener = function (e, t, o) {
    null != t && cc.isValid(e) && e.on("click", t, o);
  };
  _ctor.dateFormat = function (e) {
    var t = new Date();
    t.setTime(e);
    var o = t.getFullYear();
    var i = t.getMonth();
    var n = t.getDate();
    ++i < 10 && (i = "0" + i);
    n < 10 && (n = "0" + n);
    return o + "/" + i + "/" + n;
  };
  _ctor.getNowFormatDate = function (e) {
    undefined === e && (e = null);
    e && 10 == (e + "").length && (e *= 1e3);
    var t = new Date(e || $2Time.Time.serverTimeMs);
    var o = t.getFullYear();
    var i = t.getMonth() + 1;
    var n = t.getDate();
    var r = i;
    var a = n;
    i < 10 && (r = "0" + i);
    n < 10 && (a = "0" + n);
    return {
      year: o,
      monthStr: r,
      dateStr: a,
      currentdate: o + "-" + r + "-" + a
    };
  };
  _ctor.formatSeconds = function (e) {
    if (e < 0 || !e) {
      return {
        str: "00:00:00",
        s: "00",
        m: "00",
        h: "00"
      };
    }
    var t = parseInt(e);
    var o = 0;
    var i = 0;
    if (t > 60) {
      o = t / 60;
      t %= 60;
      if (o > 60) {
        i = o / 60, o %= 60;
      }
    }
    i = Math.floor(i);
    o = Math.floor(o);
    var n = (t = Math.floor(t)) + "秒";
    o > 0 && (n = o + "分" + n);
    i > 0 && (n = i + "小时" + n);
    var r = {
      s: t < 10 ? "0" + t : t,
      m: o < 10 ? "0" + o : o,
      h: i < 10 ? "0" + i : i
    };
    return {
      s: r.s,
      m: r.m,
      h: r.h,
      str: r.h + ":" + r.m + ":" + r.s,
      result: n
    };
  };
  _ctor.AngleToRadinas = function (e) {
    return e * (Math.PI / 180);
  };
  _ctor.GetAngle = function (e, t) {
    undefined === t && (t = cc.Vec2.ZERO);
    return Math.round(Math.atan2(e.y - t.y, e.x - t.x) * (180 / Math.PI) + 360) % 360;
  };
  _ctor.GeAngletDir = function (e) {
    var t = e * Math.PI / 180;
    return cc.v2(Math.cos(t), Math.sin(t));
  };
  _ctor.RadinasToAngle = function (e) {
    return 180 * e / Math.PI;
  };
  _ctor.AngleAndLenToPos = function (e, t) {
    undefined === t && (t = 1);
    cc.Vec2.UP.rotate(cc.misc.degreesToRadians(e), f);
    cc.Vec2.multiplyScalar(f, f, t);
    return f;
  };
  _ctor.getDistance = function (e, t) {
    return Math.sqrt(Math.pow(e.x - t.x, 2) + Math.pow(e.y - t.y, 2));
  };
  _ctor.getDistanceSqrt = function (e, t) {
    return Math.pow(e.x - t.x, 2) + Math.pow(e.y - t.y, 2);
  };
  _ctor.random = function (e, t) {
    return e + Math.floor(Math.random() * (t - e));
  };
  _ctor.weight = function (e) {
    e < 1 && (e *= 100);
    return this.random(0, 100) < e;
  };
  _ctor.weightFloat = function (e) {
    return this.random(0, 100) < 100 * e;
  };
  _ctor.weightGetValue = function (e, t, o) {
    undefined === t && (t = "w");
    undefined === o && (o = 1);
    var i;
    var n = 0;
    if (!e) {
      return null;
    }
    var r = [];
    for (i = 0; i < e.length; i++) {
      if (0 != e[i][t]) {
        n += e[i][t];
        r.push(e[i]);
      }
    }
    var a = this.random(0, n);
    for (i = 0; i < r.length; i++) {
      var s = r[i][t] * o;
      if (a <= s) {
        return r[i];
      }
      a -= s;
    }
    return r[0];
  };
  _ctor.arrToWeight = function (e) {
    var t = [];
    e.forEach(function (e) {
      var o = {
        id: e[0],
        w: e[1]
      };
      t.push(o);
    });
    return t;
  };
  _ctor.weightGetList = function (e, t, o, i) {
    undefined === o && (o = "w");
    if (e.length < t) {
      return e.filter(function (e) {
        return e[o] > 0;
      });
    }
    var n = {
      totalW: e.reduce(function (e, t) {
        return e + Number(t[o]);
      }, 0),
      selected: [],
      seen: new Set()
    };
    if (!n.totalW || "number" != typeof n.totalW) {
      cc.error("权重列表出错:", n, e);
      return [];
    }
    for (; n.selected.length < t;) {
      var r = 0;
      for (var a = e; r < a.length; r++) {
        var s = a[r];
        i && i(s, n);
        if (this.random(0, n.totalW) < Number(s[o]) && !n.seen.has(s.id)) {
          n.selected.push(s);
          n.seen.add(s.id);
          break;
        }
      }
    }
    return n.selected;
  };
  _ctor.secondsUntilNextDay = function () {
    var e = new Date();
    var t = e.getFullYear();
    var o = e.getMonth();
    var i = e.getDate();
    var n = new Date(t, o, i + 1).getTime() - e.getTime();
    return Math.floor(n / 1e3);
  };
  _ctor.hasIntersection = function (e, t) {
    return e.reduce(function (e, o) {
      return e || t.includes(o);
    }, false);
  };
  _ctor.intersectionSize = function (e, t) {
    var o = new Set(t);
    return e.filter(function (e) {
      return o.has(e);
    }).length;
  };
  _ctor.getRandomIndex = function (e) {
    var t = e.reduce(function (e, t) {
      var o = (e.length > 0 ? e[e.length - 1] : 0) + t;
      e.push(o);
      return e;
    }, []);
    var o = t[t.length - 1];
    var i = Math.random() * o;
    return t.findIndex(function (e) {
      return i < e;
    });
  };
  _ctor.getRandomIndex2 = function (e) {
    var t = 0;
    e.forEach(function (e) {
      return t += e;
    });
    var o = [];
    var i = 0;
    for (var n = 0; n < t; n++) {
      o.push(e[i]);
      i == n && i++;
    }
    var r = this.random(0, t);
    return e.indexOf(o[r]);
  };
  _ctor.changeNumStr = function (e, t, o) {
    undefined === t && (t = 5);
    undefined === o && (o = 1);
    if (e.toString().length <= t) {
      return e + "";
    }
    var i;
    for (var n = [" ", "K", "M", "B", "T", "q", "Q", "s", "S", "O"]; (i = n.shift()) && e > 1e3;) {
      e /= 1e3;
    }
    var r = "";
    if (" " === i) {
      r = e + i;
    } else {
      "K" == i && (o = 0);
      r = e.toFixed(undefined === o ? 2 : o) + i;
    }
    return r;
  };
  _ctor.splitThousands = function (e, t) {
    undefined === t && (t = 0);
    "number" != typeof e && (e = parseFloat(e));
    t = null == t ? 2 : t;
    (e = e.toString().split("."))[0] = e[0].replace(/\B(?=(\d{3})+$)/g, ",");
    e[1] = e[1] ? e[1].substr(0, t) : "00000000000000000".substr(0, t);
    if (t) {
      return e.join(".");
    } else {
      return e[0];
    }
  };
  _ctor.changeSecondToClock = function (e, t) {
    undefined === t && (t = false);
    var o = parseInt((e / 3600).toString());
    var i = parseInt((e % 3600 / 60).toString());
    var n = parseInt((e % 60).toString());
    var r = "";
    if (0 == o) {
      t && (r += "00:");
    } else if (o > 0 && o < 10) {
      r += "0" + o;
      r += ":";
    } else {
      r += o;
      r += ":";
    }
    r += 0 == i ? "00" : i > 0 && i < 10 ? "0" + i : i;
    return (r += ":") + (0 == n ? "00" : n > 0 && n < 10 ? "0" + n : n);
  };
  _ctor.convertNodePostionToOther = function (e, t) {
    if (!e.parent) {
      return exports.MyVec3.ZERO;
    }
    var i = e.parent.convertToWorldSpaceAR(e.position);
    return t.convertToNodeSpaceAR(i);
  };
  _ctor.getRandomListDiffArray = function (t, o) {
    var i;
    var n = [];
    var r = 0;
    for (var a = o - t + 1; r < a; r++) {
      n.push(r + t);
    }
    var s = 0;
    for (r = o - t; r > 0; r--) {
      s = _ctor.random(0, r);
      i = [n[r], n[s]];
      n[s] = i[0];
      n[r] = i[1];
    }
    return n;
  };
  _ctor.getComponent = function (e, t) {
    var o = e.getComponent(t);
    o || (o = e.addComponent(t));
    return o;
  };
  _ctor.prefixZero = function (e, t) {
    return (Array(t).join("0") + e).slice(-t);
  };
  _ctor.createHandler = function (e, t, o) {
    var i = new cc.Component.EventHandler();
    i.handler = t;
    i.target = e;
    i.component = o;
    return i;
  };
  _ctor.getRandomSDiff = function (t, o, i) {
    if (o - t + 1 <= i) {
      var n = [];
      for (var r = t; r <= o; r++) {
        n.push(r);
      }
      return n;
    }
    var a = new Array();
    var s = o - t + 1;
    for (r = 0; r < s; r++) {
      a[r] = t + r;
    }
    _ctor.getRandomSDiffInArray(a, i);
    return a;
  };
  _ctor.getRandomSDiffInArray = function (e, t) {
    var o;
    var i = e.length;
    if (e.length < t) {
      return e;
    }
    if (1 == e.length) {
      return e;
    }
    for (var n = 0; n < t; n++) {
      var r = this.random(n, i);
      o = [e[r], e[n]];
      e[n] = o[0];
      e[r] = o[1];
    }
    e.length = t;
    return e;
  };
  _ctor.getRandomInArray = function (e, t) {
    undefined === t && (t = 1);
    var o = this.getRandomSDiff(0, e.length - 1, t);
    var i = [];
    for (var n = 0; n < o.length; n++) {
      i.push(e[o[n]]);
    }
    return i;
  };
  _ctor.randomArr = function (e) {
    var t = this.getRandomSDiff(0, e.length - 1, 1);
    var o = [];
    for (var i = 0; i < t.length; i++) {
      o.push(e[t[i]]);
    }
    return o[0];
  };
  _ctor.getRandomByWeightInArray = function (e, t) {
    var o = [];
    for (var i = 0; i < e[0].length; i++) {
      var n = e[1][i];
      var r = e[0][i];
      for (var a = 0; a < n; a++) {
        o.push(r);
      }
    }
    return this.getRandomNumbersFromArray(o, t);
  };
  _ctor.getRandomByWeightInList = function (e, t, o) {
    undefined === t && (t = 1);
    undefined === o && (o = 1);
    var i = [];
    e.forEach(function (e) {
      i.push({
        id: e[0],
        w: e[o],
        val: cc__spreadArrays(e)
      });
    });
    return this.weightGetList(i, t);
  };
  _ctor.getRandomNumbersFromArray = function (e, t) {
    var o;
    var i = e.slice();
    var n = [];
    for (var r = i.length - 1; r > 0; r--) {
      var a = Math.floor(Math.random() * (r + 1));
      o = [i[a], i[r]];
      i[r] = o[0];
      i[a] = o[1];
    }
    var s = 0;
    for (var c = i; s < c.length; s++) {
      var l = c[s];
      if (n.length === t) {
        break;
      }
      n.includes(l) || n.push(l);
    }
    return n;
  };
  _ctor.fixToBoundSize = function (e, t, o, i) {
    var n = new cc.Size(e, t);
    var r = Math.min(e, o);
    var a = Math.min(t, i);
    var s = r;
    var c = r / e * t;
    var l = a / t * e;
    var u = a;
    if (c > a) {
      n.width = l;
      n.height = u;
    } else {
      n.width = s;
      n.height = c;
    }
    return n;
  };
  _ctor.compareVersion = function (e, t) {
    e = e.split(".");
    t = t.split(".");
    for (var o = Math.max(e.length, t.length); e.length < o;) {
      e.push("0");
    }
    for (; t.length < o;) {
      t.push("0");
    }
    for (var i = 0; i < o; i++) {
      var n = parseInt(e[i]);
      var r = parseInt(t[i]);
      if (n > r) {
        return 1;
      }
      if (n < r) {
        return -1;
      }
    }
    return 0;
  };
  _ctor.cocosToWx = function (t) {
    var o = cc.Vec2.ZERO;
    if (t instanceof cc.Node) {
      var i = t.parent.convertToWorldSpaceAR(t.position);
      o.x = i.x;
      o.y = i.y;
    } else {
      o.x = t.x;
      o.y = t.y;
    }
    var n = _ctor.getRealDesignSize();
    o.y += (n.height - 1334) / 2;
    o.y = n.height - o.y;
    var r = wx.getSystemInfoSync();
    var a = r.screenWidth;
    var s = r.screenHeight;
    return cc.v2(a / n.width * o.x, s / n.height * o.y);
  };
  _ctor.getRealDesignSize = function () {
    var e = Math.min(cc.view.getCanvasSize().width / 750, cc.view.getCanvasSize().height / 1334);
    var t = 750 * e;
    var o = 1334 * e;
    var i = cc.view.getCanvasSize().width / t;
    var n = cc.view.getCanvasSize().height / o;
    var r = Math.round(750 * i);
    var a = Math.round(1334 * n);
    this.getDesignSize.height = a;
    this.getDesignSize.width = r;
    this.offsetSize = cc.v2(cc.winSize.width / 2, cc.winSize.height / 2);
    return {
      height: a,
      width: r,
      radioHeight: n,
      radioWidth: i
    };
  };
  _ctor.getScenePosition = function (e) {
    var t = this.getRealDesignSize();
    var o = e.parent.convertToWorldSpaceAR(e.position);
    o.x *= t.radioWidth;
    o.y *= t.radioHeight;
    o.addSelf(cc.v2(-t.width / 2, -t.height / 2));
    return o;
  };
  _ctor.deepCopy = function (t, o) {
    undefined === o && (o = []);
    if (null === t || "object" != typeof t) {
      return t;
    }
    var i;
    i = function (e) {
      return e.original === t;
    };
    var n = o.filter(i)[0];
    if (n) {
      return n.copy;
    }
    var r = Array.isArray(t) ? [] : {};
    o.push({
      original: t,
      copy: r
    });
    Object.keys(t).forEach(function (i) {
      r[i] = _ctor.deepCopy(t[i], o);
    });
    return r;
  };
  _ctor.deCompressByLzstring = function (e) {
    return $2lzstring.decompressFromBase64(e);
  };
  _ctor.compressByLzstring = function (e) {
    return $2lzstring.compressToBase64(e);
  };
  _ctor.Vec2Perp = function (e, t) {
    cc.Vec2.set(e, -t.y, t.x);
    return e;
  };
  _ctor.Vec2Sign = function (e, t) {
    if (e.y * t.x > e.x * t.y) {
      return -1;
    } else {
      return 1;
    }
  };
  _ctor.Vec2Truncate = function (e, t) {
    if (t <= 0) {
      return cc.Vec2.ZERO;
    } else {
      return e.magSqr() > t * t && (cc.Vec2.normalize(e, e), cc.Vec2.multiplyScalar(e, e, t)), e;
    }
  };
  _ctor.changeNumStrToEn = function (e) {
    var t = e + "";
    var o = "-kmbtABCDEFGHIJMNOPQRXT";
    for (var i = 0; i < o.length; i++) {
      var n = Math.pow(10, 3 * i);
      if (!(e > n)) {
        return t;
      }
      var r = o[i];
      "-" != r && (t = +(e / n).toFixed(2) + r);
    }
    return t;
  };
  _ctor.sort = function (e, t) {
    var o;
    var i = false;
    var n = 0;
    for (var r = e.length - 1; n < r; n++) {
      var a = false;
      for (var s = 0; s < r - n; s++) {
        if (t(e[s], e[s + 1])) {
          o = [e[s + 1], e[s]];
          e[s] = o[0];
          e[s + 1] = o[1];
          a = true;
          i = true;
        }
      }
      if (!a) {
        break;
      }
    }
    return i;
  };
  _ctor.classificationArray = function (e, t) {
    var o = {};
    for (var i in e) {
      if (e[i]) {
        var n = e[i][t];
        if (o[n]) {
          o[n].push(e[i]);
        } else {
          o[n] = [e[i]];
        }
      }
    }
    return o;
  };
  _ctor.getBattlePower = function (e) {
    return Math.ceil((16 * e.attack * (1 + e.critPer / 1e3 * .4) * (1 + e.critRatio / 1e4 * .2) * (1 + e.hitRatio / 1e4 * .2) * (1 + e.ctrlRatio / 1e4 * .2) + e.hp * (1 + e.resitCrit / 1e3 * .4) * (1 + e.missRatio / 1e4 * .1) * (1 + e.resitCtrl / 1e4 * .2)) / 100);
  };
  _ctor.binarySearch = function (e, t, o) {
    var i = 0;
    var n = t.length - 1;
    for (var r = 0; i <= n;) {
      var a = o(e, t[r = Math.floor((i + n + 1) / 2)]);
      if (0 == a) {
        break;
      }
      if (a < 0) {
        n = r - 1;
      } else {
        i = r + 1;
      }
    }
    return r;
  };
  _ctor.getBattleSign = function (e, t) {
    var o = Object.keys(e);
    o = o.sort();
    var i = "";
    for (var n in o) {
      i += "&" + o[n] + "=" + e[o[n]];
    }
    i = i.substring(1);
    i += "&";
    i += t;
    return $2Md5.default.md5(i).toUpperCase();
  };
  _ctor.delayForEachAsync = function (e, t, o) {
    undefined === o && (o = 0);
    return cc__awaiter(this, undefined, undefined, function () {
      var i;
      var n;
      var r;
      return cc__generator(this, function (a) {
        switch (a.label) {
          case 0:
            i = 0;
            n = e.length;
            a.label = 1;
          case 1:
            if (i < n) {
              return r = e[i], t(r, i, e), [4, this.waitForSecends(o)];
            } else {
              return [3, 4];
            }
          case 2:
            a.sent();
            a.label = 3;
          case 3:
            i++;
            return [3, 1];
          case 4:
            return [2];
        }
      });
    });
  };
  _ctor.waitForSecends = function (e) {
    return cc__awaiter(this, undefined, undefined, function () {
      return cc__generator(this, function () {
        return [2, new Promise(function (t) {
          return setTimeout(t, 1e3 * e);
        })];
      });
    });
  };
  _ctor.setFocus = function (e, t) {
    undefined === t && (t = 2);
    e && (e.groupIndex = t);
  };
  _ctor.chkstrlen = function (e) {
    var t = 0;
    for (var o = 0; o < e.length; o++) {
      if (e.charCodeAt(o) > 255) {
        t += 2;
      } else {
        t++;
      }
    }
    return t;
  };
  _ctor.obj2Array = function (e) {
    var t = [];
    for (var o in e) {
      t.push(e[o]);
    }
    return t;
  };
  _ctor.degreesToRadians = function (e) {
    return e * Math.PI / 180;
  };
  _ctor.radiansToDegrees = function (e) {
    return 180 * e / Math.PI;
  };
  _ctor.clampf = function (e, t, o) {
    if (t > o) {
      var i = t;
      t = o;
      o = i;
    }
    if (e < t) {
      return t;
    } else {
      if (e < o) {
        return e;
      } else {
        return o;
      }
    }
  };
  _ctor.lerp = function (e, t, o) {
    return e + (t - e) * o;
  };
  _ctor.dateFormat2 = function (e, t) {
    if (null == e && null == t) {
      e = new Date();
      t = "yyyy-MM-dd HH:mm:ss";
    } else if ("string" == typeof e) {
      t = e;
      e = new Date();
    } else {
      undefined === t && (t = "yyyy-MM-dd HH:mm:ss");
    }
    var o = {
      y: e.getFullYear() + "",
      M: e.getMonth() + 1 + "",
      d: e.getDate() + "",
      H: e.getHours(),
      m: e.getMinutes() + "",
      s: e.getSeconds() + "",
      q: Math.floor((e.getMonth() + 3) / 3) + "",
      f: e.getMilliseconds() + ""
    };
    if (o.H > 12) {
      o.h = o.H - 12 + "";
    } else {
      o.h = o.H + "";
    }
    o.H += "";
    var i = "yMdHhmsqf";
    var n = "";
    var r = "";
    var a = 0;
    for (var s = 0; a < i.length; a++) {
      if (!((s = t.indexOf(i[a])) < 0)) {
        for (n = ""; s < t.length && t[s] == i[a]; s++) {
          n += i[a];
        }
        if (n.length > 0) {
          if (n.length == o[i[a]].length) {
            r = o[i[a]];
          } else if (n.length > o[i[a]].length) {
            r = "f" == i[a] ? o[i[a]] + this.charString("0", n.length - o[i[a]].length) : this.charString("0", n.length - o[i[a]].length) + o[i[a]];
          } else {
            switch (i[a]) {
              case "y":
                r = o[i[a]].substr(o[i[a]].length - n.length);
                break;
              case "f":
                r = o[i[a]].substr(0, n.length);
                break;
              default:
                r = o[i[a]];
            }
          }
          t = t.replace(n, r);
        }
      }
    }
    return t;
  };
  _ctor.charString = function (e, t) {
    for (var o = ""; t--;) {
      o += e;
    }
    return o;
  };
  _ctor.ToWord = function (e) {
    return Math.floor(e / 50);
  };
  _ctor.getRandomColor = function () {
    var e = Math.floor(256 * Math.random());
    var t = Math.floor(256 * Math.random());
    var o = Math.floor(256 * Math.random());
    return new cc.Color(e, t, o);
  };
  _ctor.deleteArrItem = function (e, t) {
    e.indexOf(t) >= 0 && e.splice(e.indexOf(t), 1);
  };
  _ctor.deleteArrItemFromAtr = function (e, t) {
    var o = e.findIndex(function (e) {
      for (var o in t) {
        if (e[o] != t[o]) {
          return false;
        }
      }
      return true;
    });
    o >= 0 && e.splice(o, 1);
  };
  _ctor.CheckSection = function (e, t, o) {
    return t >= e && t <= o;
  };
  _ctor.offsetSize = cc.Vec2.ZERO;
  _ctor.getDesignSize = cc.Size.ZERO;
  return _ctor;
}();
exports.GameUtil = exp_GameUtil;
(function (e) {
  var t = function (e) {
    function t() {
      return null !== e && e.apply(this, arguments) || this;
    }
    cc__extends(t, e);
    return t;
  }($2LanguageFun.LanguageFun);
  e.Language = t;
  var o = function () {
    function e() {}
    e.setGray = function (e) {
      e.getComponentInChildren(cc.Sprite);
    };
    return e;
  }();
  e.Material = o;
  var i = function () {
    function e() {}
    e.GetShaderScale = function () {
      if (wonderSdk.isNative) {
        return 10;
      } else {
        return 1;
      }
    };
    return e;
  }();
  e.System = i;
  var r = function () {
    function e(e) {
      this.limt = {
        default: 1
      };
      this.arr = {};
      this.UpCDFun = {};
      if (e) {
        for (var t in e) {
          this.limt[t] = e[t];
        }
        this.Init();
      }
    }
    e.prototype.Up = function (e) {
      undefined === e && (e = "default");
      this.UpCDFun[e] && (this.limt[e] = this.UpCDFun[e]());
      return this.limt[e];
    };
    e.prototype.Check = function (e) {
      undefined === e && (e = "default");
      return !this.arr[e] || this.arr[e] <= 0;
    };
    e.prototype.CheckStamp = function (e) {
      undefined === e && (e = "default");
      var t = $2Time.Time.serverTimeMs / 1e3;
      return t - (this.arr[e] || 0) > this.limt[e] && (this.arr[e] = t, true);
    };
    e.prototype.CheckAndSet = function (e) {
      undefined === e && (e = "default");
      return !!this.Check(e) && (this.Set(e), true);
    };
    e.prototype.CheckAndUpdate = function (e, t) {
      undefined === e && (e = "default");
      if (this.Check(e)) {
        return this.Set(e), true;
      } else {
        return this.arr[e] -= t, false;
      }
    };
    e.prototype.Set = function (e, t) {
      undefined === e && (e = "default");
      undefined === t && (t = this.limt[e]);
      this.arr[e] = t;
    };
    e.prototype.Clear = function (e) {
      undefined === e && (e = "default");
      this.arr[e] = 0;
    };
    e.prototype.Init = function () {
      for (var e in this.UpCDFun) {
        this.limt[e] = 1;
      }
      for (var e in this.limt) {
        this.Up(e);
      }
    };
    e.prototype.InitItem = function (e, t) {
      undefined === e && (e = "default");
      this.limt[e] = t;
    };
    e.prototype.Del = function (e) {
      delete this.arr[e];
      delete this.limt[e];
      delete this.UpCDFun[e];
    };
    e.prototype.OnUpdate = function (e) {
      for (var t in this.arr) {
        this.arr[t] -= e;
      }
    };
    e.prototype.Reset = function () {
      for (var e in this.arr) {
        this.arr[e] = this.limt[e];
      }
    };
    e.prototype.ClearAll = function () {
      for (var e in this.arr) {
        this.arr[e] = 0;
      }
    };
    return e;
  }();
  e.CDManage = r;
  var a = function () {
    function e() {}
    e.AddClick = function (e, t) {
      var o = this;
      e.off(cc.Node.EventType.TOUCH_START);
      e.off(cc.Node.EventType.TOUCH_END);
      e.on(cc.Node.EventType.TOUCH_START, function () {
        o.ClickID = e.uuid;
      });
      e.on(cc.Node.EventType.TOUCH_END, function () {
        o.ClickID == e.uuid && t(e);
      });
    };
    e.CreateAnimationClip = function (e, t, o) {
      undefined === o && (o = 20);
      var i = cc.AnimationClip.createWithSpriteFrames(t, o);
      i.wrapMode = cc.WrapMode.Loop;
      i.speed = 1;
      i.name = e;
      return i;
    };
    return e;
  }();
  e.UI = a;
})(exports.CCTool || (exports.CCTool = {}));