/**
 * M33_FightBuffView
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2ListenID = require('ListenID');
const $2VideoButton = require('VideoButton');
const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2ModeChainsModel = require('ModeChainsModel');
const $2AlertManager = require('AlertManager');
const $2Game = require('Game');
const $2BuffCardItem = require('BuffCardItem');
const $2MChains = require('MChains');

var i;
var cc__extends = __extends;
var cc__assign = __assign;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        skillRoot: {
            type: cc.Node,
            default: null
        },
        richTextArr: {
            type: [cc.RichText],
            default: []
        },
        buffCardBarPrefab: {
            type: cc.Prefab,
            default: null
        },
        mode: {
            get() {
                return $2ModeChainsModel.default.instance;
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        },
        param: {
            get() {
                return this._openArgs.param;,
            override: true
        1401,
            visible: false
        },
        type: {
            get() {
                return this.param.type || 1;
            },
            visible: false
        },
        poolData: {
            get() {
                return this.game.rVo.poolADMap[this.type];
            },
            visible: false
        },
        resetNum: {
            get() {
                var e;
                var t;
                return (null === (e = this.poolData) || undefined === e ? undefined : e.freeResetNum) || (null === (t = this.poolData) || undefined === t ? undefined : t.resetNum) || 0;
            },
            visible: false
        },
        resetNumDefault: {
            get() {
                return this.game.recordVo.defaultData.poolADMap[this.type].resetNum;
            },
            visible: false
        },
        getAllNum: {
            get() {
                var e;
                return (null === (e = this.poolData) || undefined === e ? undefined : e.getAll) || 0;
            },
            visible: false
        },
        getAllNumDefault: {
            get() {
                return this.game.recordVo.defaultData.poolADMap[this.type].getAll;
            },
            visible: false
        }
    },

    ctor: function () {
        this.skillRoot = null
        this.richTextArr = []
        this.buffCardBarPrefab = null
        this.itemList = []
        this.selectLimt = 1
        this.selectIDList = []
    },

    // use this for initialization
    onLoad: function () {
    },

    setInfo: function () {
        var e;
        var t = this.param.getPool();
        this.resetList(t);
        // this.labelArr[0].string = (null === (e = this.poolData) || undefined === e ? undefined : e.title) || ([$2MChains.MChains.poolType.DragonBall, $2MChains.MChains.poolType.HighBuff, $2MChains.MChains.poolType.InitialWeapon].includes(this.type) ? "请选择武器" : "选择强化属性");
    },

    resetList: function (e) {
        var t;
        var o = this;
        this.skillRoot.hideAllChildren();
        if (0 == e.length) {
        $2AlertManager.AlertManager.showNormalTips("欧皇!词条都被你抽完啦!");
        return void this.close();
        }
        this.selectIDList.length = 0;
        this.itemList.length = 0;
        this.bufflist = e.sort(function (e, t) {
        return e.isAd - t.isAd;
        });
        this.skillRoot.getComponent(cc.Layout).enabled = true;
        var i = this.bufflist.length;
        var n = function (e) {
        var i = r.bufflist[e];
        var n = $2Game.ModeCfg.Buff.get(i.id);
        var a = r.skillRoot.children[e] || cc.instantiate(r.buffCardBarPrefab).setAttribute({
        parent: r.skillRoot
        });
        a.setAttribute({
        scale: 0,
        active: true
        });
        var c = a.getComponent($2BuffCardItem.default);
        c.setInfo(n, i.isAd, e);
        c.eventScenc = "M" + $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode) + "_BuffView";
        r.itemList.push(c);
        r.scheduleOnce(function () {
        c.setClick(function () {
        o.selectItem(i.id);
        });
        }, .3);
        (!t || n.rarity > t.rarity) && (t = c);
        };
        var r = this;
        for (var a = 0; a < i; a++) {
        n(a);
        }
        this.scheduleOnce(function () {
        o.skillRoot.getComponent(cc.Layout).enabled = false;
        2 == $2Manager.Manager.vo.userVo.guideIndex && $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
        targetNode: t.node,
        descpos: 2
        });
        }, .3 * this.bufflist.length + .1);
        this.resetBtn();
    },

    selectItem: function (e) {
        this.selectIDList.push(e);
        if (this.selectIDList.length >= this.selectLimt) {
        this.itemList.forEach(function (e) {
        e.isCanClick = false;
        });
        this.selectIDList.splice(0, this.selectLimt).forEach(function (e) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_AddBuff, e);
        });
        this.close();
        }
        if (2 == $2Manager.Manager.vo.userVo.guideIndex) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
        $2Manager.Manager.vo.userVo.guideIndex = 11;
        }
    },

    resetBtn: function () {
        var e;
        var t;
        this.richTextArr[0].text = (null === (e = this.poolData) || undefined === e ? undefined : e.freeResetNum) ? cc.js.formatStr("剩余免费<color=#00ff00>%d</c>次", this.poolData.freeResetNum) : cc.js.formatStr("剩余次数<color=#00ff00>%d/%d</c>", this.resetNum, this.resetNumDefault);
        this.nodeArr[0].setActive(this.resetNum > 0 && this.bufflist.length > 2);
        this.nodeArr[0].getComponent($2VideoButton.default).isForFree = (null === (t = this.poolData) || undefined === t ? undefined : t.freeResetNum) > 0;
        this.richTextArr[1].text = cc.js.formatStr("剩余次数<color=#00ff00>%d/%d</c>", this.getAllNum, this.getAllNumDefault);
        this.nodeArr[1].setActive(this.getAllNum > 0 && this.bufflist.length > 1);
    },

    adResetAllBuff: function () {
        var e = true;
        if (this.poolData.freeResetNum > 0) {
        this.poolData.freeResetNum--;
        e = false;
        } else {
        this.poolData.resetNum--;
        }
        this.resetList(this.param.getPool(cc__assign(cc__assign({}, this.param), {
        isReset: true,
        isPay: e
        })));
        this.resetBtn();
    },

    adGetAllSkill: function () {
        this.bufflist.forEach(function (e) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_AddBuff, e.id);
        });
        this.poolData.getAll--;
        this.close();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
