Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.md5 = undefined;
exports.md5 = function (e) {
  function t(e, t) {
    return e << t | e >>> 32 - t;
  }
  function o(e, t) {
    var o;
    var i;
    var n;
    var r;
    var a;
    n = 2147483648 & e;
    r = 2147483648 & t;
    a = (1073741823 & e) + (1073741823 & t);
    if ((o = 1073741824 & e) & (i = 1073741824 & t)) {
      return 2147483648 ^ a ^ n ^ r;
    } else {
      if (o | i) {
        if (1073741824 & a) {
          return 3221225472 ^ a ^ n ^ r;
        } else {
          return 1073741824 ^ a ^ n ^ r;
        }
      } else {
        return a ^ n ^ r;
      }
    }
  }
  function i(e, t, o) {
    return e & t | ~e & o;
  }
  function n(e, t, o) {
    return e & o | t & ~o;
  }
  function r(e, t, o) {
    return e ^ t ^ o;
  }
  function a(e, t, o) {
    return t ^ (e | ~o);
  }
  function s(e, n, r, a, s, c, l) {
    e = o(e, o(o(i(n, r, a), s), l));
    return o(t(e, c), n);
  }
  function c(e, i, r, a, s, c, l) {
    e = o(e, o(o(n(i, r, a), s), l));
    return o(t(e, c), i);
  }
  function l(e, i, n, a, s, c, l) {
    e = o(e, o(o(r(i, n, a), s), l));
    return o(t(e, c), i);
  }
  function u(e, i, n, r, s, c, l) {
    e = o(e, o(o(a(i, n, r), s), l));
    return o(t(e, c), i);
  }
  function p(e) {
    var t;
    var o = "";
    var i = "";
    for (t = 0; 3 >= t; t++) {
      o += (i = "0" + (e >>> 8 * t & 255).toString(16)).substr(i.length - 2, 2);
    }
    return o;
  }
  var f;
  var h;
  var d;
  var g;
  var y;
  var m;
  var _;
  var v;
  var M;
  var b;
  b = function (e) {
    var t;
    var o = e.length;
    var i = o + 8;
    var n = 16 * ((i - i % 64) / 64 + 1);
    var r = new Array(n - 1);
    var a = 0;
    for (var s = 0; o > s;) {
      a = s % 4 * 8;
      r[t = (s - s % 4) / 4] = r[t] | e.charCodeAt(s) << a;
      s++;
    }
    a = s % 4 * 8;
    r[t = (s - s % 4) / 4] = r[t] | 128 << a;
    r[n - 2] = o << 3;
    r[n - 1] = o >>> 29;
    return r;
  }(e = function (e) {
    e = e.replace(/\r\n/g, "\n");
    var t = "";
    for (var o = 0; o < e.length; o++) {
      var i = e.charCodeAt(o);
      if (128 > i) {
        t += String.fromCharCode(i);
      } else if (i > 127 && 2048 > i) {
        t += String.fromCharCode(i >> 6 | 192);
        t += String.fromCharCode(63 & i | 128);
      } else {
        t += String.fromCharCode(i >> 12 | 224);
        t += String.fromCharCode(i >> 6 & 63 | 128);
        t += String.fromCharCode(63 & i | 128);
      }
    }
    return t;
  }(e));
  m = 1732584193;
  _ = 4023233417;
  v = 2562383102;
  M = 271733878;
  for (f = 0; f < b.length; f += 16) {
    h = m;
    d = _;
    g = v;
    y = M;
    m = s(m, _, v, M, b[f + 0], 7, 3614090360);
    M = s(M, m, _, v, b[f + 1], 12, 3905402710);
    v = s(v, M, m, _, b[f + 2], 17, 606105819);
    _ = s(_, v, M, m, b[f + 3], 22, 3250441966);
    m = s(m, _, v, M, b[f + 4], 7, 4118548399);
    M = s(M, m, _, v, b[f + 5], 12, 1200080426);
    v = s(v, M, m, _, b[f + 6], 17, 2821735955);
    _ = s(_, v, M, m, b[f + 7], 22, 4249261313);
    m = s(m, _, v, M, b[f + 8], 7, 1770035416);
    M = s(M, m, _, v, b[f + 9], 12, 2336552879);
    v = s(v, M, m, _, b[f + 10], 17, 4294925233);
    _ = s(_, v, M, m, b[f + 11], 22, 2304563134);
    m = s(m, _, v, M, b[f + 12], 7, 1804603682);
    M = s(M, m, _, v, b[f + 13], 12, 4254626195);
    v = s(v, M, m, _, b[f + 14], 17, 2792965006);
    m = c(m, _ = s(_, v, M, m, b[f + 15], 22, 1236535329), v, M, b[f + 1], 5, 4129170786);
    M = c(M, m, _, v, b[f + 6], 9, 3225465664);
    v = c(v, M, m, _, b[f + 11], 14, 643717713);
    _ = c(_, v, M, m, b[f + 0], 20, 3921069994);
    m = c(m, _, v, M, b[f + 5], 5, 3593408605);
    M = c(M, m, _, v, b[f + 10], 9, 38016083);
    v = c(v, M, m, _, b[f + 15], 14, 3634488961);
    _ = c(_, v, M, m, b[f + 4], 20, 3889429448);
    m = c(m, _, v, M, b[f + 9], 5, 568446438);
    M = c(M, m, _, v, b[f + 14], 9, 3275163606);
    v = c(v, M, m, _, b[f + 3], 14, 4107603335);
    _ = c(_, v, M, m, b[f + 8], 20, 1163531501);
    m = c(m, _, v, M, b[f + 13], 5, 2850285829);
    M = c(M, m, _, v, b[f + 2], 9, 4243563512);
    v = c(v, M, m, _, b[f + 7], 14, 1735328473);
    m = l(m, _ = c(_, v, M, m, b[f + 12], 20, 2368359562), v, M, b[f + 5], 4, 4294588738);
    M = l(M, m, _, v, b[f + 8], 11, 2272392833);
    v = l(v, M, m, _, b[f + 11], 16, 1839030562);
    _ = l(_, v, M, m, b[f + 14], 23, 4259657740);
    m = l(m, _, v, M, b[f + 1], 4, 2763975236);
    M = l(M, m, _, v, b[f + 4], 11, 1272893353);
    v = l(v, M, m, _, b[f + 7], 16, 4139469664);
    _ = l(_, v, M, m, b[f + 10], 23, 3200236656);
    m = l(m, _, v, M, b[f + 13], 4, 681279174);
    M = l(M, m, _, v, b[f + 0], 11, 3936430074);
    v = l(v, M, m, _, b[f + 3], 16, 3572445317);
    _ = l(_, v, M, m, b[f + 6], 23, 76029189);
    m = l(m, _, v, M, b[f + 9], 4, 3654602809);
    M = l(M, m, _, v, b[f + 12], 11, 3873151461);
    v = l(v, M, m, _, b[f + 15], 16, 530742520);
    m = u(m, _ = l(_, v, M, m, b[f + 2], 23, 3299628645), v, M, b[f + 0], 6, 4096336452);
    M = u(M, m, _, v, b[f + 7], 10, 1126891415);
    v = u(v, M, m, _, b[f + 14], 15, 2878612391);
    _ = u(_, v, M, m, b[f + 5], 21, 4237533241);
    m = u(m, _, v, M, b[f + 12], 6, 1700485571);
    M = u(M, m, _, v, b[f + 3], 10, 2399980690);
    v = u(v, M, m, _, b[f + 10], 15, 4293915773);
    _ = u(_, v, M, m, b[f + 1], 21, 2240044497);
    m = u(m, _, v, M, b[f + 8], 6, 1873313359);
    M = u(M, m, _, v, b[f + 15], 10, 4264355552);
    v = u(v, M, m, _, b[f + 6], 15, 2734768916);
    _ = u(_, v, M, m, b[f + 13], 21, 1309151649);
    m = u(m, _, v, M, b[f + 4], 6, 4149444226);
    M = u(M, m, _, v, b[f + 11], 10, 3174756917);
    v = u(v, M, m, _, b[f + 2], 15, 718787259);
    _ = u(_, v, M, m, b[f + 9], 21, 3951481745);
    m = o(m, h);
    _ = o(_, d);
    v = o(v, g);
    M = o(M, y);
  }
  return (p(m) + p(_) + p(v) + p(M)).toLowerCase();
};