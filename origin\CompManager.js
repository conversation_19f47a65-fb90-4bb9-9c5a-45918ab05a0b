Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Game = require("Game");
var def_CompManager = function () {
  function _ctor() {
    this._compMap = new Map();
  }
  Object.defineProperty(_ctor, "Instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerComp = function (e) {
    if (this._compMap.has(e.ID)) {
      cc.warn("CompManager.registerComp repeat", e.ID);
    } else {
      this._compMap.set(e.ID, e);
    }
  };
  _ctor.prototype.getCompById = function (e) {
    return this._compMap.get(e);
  };
  _ctor.prototype.removeComp = function (e) {
    this._compMap.delete(e.ID);
  };
  Object.defineProperty(_ctor.prototype, "compMap", {
    get: function () {
      return this._compMap;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.clear = function () {
    this._compMap.clear();
  };
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onUpdate = function (e) {
    this._compMap.forEach(function (t) {
      t.onUpdate(e);
    });
  };
  _ctor._instance = null;
  return _ctor;
}();
exports.default = def_CompManager;