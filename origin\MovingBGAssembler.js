var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GTSimpleSpriteAssembler2D = require("GTSimpleSpriteAssembler2D");
var cc_gfx = cc.gfx;
var s = new cc_gfx.VertexFormat([{
  name: cc_gfx.ATTR_POSITION,
  type: cc_gfx.ATTR_TYPE_FLOAT32,
  num: 2
}, {
  name: cc_gfx.ATTR_UV0,
  type: cc_gfx.ATTR_TYPE_FLOAT32,
  num: 2
}, {
  name: cc_gfx.ATTR_UV1,
  type: cc_gfx.ATTR_TYPE_FLOAT32,
  num: 2
}, {
  name: "a_p",
  type: cc_gfx.ATTR_TYPE_FLOAT32,
  num: 2
}, {
  name: "a_q",
  type: cc_gfx.ATTR_TYPE_FLOAT32,
  num: 2
}]);
var c = cc.Vec2.ZERO;
var def_MovingBGAssembler = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.verticesCount = 4;
    t.indicesCount = 6;
    t.uvOffset = 2;
    t.uv1Offset = 4;
    t.uv2Offset = 6;
    t.uv3Offset = 8;
    t.floatsPerVert = 10;
    t.moveSpeed = c;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.initData = function () {
    var e = this._renderData;
    e.createFlexData(0, this.verticesCount, this.indicesCount, this.getVfmt());
    var t = e.iDatas[0];
    var o = t.length / 6;
    var i = 0;
    for (var n = 0; i < o; i++) {
      var r = 4 * i;
      t[n++] = r;
      t[n++] = r + 1;
      t[n++] = r + 2;
      t[n++] = r + 1;
      t[n++] = r + 3;
      t[n++] = r + 2;
    }
  };
  _ctor.prototype.getVfmt = function () {
    return s;
  };
  _ctor.prototype.getBuffer = function () {
    return cc.renderer._handle.getBuffer("mesh", this.getVfmt());
  };
  _ctor.prototype.updateColor = function () {};
  _ctor.prototype.updateUVs = function (t) {
    e.prototype.updateUVs.call(this, t);
    var o;
    var i = t._spriteFrame.uv;
    var n = this.uvOffset;
    var r = this.floatsPerVert;
    var a = this._renderData.vDatas[0];
    var s = i[0];
    var c = i[2];
    var l = i[5];
    var u = 1 / (c - s);
    var p = -s * u;
    var f = 1 / (i[1] - l);
    var h = -l * f;
    var d = this.moveSpeed.x;
    var g = this.moveSpeed.y;
    for (var y = 0; y < 4; ++y) {
      a[(o = r * y + n) + 2] = d;
      a[o + 3] = g;
      a[o + 4] = u;
      a[o + 5] = f;
      a[o + 6] = p;
      a[o + 7] = h;
    }
  };
  return _ctor;
}($2GTSimpleSpriteAssembler2D.default);
exports.default = def_MovingBGAssembler;