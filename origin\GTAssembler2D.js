var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var def_GTAssembler2D = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.verticesCount = 4;
    t.indicesCount = 6;
    t.floatsPerVert = 5;
    t.uvOffset = 2;
    t.colorOffset = 4;
    t._renderData = null;
    t._local = null;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.init = function (t) {
    e.prototype.init.call(this, t);
    this._renderData = new cc.RenderData();
    this._renderData.init(this);
    this.initLocal();
    this.initData();
  };
  Object.defineProperty(_ctor.prototype, "verticesFloats", {
    get: function () {
      return this.verticesCount * this.floatsPerVert;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.initData = function () {
    this._renderData.createQuadData(0, this.verticesFloats, this.indicesCount);
  };
  _ctor.prototype.initLocal = function () {
    this._local = [];
    this._local.length = 4;
  };
  _ctor.prototype.updateColor = function (e, t) {
    var o = this._renderData.uintVDatas[0];
    if (o) {
      t = null != t ? t : e.node.color._val;
      var i = this.floatsPerVert;
      var n = this.colorOffset;
      for (var r = o.length; n < r; n += i) {
        o[n] = t;
      }
    }
  };
  _ctor.prototype.getBuffer = function () {
    return cc.renderer._handle._meshBuffer;
  };
  _ctor.prototype.updateWorldVerts = function (e) {
    this.updateWorldVertsWebGL(e);
  };
  _ctor.prototype.updateWorldVertsWebGL = function (e) {
    var t = this._local;
    var o = this._renderData.vDatas[0];
    var i = e.node._worldMatrix.m;
    var n = i[0];
    var r = i[1];
    var a = i[4];
    var s = i[5];
    var c = i[12];
    var l = i[13];
    var u = t[0];
    var p = t[2];
    var f = t[1];
    var h = t[3];
    var d = 1 === n && 0 === r && 0 === a && 1 === s;
    var g = 0;
    var y = this.floatsPerVert;
    if (d) {
      o[g] = u + c;
      o[g + 1] = f + l;
      o[g += y] = p + c;
      o[g + 1] = f + l;
      o[g += y] = u + c;
      o[g + 1] = h + l;
      o[g += y] = p + c;
      o[g + 1] = h + l;
    } else {
      var m = n * u;
      var _ = n * p;
      var v = r * u;
      var M = r * p;
      var b = a * f;
      var C = a * h;
      var w = s * f;
      var S = s * h;
      o[g] = m + b + c;
      o[g + 1] = v + w + l;
      o[g += y] = _ + b + c;
      o[g + 1] = M + w + l;
      o[g += y] = m + C + c;
      o[g + 1] = v + S + l;
      o[g += y] = _ + C + c;
      o[g + 1] = M + S + l;
    }
  };
  _ctor.prototype.updateWorldVertsNative = function () {
    var e = this._local;
    var t = this._renderData.vDatas[0];
    var o = this.floatsPerVert;
    var i = e[0];
    var n = e[2];
    var r = e[1];
    var a = e[3];
    var s = 0;
    t[s] = i;
    t[s + 1] = r;
    t[s += o] = n;
    t[s + 1] = r;
    t[s += o] = i;
    t[s + 1] = a;
    t[s += o] = n;
    t[s + 1] = a;
  };
  _ctor.prototype.fillBuffers = function (e, t) {
    t.worldMatDirty && this.updateWorldVerts(e);
    var o = this._renderData;
    var i = o.vDatas[0];
    var n = o.iDatas[0];
    var r = this.getBuffer();
    var a = r.request(this.verticesCount, this.indicesCount);
    var s = a.byteOffset >> 2;
    var c = r._vData;
    if (i.length + s > c.length) {
      c.set(i.subarray(0, c.length - s), s);
    } else {
      c.set(i, s);
    }
    var l = r._iData;
    var u = a.indiceOffset;
    var p = a.vertexOffset;
    var f = 0;
    for (var h = n.length; f < h; f++) {
      l[u++] = p + n[f];
    }
  };
  _ctor.prototype.packToDynamicAtlas = function (e, t) {
    if (!t._original && cc.dynamicAtlasManager && t._texture.packable) {
      var o = cc.dynamicAtlasManager.insertSpriteFrame(t);
      o && t._setDynamicAtlasFrame(o);
    }
    var i = e._materials[0];
    if (i && i.getProperty("texture") !== t._texture) {
      e._vertsDirty = true;
      e._updateMaterial();
    }
  };
  _ctor.prototype.updateUVs = function () {
    var e = [0, 0, 1, 0, 0, 1, 1, 1];
    var t = this.uvOffset;
    var o = this.floatsPerVert;
    var i = this._renderData.vDatas[0];
    for (var n = 0; n < 4; n++) {
      var r = 2 * n;
      var a = o * n + t;
      i[a] = e[r];
      i[a + 1] = e[r + 1];
    }
  };
  _ctor.prototype.updateVerts = function (e) {
    var t;
    var o;
    var i;
    var n;
    var r = e.node;
    var a = r.width;
    var s = r.height;
    var c = r.anchorX * a;
    var l = r.anchorY * s;
    t = -c;
    o = -l;
    i = a - c;
    n = s - l;
    var u = this._local;
    u[0] = t;
    u[1] = o;
    u[2] = i;
    u[3] = n;
    this.updateWorldVerts(e);
  };
  _ctor.prototype.updateRenderData = function (e) {
    if (e._vertsDirty) {
      this.updateUVs(e);
      this.updateVerts(e);
      e._vertsDirty = false;
    }
  };
  return _ctor;
}(cc.Assembler);
exports.default = def_GTAssembler2D;