var o = function () {
  var e = String.fromCharCode;
  var t = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
  var o = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$";
  var i = {};
  function n(e, t) {
    if (!i[e]) {
      i[e] = {};
      for (var o = 0; o < e.length; o++) {
        i[e][e.charAt(o)] = o;
      }
    }
    return i[e][t];
  }
  var r = {
    compressToBase64: function (e) {
      if (null == e) {
        return "";
      }
      var o = r._compress(e, 6, function (e) {
        return t.charAt(e);
      });
      switch (o.length % 4) {
        default:
        case 0:
          return o;
        case 1:
          return o + "===";
        case 2:
          return o + "==";
        case 3:
          return o + "=";
      }
    },
    decompressFromBase64: function (e) {
      if (null == e) {
        return "";
      } else {
        if ("" == e) {
          return null;
        } else {
          return r._decompress(e.length, 32, function (o) {
            return n(t, e.charAt(o));
          });
        }
      }
    },
    compressToUTF16: function (t) {
      if (null == t) {
        return "";
      } else {
        return r._compress(t, 15, function (t) {
          return e(t + 32);
        }) + " ";
      }
    },
    decompressFromUTF16: function (e) {
      if (null == e) {
        return "";
      } else {
        if ("" == e) {
          return null;
        } else {
          return r._decompress(e.length, 16384, function (t) {
            return e.charCodeAt(t) - 32;
          });
        }
      }
    },
    compressToUint8Array: function (e) {
      var t = r.compress(e);
      var o = new Uint8Array(2 * t.length);
      var i = 0;
      for (var n = t.length; i < n; i++) {
        var a = t.charCodeAt(i);
        o[2 * i] = a >>> 8;
        o[2 * i + 1] = a % 256;
      }
      return o;
    },
    decompressFromUint8Array: function (t) {
      if (null == t) {
        return r.decompress(t);
      }
      var o = new Array(t.length / 2);
      var i = 0;
      for (var n = o.length; i < n; i++) {
        o[i] = 256 * t[2 * i] + t[2 * i + 1];
      }
      var a = [];
      o.forEach(function (t) {
        a.push(e(t));
      });
      return r.decompress(a.join(""));
    },
    compressToEncodedURIComponent: function (e) {
      if (null == e) {
        return "";
      } else {
        return r._compress(e, 6, function (e) {
          return o.charAt(e);
        });
      }
    },
    decompressFromEncodedURIComponent: function (e) {
      if (null == e) {
        return "";
      } else {
        if ("" == e) {
          return null;
        } else {
          return e = e.replace(/ /g, "+"), r._decompress(e.length, 32, function (t) {
            return n(o, e.charAt(t));
          });
        }
      }
    },
    compress: function (t) {
      return r._compress(t, 16, function (t) {
        return e(t);
      });
    },
    _compress: function (e, t, o) {
      if (null == e) {
        return "";
      }
      var i;
      var n;
      var r;
      var a = {};
      var s = {};
      var c = "";
      var l = "";
      var u = "";
      var p = 2;
      var f = 3;
      var h = 2;
      var d = [];
      var g = 0;
      var y = 0;
      for (r = 0; r < e.length; r += 1) {
        c = e.charAt(r);
        if (!Object.prototype.hasOwnProperty.call(a, c)) {
          a[c] = f++;
          s[c] = true;
        }
        l = u + c;
        if (Object.prototype.hasOwnProperty.call(a, l)) {
          u = l;
        } else {
          if (Object.prototype.hasOwnProperty.call(s, u)) {
            if (u.charCodeAt(0) < 256) {
              for (i = 0; i < h; i++) {
                g <<= 1;
                if (y == t - 1) {
                  y = 0;
                  d.push(o(g));
                  g = 0;
                } else {
                  y++;
                }
              }
              n = u.charCodeAt(0);
              for (i = 0; i < 8; i++) {
                g = g << 1 | 1 & n;
                if (y == t - 1) {
                  y = 0;
                  d.push(o(g));
                  g = 0;
                } else {
                  y++;
                }
                n >>= 1;
              }
            } else {
              n = 1;
              for (i = 0; i < h; i++) {
                g = g << 1 | n;
                if (y == t - 1) {
                  y = 0;
                  d.push(o(g));
                  g = 0;
                } else {
                  y++;
                }
                n = 0;
              }
              n = u.charCodeAt(0);
              for (i = 0; i < 16; i++) {
                g = g << 1 | 1 & n;
                if (y == t - 1) {
                  y = 0;
                  d.push(o(g));
                  g = 0;
                } else {
                  y++;
                }
                n >>= 1;
              }
            }
            if (0 == --p) {
              p = Math.pow(2, h);
              h++;
            }
            delete s[u];
          } else {
            n = a[u];
            for (i = 0; i < h; i++) {
              g = g << 1 | 1 & n;
              if (y == t - 1) {
                y = 0;
                d.push(o(g));
                g = 0;
              } else {
                y++;
              }
              n >>= 1;
            }
          }
          if (0 == --p) {
            p = Math.pow(2, h);
            h++;
          }
          a[l] = f++;
          u = String(c);
        }
      }
      if ("" !== u) {
        if (Object.prototype.hasOwnProperty.call(s, u)) {
          if (u.charCodeAt(0) < 256) {
            for (i = 0; i < h; i++) {
              g <<= 1;
              if (y == t - 1) {
                y = 0;
                d.push(o(g));
                g = 0;
              } else {
                y++;
              }
            }
            n = u.charCodeAt(0);
            for (i = 0; i < 8; i++) {
              g = g << 1 | 1 & n;
              if (y == t - 1) {
                y = 0;
                d.push(o(g));
                g = 0;
              } else {
                y++;
              }
              n >>= 1;
            }
          } else {
            n = 1;
            for (i = 0; i < h; i++) {
              g = g << 1 | n;
              if (y == t - 1) {
                y = 0;
                d.push(o(g));
                g = 0;
              } else {
                y++;
              }
              n = 0;
            }
            n = u.charCodeAt(0);
            for (i = 0; i < 16; i++) {
              g = g << 1 | 1 & n;
              if (y == t - 1) {
                y = 0;
                d.push(o(g));
                g = 0;
              } else {
                y++;
              }
              n >>= 1;
            }
          }
          if (0 == --p) {
            p = Math.pow(2, h);
            h++;
          }
          delete s[u];
        } else {
          n = a[u];
          for (i = 0; i < h; i++) {
            g = g << 1 | 1 & n;
            if (y == t - 1) {
              y = 0;
              d.push(o(g));
              g = 0;
            } else {
              y++;
            }
            n >>= 1;
          }
        }
        if (0 == --p) {
          p = Math.pow(2, h);
          h++;
        }
      }
      n = 2;
      for (i = 0; i < h; i++) {
        g = g << 1 | 1 & n;
        if (y == t - 1) {
          y = 0;
          d.push(o(g));
          g = 0;
        } else {
          y++;
        }
        n >>= 1;
      }
      for (;;) {
        g <<= 1;
        if (y == t - 1) {
          d.push(o(g));
          break;
        }
        y++;
      }
      return d.join("");
    },
    decompress: function (e) {
      if (null == e) {
        return "";
      } else {
        if ("" == e) {
          return null;
        } else {
          return r._decompress(e.length, 32768, function (t) {
            return e.charCodeAt(t);
          });
        }
      }
    },
    _decompress: function (t, o, i) {
      var n;
      var r;
      var a;
      var s;
      var c;
      var l;
      var u;
      var p = [];
      var f = 4;
      var h = 4;
      var d = 3;
      var g = "";
      var y = [];
      var m = {
        val: i(0),
        position: o,
        index: 1
      };
      for (n = 0; n < 3; n += 1) {
        p[n] = n;
      }
      a = 0;
      c = Math.pow(2, 2);
      for (l = 1; l != c;) {
        s = m.val & m.position;
        m.position >>= 1;
        if (0 == m.position) {
          m.position = o;
          m.val = i(m.index++);
        }
        a |= (s > 0 ? 1 : 0) * l;
        l <<= 1;
      }
      switch (a) {
        case 0:
          a = 0;
          c = Math.pow(2, 8);
          for (l = 1; l != c;) {
            s = m.val & m.position;
            m.position >>= 1;
            if (0 == m.position) {
              m.position = o;
              m.val = i(m.index++);
            }
            a |= (s > 0 ? 1 : 0) * l;
            l <<= 1;
          }
          u = e(a);
          break;
        case 1:
          a = 0;
          c = Math.pow(2, 16);
          for (l = 1; l != c;) {
            s = m.val & m.position;
            m.position >>= 1;
            if (0 == m.position) {
              m.position = o;
              m.val = i(m.index++);
            }
            a |= (s > 0 ? 1 : 0) * l;
            l <<= 1;
          }
          u = e(a);
          break;
        case 2:
          return "";
      }
      p[3] = u;
      r = u;
      for (y.push(u);;) {
        if (m.index > t) {
          return "";
        }
        a = 0;
        c = Math.pow(2, d);
        for (l = 1; l != c;) {
          s = m.val & m.position;
          m.position >>= 1;
          if (0 == m.position) {
            m.position = o;
            m.val = i(m.index++);
          }
          a |= (s > 0 ? 1 : 0) * l;
          l <<= 1;
        }
        switch (u = a) {
          case 0:
            a = 0;
            c = Math.pow(2, 8);
            for (l = 1; l != c;) {
              s = m.val & m.position;
              m.position >>= 1;
              if (0 == m.position) {
                m.position = o;
                m.val = i(m.index++);
              }
              a |= (s > 0 ? 1 : 0) * l;
              l <<= 1;
            }
            p[h++] = e(a);
            u = h - 1;
            f--;
            break;
          case 1:
            a = 0;
            c = Math.pow(2, 16);
            for (l = 1; l != c;) {
              s = m.val & m.position;
              m.position >>= 1;
              if (0 == m.position) {
                m.position = o;
                m.val = i(m.index++);
              }
              a |= (s > 0 ? 1 : 0) * l;
              l <<= 1;
            }
            p[h++] = e(a);
            u = h - 1;
            f--;
            break;
          case 2:
            return y.join("");
        }
        if (0 == f) {
          f = Math.pow(2, d);
          d++;
        }
        if (p[u]) {
          g = p[u];
        } else {
          if (u !== h) {
            return null;
          }
          g = r + r.charAt(0);
        }
        y.push(g);
        p[h++] = r + g.charAt(0);
        r = g;
        if (0 == --f) {
          f = Math.pow(2, d);
          d++;
        }
      }
    }
  };
  return r;
}();
if ("function" == typeof define && define.amd) {
  define(function () {
    return o;
  });
} else {
  undefined !== module && null != module && (module.exports = o);
}