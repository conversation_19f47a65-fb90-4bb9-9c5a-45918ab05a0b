var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MVC = undefined;
var $2CallID = require("CallID");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2Time = require("Time");
var $2ResKeeper = require("ResKeeper");
var $2HttpClient = require("HttpClient");
var $2NetManager = require("NetManager");
var $2Notifier = require("Notifier");
(function (e) {
  var t = function () {
    function e() {}
    e.prototype.sort = function (e, t) {
      e.sort(function (e, o) {
        for (var i in t) {
          var n = t[i];
          var r = e[i];
          var a = o[i];
          if (null != r && null != a) {
            return n * (r - a);
          }
        }
        return 0;
      });
    };
    e.prototype.refreshdata = function () {};
    return e;
  }();
  e.BaseModel = t;
  var o = function () {
    function e() {
      h.add(this);
      this.registerAllProtocol();
    }
    e.prototype.changeListener = function () {};
    e.prototype.registerProtocol = function (e, t, o) {
      undefined === o && (o = 0);
      $2NetManager.NetManager.instance.setResponseHandler(e, t, this, o);
    };
    e.prototype.httpPost = function (e, t, o) {
      undefined === o && (o = "json");
      return $2HttpClient.HttpClient.httpPost(e, t, o);
    };
    e.prototype.httpGet = function (e, t, o) {
      undefined === o && (o = "json");
      return $2HttpClient.HttpClient.httpGet(e, t, o);
    };
    return e;
  }();
  e.BaseController = o;
  var i = function (e) {
    function t() {
      return null !== e && e.apply(this, arguments) || this;
    }
    cc__extends(t, e);
    t.prototype.setup = function (e) {
      this._model = e;
      return true;
    };
    Object.defineProperty(t.prototype, "mode", {
      get: function () {
        return this._model;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "classname", {
      get: function () {
        return "MController";
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.reset = function () {};
    t.prototype.registerAllProtocol = function () {};
    Object.defineProperty(t.prototype, "isNewDay", {
      get: function () {
        return new Date($2Time.Time.serverTimeMs).getDate() != $2Manager.Manager.storage.getNumber(this.classname + "_Day", 0);
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.setDay = function () {
      var e = new Date($2Time.Time.serverTimeMs).getDate();
      $2Manager.Manager.storage.setNumber(this.classname + "_Day", e);
    };
    return t;
  }(o);
  e.MController = i;
  var h = function () {
    function e() {}
    e.add = function (t) {
      var o = t.classname;
      if (null == e.getInstance(o)) {
        e._container.push(t);
      } else {
        console.error("ControllerContainer.Add repeat:" + o);
      }
    };
    e.getInstance = function (t) {
      var o = 0;
      for (var i = e._container; o < i.length; o++) {
        var n = i[o];
        if (n.classname == t) {
          return n;
        }
      }
      return null;
    };
    e.reset = function () {
      var t = e._container.length;
      for (var o = 0; o < t; o++) {
        e._container[o].reset();
      }
    };
    e._container = [];
    return e;
  }();
  e.ControllerContainer = h;
  var d;
  var g;
  var y = function () {
    function e() {}
    Object.defineProperty(e, "loadAssetHandler", {
      get: function () {
        return e._loadAssetHandler;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e, "unloadAssetHandler", {
      get: function () {
        return e._unloadAssetHandler;
      },
      enumerable: false,
      configurable: true
    });
    e.initAssetHandler = function (t, o) {
      e._loadAssetHandler = t;
      e._unloadAssetHandler = o;
    };
    Object.defineProperty(e, "onOpenEvent", {
      get: function () {
        return e._onOpenEvent;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e, "onCloseEvent", {
      get: function () {
        return e._onCloseEvent;
      },
      enumerable: false,
      configurable: true
    });
    e.initUIEvent = function (t, o) {
      e._onOpenEvent = t;
      e._onCloseEvent = o;
    };
    return e;
  }();
  e.ViewHandler = y;
  (function (e) {
    e[e.Unload = 0] = "Unload";
    e[e.Loading = 1] = "Loading";
    e[e.Loaded = 2] = "Loaded";
  })(e.eLoadState || (e.eLoadState = {}));
  (function (e) {
    e[e.Scene = 1] = "Scene";
    e[e.Main = 2] = "Main";
    e[e.Panel = 3] = "Panel";
    e[e.Popup = 4] = "Popup";
    e[e.SubPopup = 5] = "SubPopup";
    e[e.Tips = 6] = "Tips";
    e[e.Guide = 7] = "Guide";
    e[e.Loading = 8] = "Loading";
    e[e.Max = 9] = "Max";
  })(d = e.eUILayer || (e.eUILayer = {}));
  (function (e) {
    e[e.Scene = 1] = "Scene";
    e[e.Panel = 2] = "Panel";
    e[e.Popup = 3] = "Popup";
    e[e.Guide = 4] = "Guide";
    e[e.None = 5] = "None";
  })(g = e.eUIQueue || (e.eUIQueue = {}));
  var m = function () {
    function e() {
      this._view = null;
    }
    e.prototype.init = function (e) {
      this._view = e;
    };
    e.prototype.show = function () {
      var e = this;
      this._view.node.active = true;
      setTimeout(function () {
        return e._view.showFinish();
      });
    };
    e.prototype.hide = function () {
      var e = this;
      this._view.node.active = false;
      setTimeout(function () {
        return e._view.hideFinish();
      });
    };
    return e;
  }();
  e.DefaultTransition = m;
  var _ = new Map();
  e.transition = function (e, t) {
    return function () {
      var o = cc._RF.peek();
      t = t || o && o.script;
      _.set(t, e);
    };
  };
  var v = new Map();
  e.uilayer = function (e, t) {
    return function () {
      var o = cc._RF.peek();
      t = t || o && o.script;
      v.set(t, e);
    };
  };
  var M = new Map();
  e.uiqueue = function (e, t) {
    return function () {
      var o = cc._RF.peek();
      t = t || o && o.script;
      M.set(t, e);
    };
  };
  var b = new Map();
  e.viewbgm = function (e) {
    return function () {
      var t = cc._RF.peek();
      b.set(t && t.script, e);
    };
  };
  e.getTransitionByView = function (e) {
    var t;
    var o = e.name;
    -1 != (t = e.name.indexOf("<")) && (o = e.name.substring(0, t));
    return _.get(o) || m;
  };
  e.getUILayerByView = function (e) {
    var t = e.name;
    var o = e.name.indexOf("<");
    -1 != o && (t = e.name.substring(0, o));
    return v.get(t) || d.Main;
  };
  e.getUIQueueByView = function (e) {
    var t;
    var o = e.name;
    -1 != (t = e.name.indexOf("<")) && (o = e.name.substring(0, t));
    return M.get(o) || g.None;
  };
  e.getViewBgmByView = function (e) {
    var t;
    var o = e.name;
    -1 != (t = e.name.indexOf("<")) && (o = e.name.substring(0, t));
    return b.get(o) || 0;
  };
  var C = function () {
    function e() {
      this._tab = 0;
      this._select = 0;
      this.dailyTime = 0;
      this._nodeGroup = 1;
      this._isNeedLoading = true;
    }
    Object.defineProperty(e.prototype, "tab", {
      get: function () {
        return this._tab;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.setTab = function (e) {
      this._tab = e;
      return this;
    };
    Object.defineProperty(e.prototype, "select", {
      get: function () {
        return this._select;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.setSelect = function (e) {
      this._select = e;
      return this;
    };
    Object.defineProperty(e.prototype, "callback", {
      get: function () {
        return this._callback;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.setCallback = function (e) {
      this._callback = e;
      return this;
    };
    Object.defineProperty(e.prototype, "onOpenCallback", {
      get: function () {
        return this._onOpenCallback;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.setOpenCallback = function (e) {
      this._onOpenCallback = e;
      return this;
    };
    Object.defineProperty(e.prototype, "context", {
      get: function () {
        return this._context;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.setContext = function (e) {
      this._context = e;
      return this;
    };
    Object.defineProperty(e.prototype, "param", {
      get: function () {
        return this._param;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.setParam = function (e) {
      this._param = e;
      return this;
    };
    e.prototype.setDailyTime = function (e) {
      this.dailyTime = e;
      return this;
    };
    e.prototype.reset = function () {
      this._tab = 0;
      this._select = 0;
      this._callback = null;
      this._context = null;
      this._param = null;
    };
    Object.defineProperty(e.prototype, "nodeGroup", {
      get: function () {
        return this._nodeGroup;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.setNodeGroup = function (e) {
      this._nodeGroup = e;
      return this;
    };
    Object.defineProperty(e.prototype, "isNeedLoading", {
      get: function () {
        return this._isNeedLoading;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.setIsNeedLoading = function (e) {
      this._isNeedLoading = e;
      return this;
    };
    return e;
  }();
  e.OpenArgs = C;
  e.openArgs = function () {
    return new C();
  };
  var w = function (e) {
    function t() {
      var t = null !== e && e.apply(this, arguments) || this;
      t._transition = null;
      t._assetPath = "";
      t._callComponent = null;
      t.BlockEvents = ["touchend"];
      t._uiQueue = g.None;
      t._uiMask = null;
      t.eventScene = "";
      t._isOpened = false;
      t._isShowed = false;
      t.callClose = false;
      t._closeCall = null;
      t._openArgs = null;
      return t;
    }
    cc__extends(t, e);
    t.prototype.init = function (e, t, o, i, n) {
      this._uiLayer = e;
      this._uiQueue = t;
      this._assetPath = i;
      this._transition = o;
      n && (this._viewBgm = n);
      this._preBgm = $2Manager.Manager.audio.currMusicId;
      var r = this.assetPath.split("/");
      this.eventScene = r[r.length - 1];
    };
    Object.defineProperty(t.prototype, "assetPath", {
      get: function () {
        return this._assetPath;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "uiLayer", {
      get: function () {
        return this._uiLayer;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "viewBgm", {
      get: function () {
        return this._viewBgm;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "uiQueue", {
      get: function () {
        return this._uiQueue;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "uiMask", {
      get: function () {
        return this._uiMask;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.setUIMaskActive = function (e) {
      this._uiMask && (this._uiMask.active = e);
    };
    t.prototype.setNodeInfo = function (e) {
      this.node.parent = e;
      this.node.width = e.width;
      this.node.height = e.height;
      this._transition.init(this);
      this._uiMask = this.node.getChildByName("UIMask");
      var t = this.node.getComponent($2ResKeeper.default);
      this._callComponent = this.node.getComponent("CallComponent");
      t || this.node.addComponent($2ResKeeper.default);
      if (!this._uiMask) {
        var o = new cc.Node();
        o.width = this.node.width;
        o.height = this.node.height;
        o.scale = this.node.scale;
        o.addComponent(cc.BlockInputEvents);
        this._uiMask = o;
        this.node.addChild(o, cc.macro.MAX_ZINDEX, "UIMask");
      }
    };
    Object.defineProperty(t.prototype, "isOpened", {
      get: function () {
        return this._isOpened;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.open = function () {
      var e;
      if (this._isOpened) {
        if (!this._isShowed) {
          this._isShowed = false;
          this.show();
        }
        this.setInfo();
        this._callComponent && this._callComponent.setInfo();
      } else {
        this.callClose = false;
        for (var t = 0; t < this.BlockEvents.length; t++) {
          this.node.on(this.BlockEvents[t], this.onClickFrame, this);
        }
        y.onOpenEvent(this);
        this.onOpen();
        null === (e = this._openArgs.onOpenCallback) || undefined === e || e.call(null, this);
        this._callComponent && this._callComponent.onOpen();
        this._isOpened = true;
        this.show();
        this.setInfo();
        this._callComponent && this._callComponent.setInfo();
      }
    };
    t.prototype.onBtn = function () {};
    Object.defineProperty(t.prototype, "isShowed", {
      get: function () {
        return this._isShowed;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.show = function () {
      if (!this._isShowed) {
        this._isShowed = true;
        this.setUIMaskActive(true);
        this.changeListener(true);
        this._callComponent && this._callComponent.changeListener(true);
        this._transition.show();
        this.onShow();
        this._callComponent && this._callComponent.onShow();
        this.viewBgm && $2Manager.Manager.audio.playMusic(this.viewBgm, true);
      }
    };
    t.prototype.showFinish = function () {
      this.setUIMaskActive(false);
      this.onShowFinish();
      this._callComponent && this._callComponent.onShowFinish();
      this.sendViewEvent("show");
    };
    t.prototype.sendViewEvent = function (e, t) {
      undefined === t && (t = this.eventScene);
      $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "View", {
        Type: e,
        Scene: t,
        Mode: $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode)
      });
    };
    t.prototype.hide = function () {
      if (this._isShowed) {
        this._isShowed = false;
        this.setUIMaskActive(true);
        this.changeListener(false);
        this._callComponent && this._callComponent.changeListener(false);
        this._transition.hide();
        this.onHide();
        this._callComponent && this._callComponent.onHide();
        this.sendViewEvent("hide");
      }
    };
    t.prototype.onUpdate = function () {};
    t.prototype.hideFinish = function () {
      this.onHideFinish();
      this._callComponent && this._callComponent.onHideFinish();
      this.callClose && this.toClose();
    };
    t.prototype.close = function (e) {
      var t;
      if (this._isOpened) {
        null === (t = this._openArgs.callback) || undefined === t || t.call(null, e ? 0 : 1);
        this._closeCall = e;
        this._isOpened = false;
        this.callClose = true;
        if (this.isShowed) {
          this.hide();
        } else {
          this.toClose();
        }
      }
    };
    t.prototype.toClose = function () {
      this.onClose();
      this._callComponent && this._callComponent.onClose();
      for (var e = 0; e < this.BlockEvents.length; e++) {
        this.node.off(this.BlockEvents[e], this.onClickFrame, this);
      }
      y.onCloseEvent(this);
      null != this.node && this.node.destroy();
      if (this._closeCall) {
        var t = this._closeCall;
        "function" == typeof t && setTimeout(function () {
          t();
        });
      }
    };
    t.prototype.setOpenArgs = function (e) {
      this._openArgs = e;
    };
    t.prototype.onClickFrame = function (e) {
      e.stopPropagation();
    };
    t.prototype.offTouch = function () {
      for (var e = 0; e < this.BlockEvents.length; e++) {
        this.node.off(this.BlockEvents[e], this.onClickFrame, this);
      }
    };
    t.prototype.onTouch = function () {
      for (var e = 0; e < this.BlockEvents.length; e++) {
        this.node.on(this.BlockEvents[e], this.onClickFrame, this);
      }
    };
    return t;
  }($2ResKeeper.default);
  e.BaseView = w;
})(exports.MVC || (exports.MVC = {}));