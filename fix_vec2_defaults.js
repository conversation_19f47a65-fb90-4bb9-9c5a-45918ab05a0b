/**
 * 修复output目录中JS文件的cc.Vec2默认值
 * 检查Properties中type为cc.Vec2的属性，将default值从null修改为new cc.Vec2()
 */

const fs = require('fs');
const path = require('path');

// 输出目录
const OUTPUT_DIR = './output';

/**
 * 检查并修复单个JS文件中的cc.Vec2默认值
 * @param {string} filePath 文件路径
 */
function fixVec2DefaultsInFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let modified = false;
        let newContent = content;

        // 正则表达式匹配properties块中的cc.Vec2属性
        // 匹配模式：属性名: { type: cc.Vec2, default: null }
        const vec2PropertyRegex = /(\s+)(\w+):\s*\{\s*type:\s*cc\.Vec2,\s*default:\s*null\s*\}/g;

        newContent = newContent.replace(vec2PropertyRegex, (match, indent, propertyName) => {
            console.log(`  修复cc.Vec2属性: ${propertyName}`);
            modified = true;
            return `${indent}${propertyName}: {\n${indent}    type: cc.Vec2,\n${indent}    default: new cc.Vec2()\n${indent}}`;
        });

        // 处理多行格式的cc.Vec2情况
        const multilineVec2Regex = /(\s+)(\w+):\s*\{\s*\n(\s+)type:\s*cc\.Vec2,\s*\n(\s+)default:\s*null\s*\n(\s+)\}/g;

        newContent = newContent.replace(multilineVec2Regex, (match, indent, propertyName, typeIndent, defaultIndent, closingIndent) => {
            console.log(`  修复多行cc.Vec2属性: ${propertyName}`);
            modified = true;
            return `${indent}${propertyName}: {\n${typeIndent}type: cc.Vec2,\n${defaultIndent}default: new cc.Vec2()\n${closingIndent}}`;
        });

        // 正则表达式匹配properties块中的[cc.Vec2]数组属性
        // 匹配模式：属性名: { type: [cc.Vec2], default: null }
        const vec2ArrayPropertyRegex = /(\s+)(\w+):\s*\{\s*type:\s*\[cc\.Vec2\],\s*default:\s*null\s*\}/g;

        newContent = newContent.replace(vec2ArrayPropertyRegex, (match, indent, propertyName) => {
            console.log(`  修复[cc.Vec2]数组属性: ${propertyName}`);
            modified = true;
            return `${indent}${propertyName}: {\n${indent}    type: [cc.Vec2],\n${indent}    default: []\n${indent}}`;
        });

        // 处理多行格式的[cc.Vec2]情况
        const multilineVec2ArrayRegex = /(\s+)(\w+):\s*\{\s*\n(\s+)type:\s*\[cc\.Vec2\],\s*\n(\s+)default:\s*null\s*\n(\s+)\}/g;

        newContent = newContent.replace(multilineVec2ArrayRegex, (match, indent, propertyName, typeIndent, defaultIndent, closingIndent) => {
            console.log(`  修复多行[cc.Vec2]数组属性: ${propertyName}`);
            modified = true;
            return `${indent}${propertyName}: {\n${typeIndent}type: [cc.Vec2],\n${defaultIndent}default: []\n${closingIndent}}`;
        });

        // 正则表达式匹配properties块中的cc.Size属性
        // 匹配模式：属性名: { type: cc.Size, default: null }
        const sizePropertyRegex = /(\s+)(\w+):\s*\{\s*type:\s*cc\.Size,\s*default:\s*null\s*\}/g;

        newContent = newContent.replace(sizePropertyRegex, (match, indent, propertyName) => {
            console.log(`  修复cc.Size属性: ${propertyName}`);
            modified = true;
            return `${indent}${propertyName}: {\n${indent}    type: cc.Size,\n${indent}    default: new cc.Size()\n${indent}}`;
        });

        // 处理多行格式的cc.Size情况
        const multilineSizeRegex = /(\s+)(\w+):\s*\{\s*\n(\s+)type:\s*cc\.Size,\s*\n(\s+)default:\s*null\s*\n(\s+)\}/g;

        newContent = newContent.replace(multilineSizeRegex, (match, indent, propertyName, typeIndent, defaultIndent, closingIndent) => {
            console.log(`  修复多行cc.Size属性: ${propertyName}`);
            modified = true;
            return `${indent}${propertyName}: {\n${typeIndent}type: cc.Size,\n${defaultIndent}default: new cc.Size()\n${closingIndent}}`;
        });

        // 正则表达式匹配properties块中的[cc.Size]数组属性
        // 匹配模式：属性名: { type: [cc.Size], default: null }
        const sizeArrayPropertyRegex = /(\s+)(\w+):\s*\{\s*type:\s*\[cc\.Size\],\s*default:\s*null\s*\}/g;

        newContent = newContent.replace(sizeArrayPropertyRegex, (match, indent, propertyName) => {
            console.log(`  修复[cc.Size]数组属性: ${propertyName}`);
            modified = true;
            return `${indent}${propertyName}: {\n${indent}    type: [cc.Size],\n${indent}    default: []\n${indent}}`;
        });

        // 处理多行格式的[cc.Size]情况
        const multilineSizeArrayRegex = /(\s+)(\w+):\s*\{\s*\n(\s+)type:\s*\[cc\.Size\],\s*\n(\s+)default:\s*null\s*\n(\s+)\}/g;

        newContent = newContent.replace(multilineSizeArrayRegex, (match, indent, propertyName, typeIndent, defaultIndent, closingIndent) => {
            console.log(`  修复多行[cc.Size]数组属性: ${propertyName}`);
            modified = true;
            return `${indent}${propertyName}: {\n${typeIndent}type: [cc.Size],\n${defaultIndent}default: []\n${closingIndent}}`;
        });

        if (modified) {
            fs.writeFileSync(filePath, newContent, 'utf8');
            console.log(`✓ 已修复文件: ${path.basename(filePath)}`);
            return true;
        } else {
            console.log(`- 无需修复: ${path.basename(filePath)}`);
            return false;
        }
    } catch (error) {
        console.error(`✗ 处理文件失败 ${filePath}:`, error.message);
        return false;
    }
}

/**
 * 处理output目录中的所有JS文件
 */
function processAllFiles() {
    if (!fs.existsSync(OUTPUT_DIR)) {
        console.error(`错误: 目录 ${OUTPUT_DIR} 不存在`);
        return;
    }

    const files = fs.readdirSync(OUTPUT_DIR);
    const jsFiles = files.filter(file => file.endsWith('.js'));

    console.log(`开始处理 ${jsFiles.length} 个JS文件...\n`);

    let processedCount = 0;
    let modifiedCount = 0;

    jsFiles.forEach(file => {
        const filePath = path.join(OUTPUT_DIR, file);
        console.log(`处理文件: ${file}`);
        
        const wasModified = fixVec2DefaultsInFile(filePath);
        processedCount++;
        if (wasModified) {
            modifiedCount++;
        }
        console.log('');
    });

    console.log(`\n处理完成!`);
    console.log(`总文件数: ${processedCount}`);
    console.log(`修改文件数: ${modifiedCount}`);
    console.log(`未修改文件数: ${processedCount - modifiedCount}`);
}

// 执行处理
if (require.main === module) {
    processAllFiles();
}

module.exports = {
    fixVec2DefaultsInFile,
    processAllFiles
};
