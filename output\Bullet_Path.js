/**
 * Bullet_Path
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameUtil = require('GameUtil');
const $2BulletBase = require('BulletBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var p = cc.v2();

exports.default = cc.Class({
    extends: $2BulletBase.default,

    properties: {
    },

    ctor: function () {
        this.path = []
    },

    // use this for initialization
    onLoad: function () {
    },

    set: function (e) {
        var t;
        this.path.length = 0;
        (t = this.path).push.apply(t, $2GameUtil.GameUtil.deepCopy(e));
    },

    onUpdate: function (t) {
        this._super(t);
        this._vo.lifeTime < 0 || this.isDead || 0 != this.path.length && (p.setVal(this.path[0].x, this.path[0].y), cc.Vec2.squaredDistance(p, this.position) < 400 && (this.path.splice(0, 1), p.setVal(this.path[0].x, this.path[0].y)), this.vo.shootDir.set(p.sub(this.position).normalize()), cc.Vec2.multiplyScalar(p, this._vo.shootDir, this.maxSpeed * t), cc.Vec2.add(p, this.position, p), this.setPosition(p), this.isBanRotate || 0 != this.isRotate || this.updateDir(t));
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
