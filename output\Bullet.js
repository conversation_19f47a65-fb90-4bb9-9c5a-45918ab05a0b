/**
 * Bullet
 * 组件类 - 从编译后的JS反编译生成
 */

const $2BulletBase = require('BulletBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var u = cc.v2();

exports.default = cc.Class({
    extends: $2BulletBase.default,

    properties: {
    },

    // use this for initialization
    onLoad: function () {
    },

    onUpdate: function (t) {
        this._super(t);
        if (!(this._vo.lifeTime < 0 || this.isDead)) {
        cc.Vec2.multiplyScalar(u, this._vo.shootDir, this.maxSpeed * t);
        cc.Vec2.add(u, this.position, u);
        this.setPosition(u);
        this.isBanRotate || 0 != this.isRotate || this.updateDir(t);
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
