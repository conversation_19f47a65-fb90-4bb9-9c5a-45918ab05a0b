var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2GameatrCfg = require("GameatrCfg");
var $2MVC = require("MVC");
var $2GameSeting = require("GameSeting");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2LevelMgr = require("LevelMgr");
var $2RecordVo = require("RecordVo");
var $2Game = require("Game");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var $2MChains = require("MChains");
var y = function (e) {
  function t() {
    var t;
    var o = null !== e && e.apply(this, arguments) || this;
    o.selectDiffType = $2GameSeting.GameSeting.DiffType.Ease;
    o.activityList = (t = {}, $2Cfg.Cfg.activity.forEach(function (e) {
      t[e.id] = new $2ModeBackpackHeroModel.ActivityPass(e);
    }), t);
    return o;
  }
  cc__extends(t, e);
  return t;
}($2RecordVo.RecordVo.Data);
var def_ModeChainsModel = function (e) {
  function _ctor() {
    var o;
    var i = e.call(this) || this;
    i.gameMode = $2Game.Game.Mode.CHAINS;
    i.isMovingBG = true;
    i.gmDiff = 0;
    i.poolMap = ((o = {})[$2MChains.MChains.poolType.NormalBuff] = function () {
      return i.cardPool.norBuffPool;
    }, o[$2MChains.MChains.poolType.HighBuff] = function () {
      return i.cardPool.adBuffPool;
    }, o[$2MChains.MChains.poolType.Pumpkin] = function () {
      return i.cardPool.adPool;
    }, o[$2MChains.MChains.poolType.DragonBall] = function () {
      return i.cardPool.greedGemPool;
    }, o[$2MChains.MChains.poolType.InitialWeapon] = function () {
      return i.cardPool.normalPool;
    }, o);
    null == _ctor._instance && (_ctor._instance = i);
    return i;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "rVo", {
    get: function () {
      return this.recordVo.vo;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "role", {
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.loginFinish = function () {
    this.recordVo = new $2RecordVo.RecordVo.Mgr("ModeChainsModel", function () {
      return new y();
    }, {
      isAutoSave: true,
      isAutoToform: false
    });
    this.levelVo = new $2LevelMgr.Level.Mgr("ModeChainsLevel", function () {
      return new $2LevelMgr.Level.Data();
    }, {
      isAutoSave: true,
      isAutoToform: false,
      onChange: function () {}
    });
  };
  Object.defineProperty(_ctor.prototype, "cardPool", {
    get: function () {
      var e = this;
      return $2Cfg.Cfg.BagModeSkillPool.findBy(function (t) {
        var o;
        return t.modeType == e.gameMode && (!e.game.miniGameCfg || (null === (o = t.lv) || undefined === o ? undefined : o.includes(e.game.miniGameCfg.lvid)));
      }) || $2Cfg.Cfg.BagModeSkillPool.find({
        modeType: this.gameMode
      });
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.fightBuffWidth = function (e, t, o) {
    var i = this;
    undefined === t && (t = {});
    undefined === o && (o = this.poolMap[e]());
    t.num || (t.num = 3);
    null == t.isBinding && (t.isBinding = true);
    var n = [];
    var s = o[0];
    var p = o[1];
    this.role.buffMgr.use(3042, false, function () {
      t.num += 1;
    });
    var f = this.role.buffMgr.getSpecificBuffAttr({
      skillID: 0
    }).getor($2GameatrCfg.GameatrDefine.buffWeight, 0);
    var y = [];
    var m = [];
    $2ModeBackpackHeroModel.default.instance.userEquipPack.forEach(function (e) {
      if (e.isFitOut) {
        var t = $2Cfg.Cfg.EquipLv.filter({
          equipId: e.id
        });
        t.forEach(function (t) {
          t.lv > e.lv && t.unlockBuff && m.push(t.unlockBuff);
        });
        t.forEach(function (e) {
          return e.skill.forEach(function (e) {
            return y.add(e);
          });
        });
      }
    });
    var _ = [];
    s.forEach(function (o, r) {
      var s = $2Game.ModeCfg.Buff.get(o);
      var d = i.role.buffMgr.get(o);
      if (!m.includes(o) && (!s.skillId || !t.isBinding || $2GameUtil.GameUtil.hasIntersection(i.role.skillMgr.skillIDs, s.skillId) || s.skillId.includes(0))) {
        if (d) {
          if (d.isMaxLayer) {
            return;
          }
          if (1 == s.isSelect) {
            return;
          }
        }
        s.skillId && _.push(o);
        if (s.attr.includes($2GameatrCfg.GameatrDefine.addSkill) && 11 != i.game.miniGameCfg.type && !s.value.find(function (e) {
          return y.includes(e[0]);
        })) {
          return;
        }
        var v = {
          id: o,
          w: p[r],
          isAd: 0
        };
        t.isReset && t.isPay && ![13].includes(s.id) && (v.w += $2Manager.Manager.vo.switchVo.dragonRefreshBuff[s.rarity - 3] || 0);
        f && e == $2MChains.MChains.poolType.NormalBuff && [$2GameSeting.GameSeting.RarityType.A, $2GameSeting.GameSeting.RarityType.S].includes(s.rarity) && (v.w += f);
        i.role.buffMgr.use(3043, true, function (e) {
          [$2GameSeting.GameSeting.RarityType.B, $2GameSeting.GameSeting.RarityType.A, $2GameSeting.GameSeting.RarityType.S].includes(s.rarity) && (v.w += e.cutVo.value[0][0]);
        });
        n.push(v);
      }
    });
    for (var v = n.length - 1; v >= 0; v--) {
      n[v].w <= 0 && $2GameUtil.GameUtil.deleteArrItem(n, n[v]);
    }
    var M = [];
    $2GameUtil.GameUtil.weightGetList(n, t.num).forEach(function (e) {
      M.push({
        id: e.id,
        isAd: e.isAd
      });
    });
    return M;
  };
  _ctor.prototype.getRewardlist = function (e) {
    return this.levelVo.getRewardlist(e);
  };
  _ctor._instance = null;
  return _ctor;
}($2MVC.MVC.BaseModel);
exports.default = def_ModeChainsModel;