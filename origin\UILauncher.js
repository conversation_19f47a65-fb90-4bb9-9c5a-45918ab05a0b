Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.UILauncher = undefined;
var $2UIManager = require("UIManager");
var $2MVC = require("MVC");
var $2Manager = require("Manager");
exports.UILauncher = function () {
  $2UIManager.UIManager.Init();
  $2MVC.MVC.ViewHandler.initAssetHandler($2Manager.Manager.loader.loadRes.bind($2Manager.Manager.loader), $2Manager.Manager.loader.releaseAsset.bind($2Manager.Manager.loader));
};