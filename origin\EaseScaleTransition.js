var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.EaseScaleTransition = undefined;
var $1$2MVC = require("MVC");
var exp_EaseScaleTransition = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._animInSpeed = .3;
    t._animOutSpeed = .15;
    t.maskbgOpacity = 155;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.init = function (e) {
    this._view = e;
    this.maskbgOpacity = this._view.node.children[0].opacity;
    if (this._view.node.children[1]) {
      this._view.node.children[1].width = this._view.node.width;
      this._view.node.children[1].height = this._view.node.height;
    }
  };
  _ctor.prototype.show = function () {
    var e = this;
    this._view.node.active = true;
    if (this._view.node.children[0]) {
      this._view.node.children[0].stopAllActions();
      cc.tween(this._view.node.children[0]).call(function () {
        e._view.node.children[0].opacity = 0;
      }).to(.15, {
        opacity: this.maskbgOpacity
      }, {
        easing: cc.easing.backOut
      }).start();
    }
    if (this._view.node.children[1]) {
      this._view.node.children[1].stopAllActions();
      cc.tween(this._view.node.children[1]).set({
        scale: .1
      }).to(this._animInSpeed, {
        scale: 1
      }, {
        easing: cc.easing.backOut
      }).call(function () {
        e.onFadeInFinish();
      }).start();
    } else {
      setTimeout(function () {
        return e._view.showFinish();
      });
    }
  };
  _ctor.prototype.hide = function () {
    var e = this;
    if (this._view.node.children[0]) {
      this._view.node.children[0].stopAllActions();
      cc.tween(this._view.node.children[0]).to(.15, {
        opacity: 0
      }, {
        easing: cc.easing.backIn
      }).start();
    }
    if (this._view.node.children[1]) {
      this._view.node.children[1].stopAllActions();
      cc.tween(this._view.node.children[1]).to(this._animOutSpeed, {
        scale: .1
      }, {
        easing: cc.easing.backIn
      }).call(function () {
        e.onFadeOutFinish();
      }).start();
    } else {
      this._view.node.active = false;
      setTimeout(function () {
        return e._view.hideFinish();
      });
    }
  };
  _ctor.prototype.onFadeInFinish = function () {
    this._view.showFinish();
  };
  _ctor.prototype.onFadeOutFinish = function () {
    this._view.node.active = false;
    this._view.hideFinish();
  };
  return _ctor;
}($1$2MVC.MVC.DefaultTransition);
exports.EaseScaleTransition = exp_EaseScaleTransition;