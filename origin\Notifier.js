var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Notifier = exports.PriorHighest = exports.PriorHigh = exports.PriorMiddle = exports.PriorLow = exports.PriorLowest = undefined;
var $2NotifyListener = require("NotifyListener");
var $2NotifyCaller = require("NotifyCaller");
exports.PriorLowest = -200;
exports.PriorLow = -100;
exports.PriorMiddle = 0;
exports.PriorHigh = 100;
exports.PriorHighest = 200;
var exp_Notifier = function () {
  function _ctor() {}
  Object.defineProperty(_ctor.prototype, "listener", {
    get: function () {
      return _ctor._listener;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.addListener = function (e, t, i, n) {
    undefined === n && (n = exports.PriorMiddle);
    this._listener.Register(e, t, i, n);
  };
  _ctor.removeListener = function (e, t, o) {
    this._listener.Unregister(e, t, o);
  };
  _ctor.changeListener = function (e, t, i, n, r) {
    undefined === r && (r = exports.PriorMiddle);
    if (e) {
      this.addListener(t, i, n, r);
    } else {
      this.removeListener(t, i, n);
    }
  };
  _ctor.send = function (e) {
    var t;
    var o = [];
    for (var n = 1; n < arguments.length; n++) {
      o[n - 1] = arguments[n];
    }
    (t = this._listener).Send.apply(t, cc__spreadArrays([e], o));
  };
  _ctor.isExist = function (e) {
    return this._listener.IsExist(e);
  };
  _ctor.addCall = function (e, t, o) {
    return this._caller.Register(e, t, o);
  };
  _ctor.removeCall = function (e, t, o) {
    return this._caller.Unregister(e, t, o);
  };
  _ctor.changeCall = function (e, t, o, i) {
    if (e) {
      this.addCall(t, o, i);
    } else {
      this.removeCall(t, o, i);
    }
  };
  _ctor.call = function (e) {
    var t;
    var o = [];
    for (var n = 1; n < arguments.length; n++) {
      o[n - 1] = arguments[n];
    }
    return (t = this._caller).Call.apply(t, cc__spreadArrays([e], o));
  };
  _ctor._listener = new $2NotifyListener.NotifyListener();
  _ctor._caller = new $2NotifyCaller.NotifyCaller();
  return _ctor;
}();
exports.Notifier = exp_Notifier;