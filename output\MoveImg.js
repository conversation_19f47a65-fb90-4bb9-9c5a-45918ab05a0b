/**
 * MoveImg
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Game = require('Game');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        refreshFrequency: {
            displayName: "刷新帧率",
            default: 20
        },
        rolePos: {
            get() {
                var e;
                if (null === (e = $2Game.Game.Mgr.instance.mainRole) || undefined === e) {
                return undefined;
                } else {
                return e.position;
                }
            },
            visible: false
        }
    },

    ctor: function () {
        this.refreshFrequency = 20
        this._date = 0
    },

    onLoad: function () {
        this._mal = this.node.getComponent(cc.Sprite).getMaterial(0);
    },

    update: function () {
        if ($2Game.Game.Mgr.instance.gameState == $2Game.Game.State.START) {
        this._date++;
        if (this._date > this.refreshFrequency && this.rolePos) {
        this._date = 0, this.node.position = this.rolePos, this._mal.setProperty("dir", cc.v2(49e-5 * this.rolePos.x, -49e-5 * this.rolePos.y));
        }
        }
    }
});
