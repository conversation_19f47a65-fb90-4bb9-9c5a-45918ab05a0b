/**
 * MCRole
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Cfg = require('Cfg');
const $2Notifier = require('Notifier');
const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2GameUtil = require('GameUtil');
const $2Buff = require('Buff');
const $2BaseEntity = require('BaseEntity');
const $2DragonBody = require('DragonBody');
const $2Role = require('Role');
const $2Game = require('Game');
const $2SkillManager = require('SkillManager');
const $2PropertyVo = require('PropertyVo');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');
const $2MCBoss = require('MCBoss');
const $2MChains = require('MChains');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Role.default,

    properties: {
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        },
        settingScale: {
            get() {
                return this.property.base.Scale;
            },
            visible: false
        },
        canFire: {
            get() {
                return this.game.bronMonsterMgr.cutStatus == $2MChains.MChains.RoundStatus.BATTLE;
            },
            visible: false
        },
        myData: {
            get() {
                return this._myData;
            },
            set(value) {
                var t;
                var o = this;
                this._myData = e;
                // if (e.spine) {
                //   this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
                //   return void (this.mySkeleton && (null === (t = this.mySkeleton) || undefined === t || t.clearTracks(), $2Manager.Manager.loader.loadSpine(e.spine, this.mySkeleton.node).then(function (e) {
                //     o.mySkeleton.reset(e);
                //     // o.attackAmList = o.mySkeleton.animations.filter(function (e) {
                //     //   return e.includes("attack");
                //     // });
                //     o.delayByGame(function () {
                //       o.onNewSize(o.roleNode.getContentSize());
                //     });
                //     o.firstSkill && (o.firstSkill.mySkeleton = o.mySkeleton);
                //     // o.setAnimation("idle", true);
                //     o.setAnimation("daiji", true);
                //     // if (o.game.passType == $2MChains.MChains.PassType.D360) {
                //     //   o.forwardDirection.setVal(0, -1);
                //     //   o.mySkeleton.setSkin("f");
                //     // } else {
                //     //   o.mySkeleton.setSkin("b");
                //     // }
                //   })));
                // }
                this.skillMgr.launchPoint.set(this.roleNode.position.add(cc.v2(0, this.roleNode.height / 2)));
            },
            visible: false
        },
        isCanRelive: {
            get() {
                return !this.game.passParam.isHighDiff && this.reliveNum > 0;
            },
            set(value) {
                this._isCanRelive = e;
            },
            visible: false
        },
        attackAm: {
            get() {
                if (this.game.passType == $2MChains.MChains.PassType.ForwardMoveExtend) {
                // return "attack";
                return "gongji";
                } else {
                return $2GameUtil.GameUtil.randomArr(this.attackAmList);
                }
            },
            visible: false
        },
        horDir: {
            get() {
                return this._horDir;
            },
            set(value) {
                this._horDir = e;
            },
            visible: false
        }
    },

    ctor: function () {
        this._myData = null
        this.reliveNum = $2Manager.Manager.vo.switchVo.dragonRevive
        this.attackAmList = ["attack", "attack2", "attack3"]
        this.attackAmList = ["gongji"]
        this.damagePanel = new $2GameSeting.TMap()
        this._bossHurtDt = 0
    },

    // use this for initialization
    onLoad: function () {
    },

    changeListener: function (t) {
        this._super(t);
        this.node.changeListener(t, $2ListenID.ListenID.Fight_SpawnHurt, this.onSpawnHurt, this);
    },

    onNewSize: function (t) {
        var o;
        this.node.setContentSize(t.width, t.height);
        this.collider.size = t;
        this.radius = .5 * t.width;
        this._haedPosition.setVal(0, t.height * this.scale);
        this._bodyPosition.setVal(0, t.height * this.scale / 2);
        this.collider.offset = this._bodyPosition;
        if (this.game.passType == $2MChains.MChains.PassType.D360) {
        this._haedPosition.setVal(0, t.height * this.scale * .7);
        this._bodyPosition.setVal(0, 0);
        }
        null === (o = this.skillMgr) || undefined === o || o.launchPoint.set(this._bodyPosition);
        this.game.lineNode.setPosition(this.position);
        this.botEffectBox.setAttribute({
        position: this._bodyPosition
        });
        this.topEffectBox.setAttribute({
        position: this._bodyPosition
        });
        this._super(t);
    },

    init: function () {
        this._super();
        this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
        this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
        this.entityType = $2BaseEntity.EntityType.Role;
        this.campType = $2BaseEntity.CampType.One;
        this.skillMgr.poolLimt = 99;
    },

    setRole: function () {
        var e = this;
        this.roleId = this.game.pathData.roleId || 30300;
        this.myData = $2Cfg.Cfg.RoleUnlock.get(this.roleId);
        this.property || (this.property = new $2PropertyVo.Property.Vo(this));
        this.property.set($2Cfg.Cfg.Role.find({
        roleId: this.roleId
        }));
        this.game.passParam.isHighDiff && (this.property.base.hp = this.property.cut.hp = .2 * this.property.base.hp);
        this.updateProperty();
        this.myData.startSkill && this.addSkill(this.myData.startSkill, true, true);
        this.myData.startBuff && this.buffMgr.add(this.myData.startBuff);
        this.forwardDirection.setVal(0, 1);
        this.initHp();
        this.game.createLifeBar(this, {}).then(function (t) {
        return t.isPermanent = e.game.passType == $2MChains.MChains.PassType.Move;
        });
    },

    behit: function (e) {
        if (!this.isDead && this.hurtMgr.checkHurt(e)) {
        this.curHp -= e.val;
        this.node.emit($2ListenID.ListenID.Fight_BeHit, e);
        this.materialTwinkle();
        this.game.showDamageDisplay(e, this.haedPosition);
        if (this.curHp <= 0) {
        this.toDead();
        wonderSdk.vibrate(0);
        }
        return e;
        }
    },

    materialTwinkle: function () {
        var e = this;
        if (this._mals) {
        this._mals.setProperty("setwhite", .6);
        this.delayByGame(function () {
        e._mals.setProperty("setwhite", 0);
        }, .1);
        }
    },

    setJoyStickPos: function (e) {
        this.isDead || this.toMove(e);
    },

    toDead: function () {
        if (!this.isDead) {
        this.node.emit($2ListenID.ListenID.Fight_Dead);
        this.isDead = true;
        this.game.sendEvent("BatchFail");
        if (this.isCanRelive) {
        this.reliveNum--, $2Notifier.Notifier.send($2ListenID.ListenID.Fight_OpenReliveView);
        } else {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, false);
        }
        }
    },

    relive: function () {
        this.game.clearAllBullet();
        this.buffMgr.clearDebuff();
        this.isDead = false;
        this.initHp();
        this.addBuff(9994);
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
    },

    onSkill: function (e) {
        if (e.mySkeleton) {
        // e.mySkeleton.playQueue([this.attackAm, "idle"], true);
        e.mySkeleton.playQueue([this.attackAm, "daiji"], true);
        } else {
        // this.mySkeleton.playQueue([this.attackAm, "idle"], true);
        this.mySkeleton.playQueue([this.attackAm, "daiji"], true);
        }
    },

    addSkill: function (e, t, o) {
        var i = $2GameUtil.GameUtil.deepCopy($2Game.ModeCfg.Skill.get(e));
        var n = $2Cfg.Cfg.EquipLv.getArray().filter(function (t) {
        return t.skill.includes(e);
        });
        if (n.length > 0) {
        var r = $2ModeBackpackHeroModel.default.instance.userEquipPack.getItem(n[0].equipId);
        var s = n.find(function (e) {
        return e.lv == r.lv;
        });
        i.dam = s.atk;
        }
        return this.addSkillByData(i, t, o);
    },

    addSkillByData: function (e, t, o) {
        var i;
        var n;
        var r = this.skillMgr.addByData(e, t, o);
        var a = {
        9100: "buildjg",
        9060: "build03e",
        9040: "build05e",
        91200: "kpbl",
        91220: "kpbl",
        91240: "kpbl",
        91260: "kpbl",
        91600: "sdkpbl",
        91620: "sdkpbl",
        91640: "sdkpbl",
        91660: "sdkpbl"
        };
        a[r.skillCfg.id] && $2Manager.Manager.loader.loadSpineNode("bones/role/" + a[r.skillCfg.id], {
        nodeAttr: {
        parent: this.node,
        x: (null === (i = r.skillCfg.releasePos) || undefined === i ? undefined : i[0]) || 0,
        y: (null === (n = r.skillCfg.releasePos) || undefined === n ? undefined : n[1]) || 0
        },
        spAttr: {
        isPlayerOnLoad: true,
        animation: "idle",
        loop: true
        }
        }).then(function (e) {
        r.mySkeleton = e;
        });
        this.damagePanel.add(r.skillCfg.id, 0);
        return r;
    },

    onSpawnHurt: function (e) {
        e.ownerSkill && this.damagePanel.add(e.ownerSkill.belongSkillID || e.ownerSkill.id, e.val);
    },

    setPosition: function (e) {
        if (this.limitSize) {
        e.x = cc.misc.clampf(e.x, this.limitSize[0], this.limitSize[1]);
        e.y = cc.misc.clampf(e.y, this.limitSize[2], this.limitSize[3]);
        }
        this.node.setPosition(e);
    },

    onCollisionEnter: function (e) {
        var t = e.comp;
        if (t instanceof $2DragonBody.default && t.curIndex) {
        var o = t.owerChains.getHurt();
        this.behit(o);
        } else {
        t instanceof $2MCBoss.MCBoss && this.behit(t.getHurt().setAttribute({
        hurCd: 1,
        hid: t.ID
        }));
        }
    },

    onCollisionStay: function (e) {
        var t = e.comp;
        t instanceof $2MCBoss.MCBoss && this.behit(t.getHurt().setAttribute({
        hurCd: 1,
        hid: t.ID
        }));
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
