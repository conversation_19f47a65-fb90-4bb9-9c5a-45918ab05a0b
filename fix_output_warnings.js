const fs = require('fs');
const path = require('path');

// 修复 output 目录中转换后文件的警告问题
class OutputWarningFixer {
    constructor() {
        this.outputDir = './output';
        this.scriptsDir = './scripts';
        this.fixedCount = 0;
        this.errors = [];
        
        // 从警告信息中提取的需要修复的问题
        this.propertyOverrideFixes = [
            { file: 'OrganismBase.js', property: 'horDir' },
            { file: 'FCircleCollider.js', property: 'type' },
            { file: 'FBoxCollider.js', property: 'type' },
            { file: 'FPolygonCollider.js', property: 'type' },
            { file: 'M33_FightBuffView.js', property: 'param' },
            { file: 'M33_FightScene.js', property: 'role' },
            { file: 'M33_FightUIView.js', property: 'game' },
            // 从警告信息中补充的其他属性隐藏问题
            { file: 'MCRole.js', property: 'myData' },
            { file: 'MCRole.js', property: 'isCanRelive' },
            { file: 'MCRole.js', property: 'settingScale' },
            { file: 'MCRole.js', property: 'game' },
            { file: 'MCRole.js', property: 'roleID' },
            { file: 'MCRole.js', property: 'saveData' },
            { file: 'MCRole.js', property: 'prorSkillId' },
            { file: 'MCRole.js', property: 'figureData' },
            { file: 'MCRole.js', property: 'figureNextData' },
            { file: 'MCRole.js', property: 'nextLv' }
        ];
        
        this.destroyFunctionFixes = [
            'BulletVo.js'
        ];
        
        this.arrayDefaultFixes = [
            { file: 'Launcher.js', property: 'logos' }
        ];
        
        this.typeDeclarationFixes = [
            { file: 'AutoAmTool.js', property: 'amTime' }
        ];
    }

    readFile(filePath) {
        try {
            return fs.readFileSync(filePath, 'utf8');
        } catch (error) {
            this.errors.push(`读取文件失败: ${filePath} - ${error.message}`);
            return null;
        }
    }

    writeFile(filePath, content) {
        try {
            fs.writeFileSync(filePath, content, 'utf8');
            return true;
        } catch (error) {
            this.errors.push(`写入文件失败: ${filePath} - ${error.message}`);
            return false;
        }
    }

    // 修复属性隐藏问题 - 添加 override: true
    fixPropertyOverride(content, propertyName) {
        // 匹配属性定义模式，支持多行
        const patterns = [
            // 模式1: property: { ... }
            new RegExp(`(\\s*${propertyName}\\s*:\\s*{[^}]*?)\\s*}`, 'gs'),
            // 模式2: property: { ... },
            new RegExp(`(\\s*${propertyName}\\s*:\\s*{[^}]*?)(\\s*},?)`, 'gs')
        ];
        
        let fixedContent = content;
        let hasChanges = false;
        
        patterns.forEach(pattern => {
            fixedContent = fixedContent.replace(pattern, (match, propDef, ending = '') => {
                // 检查是否已经有 override 属性
                if (propDef.includes('override:')) {
                    return match;
                }
                
                hasChanges = true;
                // 添加 override: true，保持缩进
                const indent = '            '; // 12个空格缩进
                if (ending) {
                    return propDef + `,\n${indent}override: true\n        ${ending}`;
                } else {
                    return propDef + `,\n${indent}override: true\n        }`;
                }
            });
        });
        
        return { content: fixedContent, hasChanges };
    }

    // 修复 this._super 调用问题
    fixSuperCalls(content) {
        let fixedContent = content;
        let hasChanges = false;
        
        // 查找并注释掉无效的 this._super() 调用
        const superCallPattern = /(\s*)this\._super\(\);?(\s*)/g;
        
        fixedContent = fixedContent.replace(superCallPattern, (match, beforeSpace, afterSpace) => {
            hasChanges = true;
            return `${beforeSpace}// this._super(); // 已注释：父类无此方法${afterSpace}`;
        });
        
        return { content: fixedContent, hasChanges };
    }

    // 修复 destroy 函数问题
    fixDestroyFunction(content) {
        // 查找 destroy 函数定义
        const destroyPattern = /(destroy\s*:\s*function\s*\([^)]*\)\s*{)([\s\S]*?)(^    },?)/gm;
        
        let hasChanges = false;
        const fixedContent = content.replace(destroyPattern, (match, funcStart, funcBody, funcEnd) => {
            // 检查是否已经调用了 this._super()
            if (funcBody.includes('this._super()') || funcBody.includes('// this._super()')) {
                return match;
            }
            
            hasChanges = true;
            // 在函数开始处添加 this._super() 调用
            const newFuncBody = '\n        this._super();\n' + funcBody;
            return funcStart + newFuncBody + funcEnd;
        });
        
        return { content: fixedContent, hasChanges };
    }

    // 修复数组默认值问题
    fixArrayDefaults(content, propertyName) {
        const pattern = new RegExp(`(${propertyName}\\s*:\\s*{[^}]*?default\\s*:\\s*)[^,\\n}]+`, 'g');
        
        let hasChanges = false;
        const fixedContent = content.replace(pattern, (match, prefix) => {
            hasChanges = true;
            return prefix + '[]';
        });
        
        return { content: fixedContent, hasChanges };
    }

    // 修复类型声明问题
    fixTypeDeclaration(content, propertyName) {
        // 修复 cc.Float 类型的对象默认值问题
        const pattern = new RegExp(`(${propertyName}\\s*:\\s*{[^}]*?)type\\s*:\\s*cc\\.Float([^}]*?default\\s*:\\s*)[^,\\n}]+`, 'g');
        
        let hasChanges = false;
        const fixedContent = content.replace(pattern, (match, prefix, middle) => {
            hasChanges = true;
            return prefix + 'type: cc.Float' + middle + '0';
        });
        
        return { content: fixedContent, hasChanges };
    }

    // 处理单个文件
    processFile(filePath) {
        const fileName = path.basename(filePath);
        console.log(`处理文件: ${fileName}`);
        
        const content = this.readFile(filePath);
        if (!content) {
            return false;
        }

        let fixedContent = content;
        let totalChanges = false;

        // 1. 修复属性隐藏问题
        const propertyFix = this.propertyOverrideFixes.find(fix => fix.file === fileName);
        if (propertyFix) {
            const result = this.fixPropertyOverride(fixedContent, propertyFix.property);
            if (result.hasChanges) {
                fixedContent = result.content;
                totalChanges = true;
                console.log(`  ✓ 修复了属性隐藏问题: ${propertyFix.property}`);
            }
        }

        // 2. 修复 this._super 调用问题
        const superResult = this.fixSuperCalls(fixedContent);
        if (superResult.hasChanges) {
            fixedContent = superResult.content;
            totalChanges = true;
            console.log(`  ✓ 修复了 this._super 调用问题`);
        }

        // 3. 修复 destroy 函数问题
        if (this.destroyFunctionFixes.includes(fileName)) {
            const destroyResult = this.fixDestroyFunction(fixedContent);
            if (destroyResult.hasChanges) {
                fixedContent = destroyResult.content;
                totalChanges = true;
                console.log(`  ✓ 修复了 destroy 函数问题`);
            }
        }

        // 4. 修复数组默认值问题
        const arrayFix = this.arrayDefaultFixes.find(fix => fix.file === fileName);
        if (arrayFix) {
            const arrayResult = this.fixArrayDefaults(fixedContent, arrayFix.property);
            if (arrayResult.hasChanges) {
                fixedContent = arrayResult.content;
                totalChanges = true;
                console.log(`  ✓ 修复了数组默认值问题: ${arrayFix.property}`);
            }
        }

        // 5. 修复类型声明问题
        const typeFix = this.typeDeclarationFixes.find(fix => fix.file === fileName);
        if (typeFix) {
            const typeResult = this.fixTypeDeclaration(fixedContent, typeFix.property);
            if (typeResult.hasChanges) {
                fixedContent = typeResult.content;
                totalChanges = true;
                console.log(`  ✓ 修复了类型声明问题: ${typeFix.property}`);
            }
        }

        // 如果有修改，写入文件
        if (totalChanges) {
            if (this.writeFile(filePath, fixedContent)) {
                this.fixedCount++;
                console.log(`  ✓ 文件修复完成\n`);
                return true;
            }
        } else {
            console.log(`  - 无需修复\n`);
        }

        return false;
    }

    // 运行修复
    run() {
        console.log('开始修复 output 目录中的 Cocos Creator 警告问题...\n');

        if (!fs.existsSync(this.outputDir)) {
            console.log(`目录不存在: ${this.outputDir}`);
            return;
        }

        const files = fs.readdirSync(this.outputDir);
        const jsFiles = files.filter(file => file.endsWith('.js'));

        console.log(`找到 ${jsFiles.length} 个 JS 文件\n`);

        // 只处理有问题的文件
        const problemFiles = [
            ...this.propertyOverrideFixes.map(f => f.file),
            ...this.destroyFunctionFixes,
            ...this.arrayDefaultFixes.map(f => f.file),
            ...this.typeDeclarationFixes.map(f => f.file)
        ];

        const uniqueProblemFiles = [...new Set(problemFiles)];
        
        uniqueProblemFiles.forEach(fileName => {
            const filePath = path.join(this.outputDir, fileName);
            if (fs.existsSync(filePath)) {
                this.processFile(filePath);
            } else {
                console.log(`文件不存在: ${fileName}`);
            }
        });

        // 输出结果
        console.log('\n=== 修复完成 ===');
        console.log(`修复的文件数量: ${this.fixedCount}`);
        
        if (this.errors.length > 0) {
            console.log('\n错误信息:');
            this.errors.forEach(error => console.log(`  - ${error}`));
        }

        console.log('\n建议重新启动 Cocos Creator 以查看修复效果。');
    }
}

// 运行修复脚本
const fixer = new OutputWarningFixer();
fixer.run();
