var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2BaseSdk = require("BaseSdk");
cc.nativeAndroid = cc.nativeAndroid || {};
var cc_nativeAndroid = cc.nativeAndroid;
var s = window.jsb && jsb.reflection ? jsb.reflection.callStaticMethod : function () {};
var def_JUHEAndroid = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.defaultClass = "org/cocos2dx/javascript/AppActivity";
    t.onbannerShow = function () {};
    t.isGetReward = false;
    t._uname = "";
    t._token = "";
    t._oaid = "";
    t._loginSuccess = null;
    t._loginfail = null;
    t.showbannerNum = 0;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.logout = function () {
    s(this.defaultClass, "logout", "()V");
  };
  _ctor.prototype.init = function (t) {
    var o = this;
    e.prototype.init.call(this, t);
    var i = this;
    var n = {
      onRewardVideoComplete: function () {
        i.isGetReward = true;
      },
      onRewardVideoClose: function () {
        if (i.isGetReward) {
          i.onPlayEnd && i.onPlayEnd($2BaseSdk.VideoAdCode.COMPLETE, "看完广告");
        } else {
          i.onPlayEnd && i.onPlayEnd($2BaseSdk.VideoAdCode.NOT_COMPLITE, "未完整观看广告");
        }
      },
      onRewardVideoShow: function () {
        i.onPlayEnd && i.onPlayEnd($2BaseSdk.VideoAdCode.SHOW_SUCCESS, "");
      },
      onRewardVideoFail: function () {
        i.onPlayEnd && i.onPlayEnd($2BaseSdk.VideoAdCode.AD_ERROR, "内容正在加载中，请稍后再试");
      },
      onInterstitialShow: function () {
        console.log("onInterstitialShow");
      },
      onInterstitialClose: function () {},
      onInterstitialShowFail: function () {
        console.log("onInterstitialShowFail");
      },
      onFeedRenderSuccess: function () {},
      onFeedRenderFail: function () {},
      onSplashShow: function () {},
      onSplashClose: function () {},
      onPrivacyAccept: function () {},
      onPrivacyReject: function () {},
      onUnionSdkInitSuccess: function () {}
    };
    window.unionSdkCallback = n;
    cc_nativeAndroid.onPrivacyAccept = function () {
      o._privacyCallback && o._privacyCallback(true);
    };
    cc_nativeAndroid.onPrivacyReject = function () {
      o._privacyCallback && o._privacyCallback(false);
    };
    cc_nativeAndroid.getUuidCallback = function (e, t, i, n) {
      o.setOpenId(e);
      o._uname = i;
      o._token = t;
      o._oaid = n;
      o._loginSuccess && o._loginSuccess();
    };
    cc_nativeAndroid.logoutCallback = function () {
      cc.game.emit("GameLogOut", true);
    };
  };
  _ctor.prototype.login = function (e) {
    var t = this;
    return new Promise(function (o, i) {
      t.checkLogin(function () {
        cc.game.emit("TO_SDKLOGIN", function (i) {
          if (i) {
            e && e(i);
            o(i);
          } else {
            t.checkLogin(t._loginSuccess, t._loginfail);
          }
        }, t._token, t.getOpenId(), JSON.stringify({
          uname: t._uname,
          oaid: t._oaid
        }));
      }, function () {
        i(null);
      });
    });
  };
  _ctor.prototype.checkLogin = function (e, t) {
    this._loginSuccess = e;
    this._loginfail = t;
    s(this.defaultClass, "login", "()V");
  };
  _ctor.prototype.showBannerWithNode = function (e, t, o) {
    this.showBannerWithStyle(e, {}, o);
  };
  _ctor.prototype.showBannerWithStyle = function (e, t, o) {
    this.onbannerShow = o;
    this.showbannerNum++;
    s(this.defaultClass, "addBanner", "(Ljava/lang/String;)V", e);
    o && o();
  };
  _ctor.prototype.hideBanner = function () {
    this.showbannerNum--;
    this.showbannerNum <= 0 && s(this.defaultClass, "hideBanner", "()V");
  };
  _ctor.prototype.destroyBanner = function () {
    this.hideBanner();
  };
  _ctor.prototype.showVideoAD = function (e, t) {
    this.onPlayEnd = t;
    this.isGetReward = false;
    console.log("showVideoAD");
    this.onPlayEnd && this.onPlayEnd($2BaseSdk.VideoAdCode.COMPLETE, "");
  };
  _ctor.prototype.showFullVideoAD = function (e) {
    s(this.defaultClass, "showFullVideo", "(Ljava/lang/String;)V", e);
  };
  _ctor.prototype.sendEvent = function (e, t) {
    var o = e;
    null != t && "" != t && "none" != t || (t = "{}");
    s(this.defaultClass, "sendMsg", "(Ljava/lang/String;Ljava/lang/String;)V", o, t);
  };
  _ctor.prototype.vibrate = function (e) {
    undefined === e && (e = 0);
    s(this.defaultClass, "vibrate", "(I)V", 0 == e ? 100 : 300);
  };
  _ctor.prototype.share = function () {};
  _ctor.prototype.showInsertAd = function (e) {
    s(this.defaultClass, "showFullVideo", "(Ljava/lang/String;)V", e);
  };
  _ctor.prototype.showSplashAd = function (e) {
    s(this.defaultClass, "showSplashAd", "(Ljava/lang/String;)V", e);
  };
  _ctor.prototype.showPrivacy = function (e) {
    e && e(true);
  };
  _ctor.prototype.notifyCreateRole = function (e) {
    s(this.defaultClass, "createRole", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", e.roleId, e.serverId, e.roleName, e.serverName);
  };
  _ctor.prototype.notifyRoleEnterGame = function (e) {
    s(this.defaultClass, "enterGame", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V", e.roleId, e.serverId, e.roleName, e.serverName, e.level || 1);
  };
  _ctor.prototype.notifyRoleLevelUp = function (e) {
    s(this.defaultClass, "roleLevelUp", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V", e.roleId, e.serverId, e.roleName, e.serverName, e.level || 1);
  };
  return _ctor;
}($2BaseSdk.BaseSdk);
exports.default = def_JUHEAndroid;