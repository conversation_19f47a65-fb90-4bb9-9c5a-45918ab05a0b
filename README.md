# JS 反编译器 - Cocos Creator 转换工具

这个工具用于将编译后的 Cocos Creator JS 文件转换回原始的 cc.Class 格式。

## 功能特性

- 自动识别 ccclass 组件文件并进行转换
- 将转换后的文件保存到 `output` 目录
- 将未转换的文件复制到 `origin` 目录
- 支持单个文件和批量目录转换
- 智能处理多个 cc.Class 定义的文件

## 使用方法

### 转换单个文件
```bash
node js_decompiler_to_ccclass.js path/to/file.js
```

### 转换整个目录
```bash
node js_decompiler_to_ccclass.js scripts
```

## 输出目录结构

转换完成后，会生成以下目录结构：

```
├── output/          # 转换后的 cc.Class 格式文件
│   ├── Component1.js
│   ├── Component2.js
│   └── ...
├── origin/          # 未转换的原始文件
│   ├── Config1.js
│   ├── Controller1.js
│   └── ...
└── scripts/         # 原始输入文件（保持不变）
    ├── Component1.js
    ├── Config1.js
    └── ...
```

## 文件分类规则

1. **转换文件** (保存到 `output` 目录)
   - 包含 ccclass 装饰器的组件文件
   - 单个 cc.Class 定义的文件
   - 成功转换为 cc.Class 格式

2. **复制文件** (保存到 `origin` 目录)
   - 非 ccclass 文件（配置文件、工具类等）
   - 包含多个 cc.Class 定义的文件
   - 空文件或转换失败的文件

## 转换统计

运行完成后会显示详细的转换统计信息：

```
=== 转换统计 ===
总文件数: 344
转换文件数: 141 (保存到 output 目录)
复制文件数: 201 (保存到 origin 目录)
多个cc.Class文件数: 2 (保存到 origin 目录)
失败文件数: 0
```

## 注意事项

1. 原始文件不会被修改，始终保持在 `scripts` 目录中
2. 多个 cc.Class 定义的文件暂时不支持转换，会直接复制到 `origin` 目录
3. 转换后的文件可能需要手动调整导入路径和属性类型
4. 建议在转换后进行代码审查和测试

## 支持的转换特性

- cc.Class 基本结构转换
- 属性定义转换
- getter/setter 方法转换
- 生命周期方法转换
- 继承关系处理
- 装饰器信息提取
