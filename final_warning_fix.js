const fs = require('fs');
const path = require('path');

// 最终的警告修复脚本
class FinalWarningFixer {
    constructor() {
        this.outputDir = './output';
        this.fixedCount = 0;
        this.errors = [];
    }

    readFile(filePath) {
        try {
            return fs.readFileSync(filePath, 'utf8');
        } catch (error) {
            this.errors.push(`读取文件失败: ${filePath} - ${error.message}`);
            return null;
        }
    }

    writeFile(filePath, content) {
        try {
            fs.writeFileSync(filePath, content, 'utf8');
            return true;
        } catch (error) {
            this.errors.push(`写入文件失败: ${filePath} - ${error.message}`);
            return false;
        }
    }

    // 查找并修复所有 CCClass 相关的属性隐藏问题
    fixAllCCClassProperties(content) {
        let fixedContent = content;
        let hasChanges = false;

        // 查找所有可能的属性定义模式
        const propertyPatterns = [
            // 匹配 property: { ... } 形式
            /(\w+)\s*:\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}/g
        ];

        propertyPatterns.forEach(pattern => {
            fixedContent = fixedContent.replace(pattern, (match, propName, propContent) => {
                // 跳过已经有 override 的属性
                if (propContent.includes('override:')) {
                    return match;
                }

                // 跳过函数定义
                if (propContent.includes('function') || match.includes(': function')) {
                    return match;
                }

                // 跳过一些特殊属性
                const skipProperties = ['extends', 'properties', 'ctor', 'onLoad', 'start', 'update'];
                if (skipProperties.includes(propName)) {
                    return match;
                }

                // 检查是否是属性定义（包含 type, default, get, set 等）
                const propertyKeywords = ['type:', 'default:', 'get()', 'set(', 'visible:', 'displayName:', 'tooltip:'];
                const hasPropertyKeywords = propertyKeywords.some(keyword => propContent.includes(keyword));

                if (hasPropertyKeywords) {
                    hasChanges = true;
                    // 添加 override: true
                    const trimmedContent = propContent.trim();
                    const needsComma = !trimmedContent.endsWith(',');
                    const comma = needsComma ? ',' : '';
                    return `${propName}: {${propContent}${comma}\n            override: true\n        }`;
                }

                return match;
            });
        });

        return { content: fixedContent, hasChanges };
    }

    // 修复所有 this._super 调用问题
    fixAllSuperCalls(content) {
        let fixedContent = content;
        let hasChanges = false;

        // 查找所有 this._super() 调用
        const superCallPattern = /(\s*)this\._super\(\);?(\s*)/g;
        
        fixedContent = fixedContent.replace(superCallPattern, (match, beforeSpace, afterSpace) => {
            hasChanges = true;
            return `${beforeSpace}// this._super(); // 已注释：父类可能无此方法${afterSpace}`;
        });

        return { content: fixedContent, hasChanges };
    }

    // 修复 FBoxCollider 的 _size 类型问题
    fixFBoxColliderSize(content) {
        let fixedContent = content;
        let hasChanges = false;

        // 移除 _size 属性的 type 声明
        const sizeTypePattern = /(_size\s*:\s*\{[^}]*?)type\s*:\s*cc\.Size\s*,?\s*/g;
        
        fixedContent = fixedContent.replace(sizeTypePattern, (match, prefix) => {
            hasChanges = true;
            return prefix;
        });

        return { content: fixedContent, hasChanges };
    }

    // 处理单个文件
    processFile(filePath) {
        const fileName = path.basename(filePath);
        console.log(`处理文件: ${fileName}`);
        
        const content = this.readFile(filePath);
        if (!content) {
            return false;
        }

        let fixedContent = content;
        let totalChanges = false;
        let changesList = [];

        // 1. 修复 this._super 调用问题
        const superResult = this.fixAllSuperCalls(fixedContent);
        if (superResult.hasChanges) {
            fixedContent = superResult.content;
            totalChanges = true;
            changesList.push('this._super 调用问题');
        }

        // 2. 修复属性隐藏问题
        const propertyResult = this.fixAllCCClassProperties(fixedContent);
        if (propertyResult.hasChanges) {
            fixedContent = propertyResult.content;
            totalChanges = true;
            changesList.push('属性隐藏问题');
        }

        // 3. 特殊处理 FBoxCollider 的 _size 类型问题
        if (fileName === 'FBoxCollider.js') {
            const sizeResult = this.fixFBoxColliderSize(fixedContent);
            if (sizeResult.hasChanges) {
                fixedContent = sizeResult.content;
                totalChanges = true;
                changesList.push('_size 类型声明问题');
            }
        }

        // 如果有修改，写入文件
        if (totalChanges) {
            if (this.writeFile(filePath, fixedContent)) {
                this.fixedCount++;
                console.log(`  ✓ 修复了: ${changesList.join(', ')}`);
                console.log(`  ✓ 文件修复完成\n`);
                return true;
            }
        } else {
            console.log(`  - 无需修复\n`);
        }

        return false;
    }

    // 运行修复
    run() {
        console.log('开始最终修复 output 目录中的所有警告问题...\n');

        if (!fs.existsSync(this.outputDir)) {
            console.log(`目录不存在: ${this.outputDir}`);
            return;
        }

        // 需要特别处理的文件列表（基于警告信息）
        const problemFiles = [
            'OrganismBase.js',
            'FCircleCollider.js', 
            'FBoxCollider.js',
            'FPolygonCollider.js',
            'M33_FightBuffView.js',
            'M33_FightScene.js', 
            'M33_FightUIView.js',
            'MCRole.js',
            'MBRRole.js'
        ];

        problemFiles.forEach(fileName => {
            const filePath = path.join(this.outputDir, fileName);
            if (fs.existsSync(filePath)) {
                this.processFile(filePath);
            } else {
                console.log(`文件不存在: ${fileName}`);
            }
        });

        // 输出结果
        console.log('\n=== 最终修复完成 ===');
        console.log(`修复的文件数量: ${this.fixedCount}`);
        
        if (this.errors.length > 0) {
            console.log('\n错误信息:');
            this.errors.forEach(error => console.log(`  - ${error}`));
        }

        console.log('\n所有已知的警告问题已修复。');
        console.log('建议重新启动 Cocos Creator 以查看修复效果。');
        console.log('\n如果还有其他警告，请提供具体的警告信息以便进一步修复。');
    }
}

// 运行修复脚本
const fixer = new FinalWarningFixer();
fixer.run();
