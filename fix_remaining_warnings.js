const fs = require('fs');
const path = require('path');

// 修复剩余的警告问题
class RemainingWarningFixer {
    constructor() {
        this.outputDir = './output';
        this.fixedCount = 0;
        this.errors = [];
    }

    readFile(filePath) {
        try {
            return fs.readFileSync(filePath, 'utf8');
        } catch (error) {
            this.errors.push(`读取文件失败: ${filePath} - ${error.message}`);
            return null;
        }
    }

    writeFile(filePath, content) {
        try {
            fs.writeFileSync(filePath, content, 'utf8');
            return true;
        } catch (error) {
            this.errors.push(`写入文件失败: ${filePath} - ${error.message}`);
            return false;
        }
    }

    // 通用的属性添加 override: true 的方法
    addOverrideToProperty(content, propertyName) {
        // 更精确的正则表达式来匹配属性定义
        const patterns = [
            // 匹配 getter/setter 形式的属性
            new RegExp(`(\\s*${propertyName}\\s*:\\s*{[\\s\\S]*?)(\\s*})(\\s*,?)`, 'g'),
            // 匹配简单属性定义
            new RegExp(`(\\s*${propertyName}\\s*:\\s*{[^}]*?)(\\s*})(\\s*,?)`, 'g')
        ];
        
        let fixedContent = content;
        let hasChanges = false;
        
        patterns.forEach(pattern => {
            fixedContent = fixedContent.replace(pattern, (match, propDef, closeBrace, comma) => {
                // 检查是否已经有 override 属性
                if (propDef.includes('override:')) {
                    return match;
                }
                
                hasChanges = true;
                // 添加 override: true，保持缩进
                const indent = '            '; // 12个空格缩进
                return propDef + `,\n${indent}override: true\n        ${closeBrace}${comma}`;
            });
        });
        
        return { content: fixedContent, hasChanges };
    }

    // 批量处理文件中的多个属性
    processMultipleProperties(filePath, properties) {
        const fileName = path.basename(filePath);
        console.log(`处理文件: ${fileName}`);
        
        const content = this.readFile(filePath);
        if (!content) {
            return false;
        }

        let fixedContent = content;
        let totalChanges = false;
        let fixedProperties = [];

        properties.forEach(property => {
            const result = this.addOverrideToProperty(fixedContent, property);
            if (result.hasChanges) {
                fixedContent = result.content;
                totalChanges = true;
                fixedProperties.push(property);
            }
        });

        if (totalChanges) {
            if (this.writeFile(filePath, fixedContent)) {
                this.fixedCount++;
                console.log(`  ✓ 修复了属性隐藏问题: ${fixedProperties.join(', ')}`);
                console.log(`  ✓ 文件修复完成\n`);
                return true;
            }
        } else {
            console.log(`  - 无需修复\n`);
        }

        return false;
    }

    // 修复特定文件的 this._super 调用问题
    fixSpecificSuperCalls(filePath, methodNames) {
        const fileName = path.basename(filePath);
        console.log(`修复 ${fileName} 中的 this._super 调用问题`);
        
        const content = this.readFile(filePath);
        if (!content) {
            return false;
        }

        let fixedContent = content;
        let hasChanges = false;

        methodNames.forEach(methodName => {
            // 查找特定方法中的 this._super 调用
            const methodPattern = new RegExp(`(${methodName}\\s*:\\s*function[\\s\\S]*?{[\\s\\S]*?)this\\._super\\(\\);?([\\s\\S]*?}\\s*,?)`, 'g');
            
            fixedContent = fixedContent.replace(methodPattern, (match, beforeSuper, afterSuper) => {
                hasChanges = true;
                return beforeSuper + '// this._super(); // 已注释：父类无此方法' + afterSuper;
            });
        });

        if (hasChanges) {
            if (this.writeFile(filePath, fixedContent)) {
                console.log(`  ✓ 修复了 this._super 调用问题`);
                console.log(`  ✓ 文件修复完成\n`);
                return true;
            }
        } else {
            console.log(`  - 无需修复\n`);
        }

        return false;
    }

    // 运行修复
    run() {
        console.log('开始修复剩余的 Cocos Creator 警告问题...\n');

        // 需要修复属性隐藏问题的文件和属性
        const propertyFixes = [
            {
                file: 'MCRole.js',
                properties: ['myData', 'isCanRelive', 'settingScale', 'game', 'roleID', 'saveData', 'prorSkillId', 'figureData', 'figureNextData', 'nextLv']
            },
            {
                file: 'MBRRole.js', 
                properties: ['myData', 'isCanRelive']
            }
        ];

        // 处理属性隐藏问题
        propertyFixes.forEach(fix => {
            const filePath = path.join(this.outputDir, fix.file);
            if (fs.existsSync(filePath)) {
                this.processMultipleProperties(filePath, fix.properties);
            } else {
                console.log(`文件不存在: ${fix.file}`);
            }
        });

        // 需要修复 this._super 调用问题的文件
        const superCallFixes = [
            {
                file: 'OrganismBase.js',
                methods: ['changeListener', 'onNewSize', 'updateProperty', 'onUpdate']
            },
            {
                file: 'MCRole.js',
                methods: ['unuse', 'addBuff', 'addBuffByData', 'addArmor', 'onUpdate', 'OnBuff', 'setPosition']
            }
        ];

        // 处理 this._super 调用问题
        superCallFixes.forEach(fix => {
            const filePath = path.join(this.outputDir, fix.file);
            if (fs.existsSync(filePath)) {
                this.fixSpecificSuperCalls(filePath, fix.methods);
            } else {
                console.log(`文件不存在: ${fix.file}`);
            }
        });

        // 输出结果
        console.log('\n=== 修复完成 ===');
        console.log(`修复的文件数量: ${this.fixedCount}`);
        
        if (this.errors.length > 0) {
            console.log('\n错误信息:');
            this.errors.forEach(error => console.log(`  - ${error}`));
        }

        console.log('\n建议重新启动 Cocos Creator 以查看修复效果。');
    }
}

// 运行修复脚本
const fixer = new RemainingWarningFixer();
fixer.run();
