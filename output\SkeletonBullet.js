/**
 * SkeletonBullet
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Bullet = require('Bullet');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Bullet.default,

    properties: {
    },

    onLoad: function () {
        var t = this;
        this._super();
        this.mySkeleton = this.node.getComByChild(sp.Skeleton) || this.node.getComponent(sp.Skeleton);
        this.mySkeleton.setCompleteListener(function () {
        t.setDead();
        });
    },

    onEnable: function () {
        this.mySkeleton.setAnimation(0, this.mySkeleton.defaultAnimation, this.mySkeleton.loop);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
