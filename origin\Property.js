Object.defineProperty(exports, "__esModule", {
  value: true
});
var def_Property = function () {
  function _ctor() {
    this.baseHp = 0;
    this.extraHpRatio = 0;
    this.baseAttack = 0;
    this.extraAttackRatio = 0;
    this.reduceAttackRatio = 0;
    this.baseCritPer = 0;
    this.extraCritPer = 0;
    this.baseCritRatio = 15e3;
    this.extraCritRatio = 0;
    this.baseResitCrit = 0;
    this.extraResitCrit = 0;
    this.baseHitRatio = 1e4;
    this.extraHitRatio = 0;
    this.baseMissRatio = 0;
    this.extraMissRatio = 0;
    this.baseCtrlRatio = 0;
    this.extraCtrlRatio = 0;
    this.baseResitCtrl = 0;
    this.extraResitCtrl = 0;
  }
  Object.defineProperty(_ctor.prototype, "hp", {
    get: function () {
      return Math.ceil(this.baseHp * (1e4 + this.extraHpRatio) / 1e4);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "attack", {
    get: function () {
      return Math.ceil(this.baseAttack * (1e4 + this.extraAttackRatio) / 1e4);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "critPer", {
    get: function () {
      return this.baseCritPer + this.extraCritPer;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "critRatio", {
    get: function () {
      return this.baseCritRatio + this.extraCritRatio;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "resitCrit", {
    get: function () {
      return this.baseResitCrit + this.extraResitCrit;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "hitRatio", {
    get: function () {
      return this.baseHitRatio + this.extraHitRatio;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "missRatio", {
    get: function () {
      return this.baseMissRatio + this.extraMissRatio;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "ctrlRatio", {
    get: function () {
      return this.baseCtrlRatio + this.extraCtrlRatio;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "resitCtrl", {
    get: function () {
      return this.baseResitCtrl + this.extraResitCtrl;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.reset = function () {
    this.baseAttack = 0;
    this.baseCritPer = 0;
    this.baseCritRatio = 0;
    this.baseHitRatio = 1e4;
    this.baseHp = 0;
    this.baseMissRatio = 0;
    this.baseResitCrit = 0;
    this.baseResitCtrl = 0;
    this.baseCtrlRatio = 0;
    this.reduceAttackRatio = 0;
    this.extraAttackRatio = 0;
    this.extraCritPer = 0;
    this.extraCritRatio = 0;
    this.extraHitRatio = 0;
    this.extraHpRatio = 0;
    this.extraMissRatio = 0;
    this.extraResitCrit = 0;
    this.extraResitCtrl = 0;
    this.extraCtrlRatio = 0;
  };
  return _ctor;
}();
exports.default = def_Property;