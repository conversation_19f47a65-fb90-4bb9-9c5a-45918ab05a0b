const fs = require('fs');
const path = require('path');

// 修复警告的脚本
class WarningFixer {
    constructor() {
        this.scriptsDir = './scripts';
        this.outputDir = './output';
        this.fixedCount = 0;
        this.errors = [];
    }

    // 读取文件内容
    readFile(filePath) {
        try {
            return fs.readFileSync(filePath, 'utf8');
        } catch (error) {
            this.errors.push(`读取文件失败: ${filePath} - ${error.message}`);
            return null;
        }
    }

    // 写入文件内容
    writeFile(filePath, content) {
        try {
            fs.writeFileSync(filePath, content, 'utf8');
            return true;
        } catch (error) {
            this.errors.push(`写入文件失败: ${filePath} - ${error.message}`);
            return false;
        }
    }

    // 修复属性隐藏问题 - 添加 override: true
    fixPropertyHiding(content, className, propertyName) {
        // 查找属性定义的模式
        const propertyPattern = new RegExp(
            `(\\s*${propertyName}\\s*:\\s*{[^}]*?)\\s*}`,
            'g'
        );
        
        return content.replace(propertyPattern, (match, propDef) => {
            // 检查是否已经有 override 属性
            if (propDef.includes('override:')) {
                return match;
            }
            
            // 添加 override: true
            const lastCommaIndex = propDef.lastIndexOf(',');
            if (lastCommaIndex !== -1) {
                return propDef + ',\n            override: true\n        }';
            } else {
                return propDef + ',\n            override: true\n        }';
            }
        });
    }

    // 修复 this._super 调用问题
    fixSuperCalls(content) {
        // 移除不存在父类方法的 this._super 调用
        const superCallPatterns = [
            /this\._super\(\);?\s*\/\/.*?no super method/g,
            /this\._super\(\);\s*$/gm
        ];

        let fixedContent = content;
        superCallPatterns.forEach(pattern => {
            fixedContent = fixedContent.replace(pattern, '// this._super(); // 移除：父类无此方法');
        });

        return fixedContent;
    }

    // 修复 destroy 函数问题
    fixDestroyFunction(content) {
        // 查找 destroy 函数定义
        const destroyPattern = /(destroy\s*:\s*function\s*\([^)]*\)\s*{)([\s\S]*?)(}\s*,?)/g;
        
        return content.replace(destroyPattern, (match, funcStart, funcBody, funcEnd) => {
            // 检查是否已经调用了 this._super()
            if (funcBody.includes('this._super()')) {
                return match;
            }
            
            // 在函数开始处添加 this._super() 调用
            const newFuncBody = '\n        this._super();\n' + funcBody;
            return funcStart + newFuncBody + funcEnd;
        });
    }

    // 修复数组默认值问题
    fixArrayDefaults(content) {
        // 修复 logos 数组默认值
        content = content.replace(
            /(logos\s*:\s*{[^}]*?default\s*:\s*)[^,\n}]+/g,
            '$1[]'
        );

        return content;
    }

    // 修复类型声明问题
    fixTypeDeclarations(content) {
        // 修复 cc.Float 类型的对象默认值问题
        content = content.replace(
            /(amTime\s*:\s*{[^}]*?)type\s*:\s*cc\.Float([^}]*?default\s*:\s*)[^,\n}]+/g,
            '$1type: cc.Float$2null'
        );

        return content;
    }

    // 处理单个文件
    processFile(filePath) {
        console.log(`处理文件: ${filePath}`);
        
        const content = this.readFile(filePath);
        if (!content) {
            return false;
        }

        let fixedContent = content;
        let hasChanges = false;

        // 应用各种修复
        const newContent1 = this.fixSuperCalls(fixedContent);
        if (newContent1 !== fixedContent) {
            fixedContent = newContent1;
            hasChanges = true;
            console.log(`  - 修复了 this._super 调用问题`);
        }

        const newContent2 = this.fixDestroyFunction(fixedContent);
        if (newContent2 !== fixedContent) {
            fixedContent = newContent2;
            hasChanges = true;
            console.log(`  - 修复了 destroy 函数问题`);
        }

        const newContent3 = this.fixArrayDefaults(fixedContent);
        if (newContent3 !== fixedContent) {
            fixedContent = newContent3;
            hasChanges = true;
            console.log(`  - 修复了数组默认值问题`);
        }

        const newContent4 = this.fixTypeDeclarations(fixedContent);
        if (newContent4 !== fixedContent) {
            fixedContent = newContent4;
            hasChanges = true;
            console.log(`  - 修复了类型声明问题`);
        }

        // 修复特定的属性隐藏问题
        const propertyHidingFixes = [
            { file: 'OrganismBase.js', property: 'horDir' },
            { file: 'FCircleCollider.js', property: 'type' },
            { file: 'FBoxCollider.js', property: 'type' },
            { file: 'FPolygonCollider.js', property: 'type' },
            { file: 'M33_FightBuffView.js', property: 'param' },
            { file: 'M33_FightScene.js', property: 'role' },
            { file: 'M33_FightUIView.js', property: 'game' }
        ];

        const fileName = path.basename(filePath);
        const relevantFixes = propertyHidingFixes.filter(fix => fix.file === fileName);
        
        relevantFixes.forEach(fix => {
            const newContent = this.fixPropertyHiding(fixedContent, '', fix.property);
            if (newContent !== fixedContent) {
                fixedContent = newContent;
                hasChanges = true;
                console.log(`  - 修复了属性隐藏问题: ${fix.property}`);
            }
        });

        // 如果有修改，写入文件
        if (hasChanges) {
            if (this.writeFile(filePath, fixedContent)) {
                this.fixedCount++;
                console.log(`  ✓ 文件修复完成`);
                return true;
            }
        } else {
            console.log(`  - 无需修复`);
        }

        return false;
    }

    // 处理目录中的所有 JS 文件
    processDirectory(dirPath) {
        if (!fs.existsSync(dirPath)) {
            console.log(`目录不存在: ${dirPath}`);
            return;
        }

        const files = fs.readdirSync(dirPath);
        const jsFiles = files.filter(file => file.endsWith('.js'));

        console.log(`\n处理目录: ${dirPath}`);
        console.log(`找到 ${jsFiles.length} 个 JS 文件\n`);

        jsFiles.forEach(file => {
            const filePath = path.join(dirPath, file);
            this.processFile(filePath);
        });
    }

    // 运行修复
    run() {
        console.log('开始修复 Cocos Creator 警告问题...\n');

        // 处理 scripts 目录
        this.processDirectory(this.scriptsDir);

        // 处理 output 目录
        this.processDirectory(this.outputDir);

        // 输出结果
        console.log('\n=== 修复完成 ===');
        console.log(`修复的文件数量: ${this.fixedCount}`);
        
        if (this.errors.length > 0) {
            console.log('\n错误信息:');
            this.errors.forEach(error => console.log(`  - ${error}`));
        }

        console.log('\n建议重新启动 Cocos Creator 以查看修复效果。');
    }
}

// 运行修复脚本
const fixer = new WarningFixer();
fixer.run();
