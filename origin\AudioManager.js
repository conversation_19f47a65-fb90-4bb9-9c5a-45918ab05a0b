Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AudioManager = exports.PlayType = exports.AudioType = undefined;
var i;
var n;
var $2Cfg = require("Cfg");
var $2Notifier = require("Notifier");
var $2NotifyID = require("NotifyID");
var $2SettingModel = require("SettingModel");
var $2Manager = require("Manager");
var u = function (e) {
  this.id = e;
};
(function (e) {
  e[e.UI = 0] = "UI";
  e[e.POSINDEX1 = 1] = "POSINDEX1";
  e[e.POSINDEX2 = 2] = "POSINDEX2";
  e[e.POSINDEX3 = 3] = "POSINDEX3";
  e[e.POSINDEX4 = 4] = "POSINDEX4";
  e[e.POSINDEX5 = 5] = "POSINDEX5";
  e[e.POSINDEX6 = 6] = "POSINDEX6";
  e[e.POSINDEX7 = 7] = "POSINDEX7";
  e[e.POSINDEX8 = 8] = "POSINDEX8";
  e[e.POSINDEX9 = 9] = "POSINDEX9";
  e[e.POSINDEX10 = 10] = "POSINDEX10";
  e[e.Music = 11] = "Music";
  e[e.Max = 11] = "Max";
})(i = exports.AudioType || (exports.AudioType = {}));
(function (e) {
  e[e.Component = 1] = "Component";
  e[e.Scripts = 2] = "Scripts";
})(n = exports.PlayType || (exports.PlayType = {}));
var exp_AudioManager = function () {
  function _ctor() {
    this._clips = {};
    this._audioSources = [];
    this._audioIdLists = [];
    this._audioWaitToPlay = [];
    this._musicSettingVolume = 1;
    this._musicConfigVolume = 1;
    this._musicFadeVolume = 1;
    this._musicId = 0;
    this._enableMusic = true;
    this._audioSettingVolume = 1;
    this._audioConfigVolumes = [];
    this._enableAudio = true;
    this._audioDeltaMaps = null;
    this._deltaTime = 0;
    var e = cc.director.getScene();
    var t = new cc.Node("_AudioManager");
    cc.game.addPersistRootNode(t);
    t.parent = e;
    this._root = t;
    this._musicSource = t.addComponent(cc.AudioSource);
    this._musicSource.loop = true;
    this._audioDeltaMaps = new Map();
    for (var o = 0; o < i.Max; o++) {
      this.addAudioSource();
    }
    this._musicId = 0;
    $2Notifier.Notifier.addListener($2NotifyID.NotifyID.Game_Pause, this.OnGamePause, this);
    $2Notifier.Notifier.addListener($2NotifyID.NotifyID.Game_Update, this.onUpdate, this);
  }
  _ctor.prototype.addAudioSource = function () {
    this._audioSources.push(this._root.addComponent(cc.AudioSource));
    this._audioIdLists.push(-1);
    this._audioWaitToPlay.push(0);
    this._audioConfigVolumes.push(1);
  };
  _ctor.prototype.setMusicEnable = function (e, t) {
    undefined === t && (t = 0);
    this._enableMusic = e;
    if (!t) {
      if (this._enableMusic) {
        if (this._musicClip) {
          this.resumeMusic();
        } else {
          if (0 == this._musicId) {
            return;
          }
          this.playMusic(this._musicId, true);
        }
      } else {
        this.pauseMusic();
      }
    }
  };
  _ctor.prototype.setMusicVolume = function (t) {
    this._musicSettingVolume = t;
    if (null != this._musicSource && _ctor.musicType == n.Component) {
      this._musicSource.volume = this._musicSettingVolume * this._musicConfigVolume * this._musicFadeVolume;
    } else {
      cc.audioEngine.setMusicVolume(t);
    }
  };
  _ctor.prototype.musicVolume = function () {
    if (_ctor.musicType == n.Component) {
      return this._musicSettingVolume;
    } else {
      return cc.audioEngine.getMusicVolume();
    }
  };
  _ctor.prototype.setMusicConfigVolume = function (t) {
    this._musicConfigVolume = t;
    if (null != this._musicSource && _ctor.musicType == n.Component) {
      this._musicSource.volume = this._musicSettingVolume * this._musicConfigVolume * this._musicFadeVolume;
    } else {
      cc.audioEngine.setMusicVolume(t);
    }
  };
  Object.defineProperty(_ctor.prototype, "currMusicId", {
    get: function () {
      return this._musicId;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.playMusic = function (e, t) {
    var o = this;
    undefined === t && (t = true);
    this._musicId = e;
    if ((null == this._musicClip || this._musicClip.id != e) && this._enableMusic) {
      var i = $2Cfg.Cfg.Sound.get(e);
      if (null != i && 0 == i.type) {
        i.volume <= 0 && (i.volume = 1);
        this.setMusicConfigVolume(i.volume * this._musicSettingVolume);
        this._musicSource.loop = t;
        var n = this._clips[e];
        if (null == n) {
          var a = i.path;
          var s = Number(e);
          $2Manager.Manager.loader.loadRes(a, cc.AudioClip, function (e, t) {
            if (!e) {
              if (!o._clips[s]) {
                o._clips[s] = new u(s), o._clips[s].clip = t;
              }
              o.doPlayMusic(o._clips[s]);
            }
          });
        } else {
          this.doPlayMusic(n);
        }
      }
    }
  };
  _ctor.prototype.doPlayMusic = function (t) {
    this._musicClip = t;
    if (_ctor.musicType == n.Component) {
      if (null != t.clip) {
        this._musicSource.clip = t.clip, this._musicSource.play();
      }
    } else {
      cc.audioEngine.playMusic(t.clip, true);
    }
  };
  _ctor.prototype.stopMusic = function () {
    if (_ctor.musicType == n.Component) {
      this._musicSource.stop();
      this._musicClip = null;
      cc.audioEngine.stopMusic();
    } else {
      cc.audioEngine.stopMusic();
    }
    this._musicClip = null;
    this._musicId = -1;
  };
  _ctor.prototype.setMusicClip = function (e, t) {
    this._clips[e] = new u(e);
    this._clips[e].clip = t;
  };
  _ctor.prototype.pauseMusic = function () {
    if (_ctor.musicType == n.Component) {
      this._musicSource.pause();
    } else {
      cc.audioEngine.pauseMusic();
    }
  };
  _ctor.prototype.resumeMusic = function () {
    if (this._enableMusic) {
      console.log("resumeMusic");
      if (_ctor.musicType == n.Component) {
        this._musicSource.resume();
      } else {
        cc.audioEngine.resumeMusic();
      }
    }
  };
  _ctor.prototype.setAudioVolume = function (t, o) {
    t *= $2SettingModel.default.instance.toggle.audioValue;
    this._audioSettingVolume = t;
    if (_ctor.musicType == n.Component) {
      this._audioSources[o].volume = this._audioSettingVolume * this._audioConfigVolumes[o];
    } else {
      cc.audioEngine.setEffectsVolume(t);
    }
  };
  _ctor.prototype.setAudioClip = function (e, t) {
    this._clips[e] || (this._clips[e] = new u(e));
    this._clips[e].clip = t;
  };
  _ctor.prototype.playAudio = function (e, t) {
    var o = this;
    undefined === t && (t = i.UI);
    var n = $2Cfg.Cfg.Sound.get(e);
    if (null != n && 1 == n.type && this._enableAudio) {
      if (n.delta > 0) {
        var a = this._audioDeltaMaps.get(e);
        if (!a || a <= 0) {
          this._audioDeltaMaps.set(e, n.delta);
        } else if (a > 0) {
          return;
        }
      }
      n.volume <= 0 && (n.volume = 1);
      this.setAudioVolume(n.volume, t);
      this._audioSources[t].loop = !!n.loop;
      var s = this._clips[e];
      this._audioSources[t].loop && this._audioWaitToPlay[t]++;
      if (s) {
        this.doPlayAudio(s, t);
      } else {
        var c = n.path;
        this._clips[e] = new u(e);
        var p = Number(e);
        $2Manager.Manager.loader.loadRes(c, cc.AudioClip, function (e, i) {
          if (e) {
            cc.warn("音效加载错误", e);
          } else if (i) {
            o._clips[p].clip = i;
            if (o._audioWaitToPlay[t] < 0) {
              return;
            }
            o.doPlayAudio(o._clips[p], t);
          } else {
            delete o._clips[p];
          }
        });
      }
    }
  };
  _ctor.prototype.doPlayAudio = function (t, o) {
    if (null != t.clip) {
      this._audioWaitToPlay[o] = 0;
      if (_ctor.musicType == n.Component) {
        this._audioSources[o].clip = t.clip, this._audioSources[o].play();
      } else {
        this._audioIdLists[o] > 0 && this._audioSources[o].loop && (cc.audioEngine.stopEffect(this._audioIdLists[o]), this._audioIdLists[o] = -1), this._audioIdLists[o] = cc.audioEngine.playEffect(t.clip, this._audioSources[o].loop);
      }
    }
  };
  _ctor.prototype.stopAudio = function (t) {
    undefined === t && (t = i.UI);
    this._audioSources[t].loop && this._audioWaitToPlay[t]--;
    if (_ctor.musicType == n.Component) {
      this._audioSources[t].stop();
    } else {
      cc.audioEngine.stopEffect(this._audioIdLists[t]);
    }
  };
  _ctor.prototype.setEnableAudio = function (e) {
    this._enableAudio = e;
  };
  _ctor.prototype.audioVolume = function () {
    return this._audioSettingVolume;
  };
  _ctor.prototype.OnGamePause = function () {};
  _ctor.prototype.onUpdate = function (e) {
    this._deltaTime = e;
    this._audioDeltaMaps.forEach(this._updateAudioDelta, this);
  };
  _ctor.prototype._updateAudioDelta = function (e, t) {
    if (e > 0) {
      e -= this._deltaTime;
      this._audioDeltaMaps.set(t, e);
    }
  };
  _ctor.musicType = n.Scripts;
  return _ctor;
}();
exports.AudioManager = exp_AudioManager;