/**
 * M20_Pop_ShopBuyConfirm
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2EaseScaleTransition = require('EaseScaleTransition');
const $2Game = require('Game');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');
const $2M20Gooditem = require('M20Gooditem');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        shownode: {
            type: cc.Node,
            default: null
        },
        cost: {
            type: cc.Label,
            default: null
        },
        costT: {
            type: cc.Sprite,
            default: null
        },
        desc: {
            type: cc.Label,
            default: null
        },
        title: {
            type: cc.Label,
            default: null
        },
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    ctor: function () {
        this.shownode = null
        this.cost = null
        this.costT = null
        this.desc = null
        this.title = null
    },

    // use this for initialization
    onLoad: function () {
    },

    setInfo: function () {
        var e = this;
        var t = this.param.cfg;
        var o = $2Cfg.Cfg.RoleUnlock.find({
        id: t.equipId
        });
        var i = this.param.disCount;
        var n = this.param.count || t.getNum;
        var r = $2Cfg.Cfg.CurrencyConfig.get(t.type);
        $2Manager.Manager.loader.loadPrefab("ui/ModeBackpackHero/goodItem").then(function (i) {
        var r = i;
        r.setParent(e.shownode);
        var s = r.getComponent($2M20Gooditem.default);
        s.setdata({
        path: o ? o.icon : t.icon,
        bgpath: o ? $2GameSeting.GameSeting.getRarity(e.mode.buffmap[o.rarity]).blockImg : t.bgpath,
        count: n,
        isfrag: !!o
        });
        s.goodsbg.node.setActive(!!o);
        });
        this.cost.string = (o ? Math.floor(t.costVal[0] * i) : " 获得") + "";
        var s = $2Cfg.Cfg.CurrencyConfig.find({
        id: t.costType[0]
        });
        $2Manager.Manager.loader.loadSpriteToSprit(s.icon, this.costT, this.node);
        // this.costT.node.scale = 22 == t.type ? 1 : .6;
        this.desc.string = o ? o.desc : r.desc;
        this.title.string = t.title;
    },

    buyIt: function () {
        var e;
        var t;
        $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 3);
        null === (t = (e = this.param).cb) || undefined === t || t.call(e);
        this.close();
    },

    onClickFrame: function () {
        this.close();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
