var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.FightController = undefined;
var $2CallID = require("CallID");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2KnapsackVo = require("KnapsackVo");
var $2NodePool = require("NodePool");
var $2FightModel = require("FightModel");
var $2Game = require("Game");
var exp_FightController = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t.setup($2FightModel.default.getInstance);
    t.changeListener(true);
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor.prototype, "classname", {
    get: function () {
      return "FightController";
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerAllProtocol = function () {};
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Game_Load, this.onOpenGame, this, 200);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Game_Replay, this.onReplay, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_BackToMain, this.backToMain, this);
    $2Notifier.Notifier.changeCall(e, $2CallID.CallID.Fight_GetCutMode, this.getCutMode, this);
    $2Notifier.Notifier.changeCall(e, $2CallID.CallID.Fight_GetKnapsackMgr, this.getKnapsackMgr, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_End, this.onFight_End, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_Win, this.onFight_Win, this);
  };
  _ctor.prototype.loginFinish = function () {};
  _ctor.prototype.getKnapsackMgr = function (e) {
    return $2KnapsackVo.KnapsackVo.KMap.get(e);
  };
  _ctor.prototype.backToMain = function () {
    this._model.cutMode = $2Game.Game.Mode.NONE;
    $2Game.Game.setGameSpeed(1);
    $2NodePool.NodePool.clear();
    $2Manager.Manager.restoreGroupMatrix();
  };
  _ctor.prototype.onReplay = function () {
    $2NodePool.NodePool.clear();
  };
  _ctor.prototype.onOpenGame = function (e) {
    this._model.cutMode = e;
    $2UIManager.UIManager.Close("ui/setting/MoreGamesView");
  };
  _ctor.prototype.getCutMode = function () {
    return this._model.cutMode;
  };
  _ctor.prototype.onFight_End = function () {
    var e;
    $2Game.Game.setGameSpeed(1);
    null === (e = $2Game.Game.mgr) || undefined === e || e.sendEvent("gameFail");
  };
  _ctor.prototype.onFight_Win = function () {
    var e;
    $2Game.Game.setGameSpeed(1);
    null === (e = $2Game.Game.mgr) || undefined === e || e.sendEvent("gameWin");
  };
  return _ctor;
}($2MVC.MVC.MController);
exports.FightController = exp_FightController;