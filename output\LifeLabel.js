/**
 * LifeLabel
 * 组件类 - 从编译后的JS反编译生成
 */

const $2ListenID = require('ListenID');
const $2NodePool = require('NodePool');
const $2Notifier = require('Notifier');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        string: {
            get() {
                return this.label.string;
            },
            set(value) {
                this.isValid && (this.label.string = e);
            },
            visible: false
        }
    },

    ctor: function () {
        this.posType = 1
    },

    // use this for initialization
    onLoad: function () {
    },

    set: function (e) {
        this.offset = null;
        this.changeListener(false);
        this.label = this.node.getComponent(cc.Label);
        this.ower = e;
        this.changeListener(true);
    },

    changeListener: function (e) {
        var t;
        null === (t = this.ower) || undefined === t || t.node.changeListener(e, $2ListenID.ListenID.Fight_EntityUpdate, this.onOwerUpdate, this);
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_RoundState, this.onFight_RoundState, this);
    },

    onFight_RoundState: function () {
        this.remove();
    },

    onOwerUpdate: function () {
        if (this.ower.curHp <= 0 || !this.ower.isActive) {
        return this.remove();
        }
        var e = 1 == this.posType ? this.ower.haedPosition : this.ower.bodyPosition;
        this.offset && e.addSelf(this.offset);
        this.node.setPosition(e);
    },

    remove: function () {
        this.changeListener(false);
        this.isValid && $2NodePool.NodePool.despawn(this.node.nodeItem);
        this.ower = null;
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
