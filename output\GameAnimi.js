/**
 * GameAnimi
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameSeting = require('GameSeting');
const $2Game = require('Game');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Animation,

    properties: {
    },

    ctor: function () {
        this.type = $2GameSeting.GameSeting.TweenType.Game
    },

    // use this for initialization
    onLoad: function () {
    },

    set: function (e) {
        this.type = e;
        return this;
    },

    onEnable: function () {
        this._super();
        this.type == $2GameSeting.GameSeting.TweenType.Game && $2Game.Game.mgr && (this.defaultClip.speed = $2Game.Game.mgr.gameSpeed);
        this.play();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
