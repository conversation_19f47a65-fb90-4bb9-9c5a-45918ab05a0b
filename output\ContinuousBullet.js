/**
 * ContinuousBullet
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameSkeleton = require('GameSkeleton');
const $2Bullet = require('Bullet');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Bullet.default,

    properties: {
    },

    // use this for initialization
    onLoad: function () {
    },

    setBulletVo: function (t) {
        var o;
        this._super(t);
        1440 == this.vo.belongSkill.skillMainID && (null === (o = this.getComponentInChildren($2GameSkeleton.default)) || undefined === o || o.setAnimation(0, "ready" + [1, 1, 1, 2, 2, 2, 3, 3][this.vo.belongSkill.lv], true));
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
