var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2GameSeting = require("GameSeting");
var $2GameUtil = require("GameUtil");
var $2RecordVo = require("RecordVo");
var $2Game = require("Game");
var $2MTideDefendRebound = require("MTideDefendRebound");
var def_TideDefendModel = function (e) {
  function _ctor() {
    var o;
    var i = e.call(this) || this;
    i.gameMode = $2Game.Game.Mode.TIDEDEFEND;
    i.recordVo = null;
    i.gmDiff = 0;
    i._dragonList = {};
    i.poolMap = ((o = {})[$2MTideDefendRebound.MTideDefendRebound.poolType.NormalBuff] = function () {
      return i.cardPool.norBuffPool;
    }, o[$2MTideDefendRebound.MTideDefendRebound.poolType.HighBuff] = function () {
      return i.cardPool.adBuffPool;
    }, o);
    null == _ctor._instance && (_ctor._instance = i);
    return i;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "role", {
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "rVo", {
    get: function () {
      return this.recordVo.vo;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.loginFinish = function () {
    this.recordVo = new $2RecordVo.RecordVo.Mgr("TideDefend", function () {
      return new h();
    });
  };
  _ctor.prototype.bShowAd = function (e) {
    return this.bShowAdNew(e);
  };
  _ctor.prototype.bShowAdNew = function (e) {
    var t = 0;
    if (4001 == e) {
      t = this.rVo.tideRecordFreshTime1;
    } else {
      4002 == e && (t = this.rVo.tideRecordFreshTime2);
    }
    var o = Math.floor(t / 1e3);
    var i = Math.floor(new Date().getTime() / 1e3) - o;
    return i > 0 && i < 1800;
  };
  _ctor.prototype.setTideRecordFreshTime = function (e, t) {
    if (4001 == t) {
      this.rVo.tideRecordFreshTime1 = e;
    } else {
      4002 == t && (this.rVo.tideRecordFreshTime2 = e);
    }
  };
  _ctor.prototype.getTideRecordFreshTime = function (e) {
    if (4001 == e) {
      return this.rVo.tideRecordFreshTime1;
    } else {
      if (4002 == e) {
        return this.rVo.tideRecordFreshTime2;
      } else {
        return undefined;
      }
    }
  };
  _ctor.prototype.getksd = function () {
    this.miniGameCfg.id;
  };
  _ctor.prototype.bShowMatrix = function (e) {
    return 4002 == e.id;
  };
  _ctor.prototype.bShowMonsterBlob = function () {
    return true;
  };
  _ctor.prototype.bDragonBoss = function (e) {
    return !(!e.monId || !e.monId[0] || 999999910 != e.monId[0]);
  };
  Object.defineProperty(_ctor.prototype, "dragonList", {
    get: function () {
      var e = this;
      0 == Object.keys(this._dragonList).length && $2Cfg.Cfg.bagMonsterLv.forEach(function (t) {
        if (999999991 == t.lv) {
          e._dragonList[t.lv] || (e._dragonList[t.lv] = {});
          e._dragonList[t.lv][t.id] = t;
        } else if (999999992 == t.lv) {
          e._dragonList[t.lv] || (e._dragonList[t.lv] = {});
          e._dragonList[t.lv][t.id] = t;
        } else if (999999993 == t.lv) {
          e._dragonList[t.lv] || (e._dragonList[t.lv] = {});
          e._dragonList[t.lv][t.id] = t;
        } else if (999999910 == t.lv) {
          e._dragonList[t.lv] || (e._dragonList[t.lv] = {}), e._dragonList[t.lv][t.id] = t;
        }
      });
      return this._dragonList;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "cardPool", {
    get: function () {
      var e = this;
      return $2Cfg.Cfg.BagModeSkillPool.findBy(function (t) {
        var o;
        return t.modeType == e.gameMode && (!e.game.miniGameCfg || (null === (o = t.lv) || undefined === o ? undefined : o.includes(e.game.miniGameCfg.id)));
      }) || $2Cfg.Cfg.BagModeSkillPool.findBy(function (t) {
        return t.modeType == e.gameMode;
      });
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.fightBuffWidth = function (e, t, o) {
    var i = this;
    undefined === t && (t = {});
    undefined === o && (o = this.poolMap[e]());
    $2Game.Game.Mode.TIDEDEFEND;
    t.num || (t.num = 3);
    null == t.isBinding && (t.isBinding = true);
    var n = [];
    var r = o[0];
    var a = o[1];
    this.role.buffMgr.use(3042, false, function () {
      t.num += 1;
    });
    var l = [];
    r.forEach(function (e, o) {
      var r = $2Game.ModeCfg.Buff.get(e);
      if (r) {
        var p = i.role.buffMgr.get(e);
        if (!r.skillId || !t.isBinding || $2GameUtil.GameUtil.hasIntersection(i.role.skillMgr.skillIDs, r.skillId)) {
          if (p) {
            if (p.isMaxLayer) {
              return;
            }
            if (1 == r.isSelect) {
              return;
            }
          }
          r.skillId && l.push(e);
          var f = {
            id: e,
            w: a[o],
            isAd: 0
          };
          i.role.buffMgr.use(3043, true, function (e) {
            [$2GameSeting.GameSeting.RarityType.B, $2GameSeting.GameSeting.RarityType.A, $2GameSeting.GameSeting.RarityType.S].includes(r.rarity) && (f.w += e.cutVo.value[0][0]);
          });
          n.push(f);
        }
      }
    });
    for (var p = n.length - 1; p >= 0; p--) {
      n[p].w <= 0 && $2GameUtil.GameUtil.deleteArrItem(n, n[p]);
    }
    var f = [];
    $2GameUtil.GameUtil.weightGetList(n, t.num).forEach(function (e) {
      f.push({
        id: e.id,
        isAd: e.isAd
      });
    });
    return f;
  };
  _ctor._instance = null;
  return _ctor;
}($2MVC.MVC.BaseModel);
exports.default = def_TideDefendModel;
var h = function (e) {
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.tideRecordFreshTime = -1;
    t.tideRecordFreshTime1 = -1;
    t.tideRecordFreshTime2 = -1;
    return t;
  }
  cc__extends(t, e);
  return t;
}($2RecordVo.RecordVo.Data);