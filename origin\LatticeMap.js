Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.LatticeMap = undefined;
var $2GameSeting = require("GameSeting");
var $2Game = require("Game");
var $2BaseEntity = require("BaseEntity");
var a = cc.Vec2.ZERO;
(function (e) {
  function t(e, t) {
    undefined === t && (t = 128);
    return {
      x: Math.floor(e.x / t),
      y: Math.floor(e.y / t)
    };
  }
  function o(e) {
    return 1e4 * e.x + e.y;
  }
  e.toMapPos = t;
  e.toIndex = o;
  var s = function () {
    function e(e) {
      this.fineness = 256;
      this.tempList = [];
      for (var t in e) {
        this[t] = e[t];
      }
      this.map = new $2GameSeting.TMap();
    }
    e.prototype.onUpdate = function () {
      var e;
      var i;
      var n = this;
      this.map.clear();
      null === (e = this.list) || undefined === e || e.forEach(function (e) {
        e.isValid && (e.collider && !e.collider.isActive || (e.mapPos = t(e.position, n.fineness), i = o(e.mapPos), n.map.has(i) ? n.map.get(i).push(e) : n.map.set(i, [e])));
      });
    };
    e.prototype.seek = function (e, i, s, c) {
      var l = this;
      undefined === s && (s = false);
      this.tempList.length = 0;
      var u;
      var p;
      var f = Math.ceil(i / this.fineness);
      var h = t(e.position, this.fineness);
      if (e.entityType == $2BaseEntity.EntityType.Monster && !s) {
        var d = $2Game.Game.mgr.mainRole;
        cc.Vec2.subtract(a, e.node.position, d.position);
        if ((p = a.magSqr()) < Math.pow(i, 2)) {
          return [d];
        } else {
          return [];
        }
      }
      for (var g = h.x - f; g < h.x + f; g++) {
        for (var y = h.y - f; y < h.y + f; y++) {
          u = o({
            x: g,
            y: y
          });
          this.map.getor(u, []).forEach(function (t) {
            t.ID != e.ID && (s || t.campType != e.campType) && (t.isInvincible || c && c.includes(t.ID) || (cc.Vec2.subtract(a, e.node.position, t.position), (p = a.magSqr()) < Math.pow(i, 2) && (t.node.t_sort = p, l.tempList.push(t))));
          });
        }
      }
      this.tempList.sort(function (e, t) {
        return e.node.t_sort - t.node.t_sort;
      });
      return this.tempList;
    };
    e.prototype.seekByPos = function (e) {
      e.minRadius || (e.minRadius = 0);
      e.maxNum || (e.maxNum = 10);
      this.tempList.length = 0;
      var i;
      var n = Math.ceil(e.radius / this.fineness);
      var r = t(e.pos, this.fineness);
      if (e.radius < this.fineness) {
        i = o(r);
        this.searchIndex(i, e);
      } else {
        for (var a = r.x - n; a < r.x + n; a++) {
          for (var s = r.y - n; s < r.y + n; s++) {
            i = o({
              x: a,
              y: s
            });
            this.searchIndex(i, e);
          }
        }
      }
      this.tempList.sort(function (e, t) {
        return e.node.t_sort - t.node.t_sort;
      });
      this.tempList = this.tempList.splice(0, e.maxNum);
      return this.tempList;
    };
    e.prototype.searchIndex = function (e, t) {
      var o;
      var i = this;
      t.minRadius || (t.minRadius = 0);
      this.map.getor(e, []).forEach(function (e) {
        var n;
        if (!(t.targetCamp && !t.targetCamp.includes(e.campType) || e.isInvincible || null !== (n = t.ignoreID) && undefined !== n && n.includes(e.ID))) {
          cc.Vec2.subtract(a, t.pos, e.position);
          if ((o = a.magSqr()) > Math.pow(t.minRadius, 2) && o < Math.pow(t.radius, 2)) {
            e.node.t_sort = o, i.tempList.push(e);
          }
        }
      });
    };
    e.prototype.seekToTeammate = function (e, i) {
      var n = this;
      this.tempList.length = 0;
      var r;
      var s;
      var c = Math.ceil(i / this.fineness);
      var l = t(e.position, this.fineness);
      for (var u = l.x - c; u < l.x + c; u++) {
        for (var p = l.y - c; p < l.y + c; p++) {
          r = o({
            x: u,
            y: p
          });
          this.map.getor(r, []).forEach(function (t) {
            if (t.ID != e.ID && t.campType == e.campType) {
              cc.Vec2.subtract(a, e.node.position, t.position);
              if ((s = a.magSqr()) < Math.pow(i, 2)) {
                t.node.t_sort = s, n.tempList.push(t);
              }
            }
          });
        }
      }
      this.tempList.sort(function (e, t) {
        return e.node.t_sort - t.node.t_sort;
      });
      return this.tempList;
    };
    e.prototype.seekNodeByPos = function (e) {
      var i = this;
      this.tempList.length = 0;
      var n;
      var r = Math.ceil(e.radius / this.fineness);
      var a = t(e.pos, this.fineness);
      if (e.radius < this.fineness) {
        n = o({
          x: a.x,
          y: a.y
        });
        this.map.getor(n, []).forEach(function (t) {
          if (cc.Vec2.squaredDistance(t.position, e.pos) < Math.pow(e.radius, 2)) {
            t.node.t_sort = undefined;
            i.tempList.push(t);
          }
        });
      } else {
        for (var s = a.x - r; s < a.x + r; s++) {
          for (var c = a.y - r; c < a.y + r; c++) {
            n = o({
              x: s,
              y: c
            });
            this.map.getor(n, []).forEach(function (t) {
              if (cc.Vec2.squaredDistance(t.position, e.pos) < Math.pow(e.radius, 2)) {
                t.node.t_sort = undefined;
                i.tempList.push(t);
              }
            });
          }
        }
      }
      this.tempList.sort(function (e, t) {
        return e.node.t_sort - t.node.t_sort;
      });
      return this.tempList;
    };
    return e;
  }();
  e.Mgr = s;
})(exports.LatticeMap || (exports.LatticeMap = {}));