/**
 * TrackBullet
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameUtil = require('GameUtil');
const $2Game = require('Game');
const $2Bullet = require('Bullet');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var f = cc.v2();

exports.default = cc.Class({
    extends: $2Bullet.default,

    properties: {
    },

    ctor: function () {
        this._scearchTime = 1
        this._deltaTime = 0
    },

    // use this for initialization
    onLoad: function () {
    },

    setBulletVo: function (e) {
        this._vo = e;
        this._deltaTime = 1;
    },

    nextTarget: function () {
        var e = this.vo.belongSkill.getOwner();
        var t = this.vo.belongSkill.cutVo.dis;
        1540 == this.vo.belongSkill.skillMainID && (t += 600);
        var o = $2Game.Game.mgr.LatticeElementMap.seekByPos({
        pos: this.node.position,
        radius: t,
        targetCamp: [e.atkCamp]
        });
        if (o.length > 0) {
        this._target = $2GameUtil.GameUtil.getRandomInArray(o)[0];
        this._vo.shootDir = this._target.position.sub(this.node.position).normalize();
        }
    },

    onUpdate: function (t) {
        if ((this._deltaTime += t) >= this._scearchTime) {
        this._deltaTime = 0;
        this.nextTarget();
        }
        this._super(t);
    },

    onCollisionStay: function (e) {
        var t = e.comp;
        if (t && this.vo.atkCamp.includes(e.comp.campType)) {
        t.behit(this.vo.hurt);
        this.vo.shootDir.mul(this.vo.belongSkill.cutVo.repelDic / 100, f);
        cc.Vec2.add(f, f, t.position);
        t.setPosition(f);
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
