2025-07-03T08:00:48.875Z - warn: 'OrganismBase.horDir' hides inherited property 'BaseEntity.horDir'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.875Z - warn: this._super declared in 'undefined.changeListener' but no super method defined
2025-07-03T08:00:48.875Z - warn: this._super declared in 'undefined.onNewSize' but no super method defined
2025-07-03T08:00:48.876Z - warn: this._super declared in 'undefined.updateProperty' but no super method defined
2025-07-03T08:00:48.876Z - warn: this._super declared in 'undefined.onUpdate' but no super method defined
2025-07-03T08:00:48.877Z - warn: this._super declared in 'undefined.onNewSize' but no super method defined
2025-07-03T08:00:48.881Z - warn: this._super declared in 'undefined.unuse' but no super method defined
2025-07-03T08:00:48.883Z - warn: this._super declared in 'undefined.onNewSize' but no super method defined
2025-07-03T08:00:48.883Z - warn: 'CCClass.myData' hides inherited property 'CCClass.myData'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.883Z - warn: 'CCClass.isCanRelive' hides inherited property 'CCClass.isCanRelive'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.884Z - warn: 'FCircleCollider.type' hides inherited property 'FCollider.type'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.885Z - warn: this._super declared in 'undefined.unuse' but no super method defined
2025-07-03T08:00:48.885Z - warn: this._super declared in 'undefined.addBuff' but no super method defined
2025-07-03T08:00:48.885Z - warn: this._super declared in 'undefined.addBuffByData' but no super method defined
2025-07-03T08:00:48.885Z - warn: 'CCClass.settingScale' hides inherited property 'CCClass.settingScale'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.885Z - warn: 'CCClass.game' hides inherited property 'CCClass.game'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.886Z - warn: Can not indicate the 'cc.Float' attribute for "AutoAmTool.amTime", which its default value is type of object.
2025-07-03T08:00:48.891Z - warn: this._super declared in 'undefined.setPosition' but no super method defined
2025-07-03T08:00:48.892Z - warn: 'CCClass.roleID' hides inherited property 'CCClass.roleID'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.893Z - warn: 'CCClass.saveData' hides inherited property 'CCClass.saveData'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.893Z - warn: 'CCClass.prorSkillId' hides inherited property 'CCClass.prorSkillId'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.893Z - warn: 'CCClass.figureData' hides inherited property 'CCClass.figureData'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.893Z - warn: 'CCClass.figureNextData' hides inherited property 'CCClass.figureNextData'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.893Z - warn: 'CCClass.nextLv' hides inherited property 'CCClass.nextLv'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.893Z - warn: 'CCClass.saveData' hides inherited property 'CCClass.saveData'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.893Z - warn: this._super declared in 'undefined.addArmor' but no super method defined
2025-07-03T08:00:48.893Z - warn: this._super declared in 'undefined.onUpdate' but no super method defined
2025-07-03T08:00:48.893Z - warn: this._super declared in 'undefined.OnBuff' but no super method defined
2025-07-03T08:00:48.893Z - warn: 'CCClass.myData' hides inherited property 'CCClass.myData'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.893Z - warn: 'CCClass.isCanRelive' hides inherited property 'CCClass.isCanRelive'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.898Z - error: Overwriting 'destroy' function in 'BulletVo' class without calling super is not allowed. Call the super function in 'destroy' please.
    at Object.doValidateMethodWithProps_DEV (F:\CocosEditors\Creator\2.4.15\resources\engine\bin\.cache\dev\cocos2d\core\platform\preprocess-class.js:292:10)
    at Object.validateMethodWithProps (F:\CocosEditors\Creator\2.4.15\resources\engine\bin\.cache\dev\cocos2d\core\platform\preprocess-class.js:305:12)
    at Object.CCClass [as Class] (F:\CocosEditors\Creator\2.4.15\resources\engine\bin\.cache\dev\cocos2d\core\platform\CCClass.js:1069:21)
    at __define (F:\SVN\iaa\Dragon\temp\quick-scripts\dst\assets\_script\BulletVo.js:28:23)
    at F:\SVN\iaa\Dragon\temp\quick-scripts\dst\assets\_script\BulletVo.js:151:25
    at Object.<anonymous> (F:\SVN\iaa\Dragon\temp\quick-scripts\dst\assets\_script\BulletVo.js:158:19)
    at Object.<anonymous> (F:\SVN\iaa\Dragon\temp\quick-scripts\dst\assets\_script\BulletVo.js:160:3)
    at Module._compile (internal/modules/cjs/loader.js:1078:30)
    at Object.p (F:\CocosEditors\Creator\2.4.15\resources\app.asar\editor-framework\lib\share\require.js:1:1969)
2025-07-03T08:00:48.900Z - warn: 'FBoxCollider.type' hides inherited property 'FCollider.type'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.900Z - normal: No need to specify the "type" of "FBoxCollider._size" because cc.Size is a child class of ValueType.
2025-07-03T08:00:48.908Z - warn: 'FPolygonCollider.type' hides inherited property 'FCollider.type'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.917Z - warn: 'CCClass.game' hides inherited property 'CCClass.game'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.920Z - warn: The 'default' attribute of 'Launcher.logos' must be an array
2025-07-03T08:00:48.934Z - warn: 'M33_FightBuffView.param' hides inherited property 'Pop.param'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.935Z - warn: 'M33_FightScene.role' hides inherited property 'FightScene.role'. To make the current property override that implementation, add the `override: true` attribute please.
2025-07-03T08:00:48.935Z - warn: 'M33_FightUIView.game' hides inherited property 'FightUIView.game'. To make the current property override that implementation, add the `override: true` attribute please.