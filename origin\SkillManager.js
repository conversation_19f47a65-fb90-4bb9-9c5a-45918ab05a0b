var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Skill = undefined;
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Notifier = require("Notifier");
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var $2BaseEntity = require("BaseEntity");
var $2SkillModel = require("SkillModel");
(function (e) {
  var t;
  var o = function () {
    function e(e) {
      this.poolLimt = 99;
      this.skills = [];
      this.skillIDs = [];
      this.subSkills = [];
      this.launchPoint = cc.v2(0, 80);
      this.maxGap = 0;
      this.readySkill = [];
      this.onReady = function () {};
      this.ower = e;
    }
    Object.defineProperty(e.prototype, "game", {
      get: function () {
        return $2Game.Game.mgr;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "model", {
      get: function () {
        return $2SkillModel.default.getInstance;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "activeSkills", {
      get: function () {
        return this.skills.filter(function (e) {
          return !e.isAuto;
        });
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.add = function (e, t, o) {
      undefined === t && (t = true);
      undefined === o && (o = false);
      var i = $2Game.ModeCfg.Skill.get(e);
      if (i) {
        return this.addByData(i, t, o);
      }
    };
    e.prototype.addByData = function (e, t, o) {
      undefined === t && (t = true);
      undefined === o && (o = false);
      if (!this.isPoolFull) {
        var i = this.filterMainID(this.model.getMianID(e.id));
        if (i) {
          o = i.isStartSkill;
          this.clearByID(i.id);
        }
        var n = this.getSkillClass(e);
        this.skills.push(n);
        n.index = this.skills.length - 1;
        n.isAuto = t;
        n.isStartSkill = o;
        this.onSkillChange();
        return n;
      }
      cc.log("技能池已满");
    };
    e.prototype.getSkillClass = function (e) {
      var t = cc.js.getClassByName(e.skillClass || "Skill_Default");
      if (t) {
        var o = new t();
        o.setOwner(this.ower);
        o.skillCfg = e;
        o.load();
        return o;
      }
    };
    e.prototype.get = function (e) {
      return this.skills.find(function (t) {
        return t.id == e;
      });
    };
    e.prototype.addSubSkill = function (e) {
      var t = $2Game.ModeCfg.Skill.get(e);
      if (!t) {
        cc.warn("创建技能失败", e);
        return null;
      }
      var o = this.model.getMianID(e);
      var i = this.subSkills.find(function (e) {
        return e.skill.skillMainID == o;
      });
      i && this.subSkills.delete(i);
      return this.getSkillClass(t);
    };
    e.prototype.useSubSkill = function (e, t, o) {
      var i = this.model.getMianID(e[0]);
      var n = this.subSkills.find(function (e) {
        return e.skill.skillMainID == i;
      });
      n || (n = this.analysisData([e], this.ower)[0]);
      t == n.release && n.use(o);
    };
    e.prototype.clearAll = function () {
      for (var e = this.skills.length - 1; e >= 0; e--) {
        this.skills[e].unload();
        this.skills.splice(e, 1);
      }
      this.skills.length = 0;
      this.onSkillChange();
    };
    e.prototype.clearByID = function (e, t) {
      undefined === t && (t = false);
      if (e) {
        for (var o = 0; o < this.skills.length; o++) {
          var i = this.skills[o];
          if (t ? i.skillMainID == e : i.id == e) {
            i.unload();
            return void this.skills.splice(o, 1);
          }
        }
        this.onSkillChange();
      }
    };
    e.prototype.use = function (e) {
      var t = this.model.getMianID(e);
      var o = this.skills.find(function (e) {
        return e.skillMainID == t;
      });
      return !(!o || !o.isReady || (o.checkTarget(), 0));
    };
    e.prototype.filterMainID = function (e) {
      return this.skills.filter(function (t) {
        return t.skillMainID == e;
      })[0];
    };
    e.prototype.hasMainID = function (e) {
      for (var t = 0; t < this.skills.length; t++) {
        if (this.skills[t].skillMainID == e) {
          return true;
        }
      }
      return false;
    };
    e.prototype.onSkillChange = function () {
      var e;
      var t;
      var o = this;
      if (this.ower.entityType == $2BaseEntity.EntityType.Role) {
        this.game.timeDelay.cancelBy(this.changeTimer);
        this.changeTimer = this.ower.delayByGame(function () {
          o.skills && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_OnSkillChange, o.skills);
        }, .2).id;
      }
      this.skillIDs.length = 0;
      (e = this.skillIDs).push.apply(e, cc__spreadArrays(this.skills.map(function (e) {
        return e.skillCfg.id;
      }), this.subSkills.map(function (e) {
        return e.skill.skillCfg.id;
      })));
      null === (t = this.ower.buffMgr) || undefined === t || t.bufflist.forEach(function (e) {
        if (e.cutVo.skillId) {
          var t = $2GameUtil.GameUtil.hasIntersection(e.cutVo.skillId, o.skillIDs);
          e._isActive = t;
          t && (e.buffLayer = e.buffLayer);
        }
      });
    };
    e.prototype.onUpdate = function (e) {
      this.skills.forEach(function (t) {
        return t.onUpdate(e);
      });
      this.subSkills.forEach(function (t) {
        return t.skill.onUpdate(e);
      });
    };
    e.prototype.onBuff = function (e) {
      this.skills.forReverse(function (t) {
        return t.onBuff(e);
      });
      this.subSkills.forReverse(function (t) {
        return t.skill.onBuff(e);
      });
    };
    Object.defineProperty(e.prototype, "isPoolFull", {
      get: function () {
        return this.skills.length >= this.poolLimt;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.getReadySkill = function (e) {
      var t;
      undefined === e && (e = 1e4);
      this.readySkill.length = 0;
      var o = [];
      for (var i = this.skills.length - 1; i >= 0; i--) {
        var n = this.skills[i];
        n.isReady && e <= n.cutVo.dis && o.push({
          id: n.id,
          dis: n.cutVo.dis
        });
      }
      o.sort(function (e, t) {
        return t.dis - e.dis;
      });
      this.readySkill = o;
      this.maxGap = (null === (t = o[0]) || undefined === t ? undefined : t.dis) || 0;
      return o;
    };
    e.prototype.destroy = function () {
      this.clearAll();
    };
    e.prototype.analysisData = function (e, o) {
      var i = this;
      if (!e) {
        return [];
      }
      var n = [];
      e.forEach(function (e) {
        var r = i.subSkills.find(function (t) {
          return t.ID == e[0];
        });
        if (r) {
          n.push(r);
        } else {
          var a = new t.Data(e, o);
          a.skill && n.push(a);
        }
      });
      return n;
    };
    return e;
  }();
  e.SkillManager = o;
  (function (e) {
    var t = function () {
      function e(e, t) {
        this.tempPos = cc.Vec2.ZERO;
        this.canFireNum = 0;
        this.ID = e[0];
        this.release = e[1];
        this.spawningType = e[2];
        this.delay = e[3] || 0;
        this.weight = e[4] || 1;
        this.skill = t.skillMgr.addSubSkill(this.ID);
        this.skill && t.skillMgr.subSkills.push(this);
      }
      e.prototype.use = function (e) {
        var t = this;
        this.canFireNum && (this.skill._cd < this.skill.cutCDLimt || $2Game.Game.weightFloat(this.weight) && (this.spawningType == $2GameSeting.GameSeting.SpawningPos.Self ? e.pos.set(this.skill.launchPoint) : this.spawningType == $2GameSeting.GameSeting.SpawningPos.JoytPos && e.pos.set(this.skill.joytPos.add(this.skill.launchPoint)), this.skill.owner.delayByGame(function () {
          if (t.skill) {
            t.skill.belongSkillID = e.belongSkillID;
            t.skill.fire(e);
            t.canFireNum--;
          }
        }, this.delay), this.skill._cd = 0));
      };
      return e;
    }();
    e.Data = t;
  })(t = e.Sub || (e.Sub = {}));
  e.SelfDestructSkill = [2060, 66600];
})(exports.Skill || (exports.Skill = {}));