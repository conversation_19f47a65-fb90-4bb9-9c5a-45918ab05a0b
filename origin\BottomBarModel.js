var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2Game = require("Game");
var def_BottomBarModel = function (e) {
  function _ctor() {
    var o = e.call(this) || this;
    null == _ctor._instance && (_ctor._instance = o);
    return o;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.startGame = function (e) {
    var t = $2Game.Game.getMouth(e);
    t.isUnlock && $2Notifier.Notifier.send(t.mouth, e, t.data);
  };
  _ctor._instance = null;
  return _ctor;
}($2MVC.MVC.BaseModel);
exports.default = def_BottomBarModel;