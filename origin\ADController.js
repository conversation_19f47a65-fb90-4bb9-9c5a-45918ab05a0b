var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ADController = undefined;
var r;
require('./Game')
require('./MCDragoMutilation')
var $2CallID = require("CallID");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2NotifyID = require("NotifyID");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2AlertManager = require("AlertManager");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var $2ADModel = require("ADModel");
(function(e) {
    e[e.Video = 1] = "Video";
    e[e.ADcoupons_in = $2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_in] = "ADcoupons_in";
    e[e.ADcoupons_out = $2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out] = "ADcoupons_out";
})(r || (r = {}));
var exp_ADController = function(e) {
    function _ctor() {
        var t = e.call(this) || this;
        t.setup($2ADModel.default.instance);
        t.changeListener(true);
        return t;
    }
    cc__extends(_ctor, e);
    _ctor.prototype.reset = function() {
        this._model.reset();
    };
    Object.defineProperty(_ctor.prototype, "classname", {
        get: function() {
            return "ADController";
        },
        enumerable: false,
        configurable: true
    });
    _ctor.prototype.registerAllProtocol = function() {};
    _ctor.prototype.changeListener = function(e) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Ad_ShowVideo, this.onAd_ShowVideo, this);
        $2Notifier.Notifier.changeCall(e, $2CallID.CallID.Ad_VideoType, this.getVideoType, this);
        $2Notifier.Notifier.changeCall(e, $2CallID.CallID.Ad_ModeVideoNum, this.getModeVideoNum, this);
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Game_Load, this.onOpenGame, this, 200);
    };
    Object.defineProperty(_ctor.prototype, "curMode", {
        get: function() {
            return $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode);
        },
        enumerable: false,
        configurable: true
    });
    _ctor.prototype.onOpenGame = function() {
        this.curMode > 0 && this.mode.data.set(this.curMode, 0);
    };
    _ctor.prototype.getModeVideoNum = function() {
        return this.mode.data.getor(this.curMode, 0);
    };
    _ctor.prototype.getVideoType = function() {
        return this.videoType;
    };
    _ctor.prototype.onAd_ShowVideo = function(e) {
        var t = this;
        this.mode.isVideoIng = true;
        if ($2Manager.Manager.vo.knapsackVo.has($2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out)) {
            this.videoType = r.ADcoupons_out;
            if ($2Manager.Manager.vo.userVo.checkIgnorePop($2GameSeting.GameSeting.PopViewType.ADCouponsConfig)) {
                $2Manager.Manager.vo.knapsackVo.useUp($2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out), $2ModeBackpackHeroModel.default.instance.addAdCount(), e(wonderSdk.VideoAdCode.COMPLETE);
            } else {
                $2AlertManager.AlertManager.showSelectAlert({
                    title: "提示",
                    desc: "是否使用广告券跳过广告?",
                    confirmText: "确认",
                    icon: "img/lobby/icon_adcard",
                    viewType: $2GameSeting.GameSeting.PopViewType.ADCouponsConfig,
                    hasIgnore: true,
                    confirm: function() {
                        $2Manager.Manager.vo.knapsackVo.useUp($2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out);
                        $2ModeBackpackHeroModel.default.instance.addAdCount();
                        e(wonderSdk.VideoAdCode.COMPLETE);
                    }
                });
            }
            this.mode.isVideoIng = false;
        } else {
            this.videoType = r.Video;
            $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, true);
            wonderSdk.showVideoAD(function(o) {
                t.mode.isVideoIng = false;
                $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, false);
                e(o);
                $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 8);
                t.mode.data.add(t.curMode, 1);
            });
        }
    };
    return _ctor;
}($2MVC.MVC.MController);
exports.ADController = exp_ADController;