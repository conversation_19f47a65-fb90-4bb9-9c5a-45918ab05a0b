Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.serverTime = exports.getOpenid = exports.report = exports.click = undefined;
var $2Request = require("Request");
var $2config = require("config");
var $2md51 = require("md51");
var $2SdkConfig = require("SdkConfig");
exports.click = function (e, t, o, r) {
  undefined === t && (t = function () {});
  undefined === o && (o = function () {});
  undefined === r && (r = function () {});
  $2Request.default.post({
    url: $2config.DOMAIN + "/common/app-track/click",
    data: e,
    dataType: "json",
    success: t,
    fail: o,
    complete: r
  });
};
exports.report = function (e, t, o, a) {
  undefined === t && (t = function () {});
  undefined === o && (o = function () {});
  undefined === a && (a = function () {});
  $2Request.default.post({
    url: $2config.DOMAIN + "/common/user-op/op-merge-report?trace_id=" + $2md51.md5((Math.round(Date.now() / 1e3) + 1e3 * Math.random()).toString()),
    data: e,
    dataType: "json",
    success: t,
    fail: o,
    complete: a
  });
};
exports.getOpenid = function (e, t, o, r) {
  var s;
  undefined === t && (t = function () {});
  undefined === o && (o = function () {});
  undefined === r && (r = function () {});
  (s = {})[$2SdkConfig.EPlatform.KWAI_MICRO] = $2config.DOMAIN + "/common/kuaishou/login";
  s[$2SdkConfig.EPlatform.BYTE_DANCE] = $2config.DOMAIN + "/common/tt/session/sign_in";
  s[$2SdkConfig.EPlatform.WECHAT_GAME] = $2config.DOMAIN + "/common/session/check_code";
  var c = s;
  $2Request.default.post({
    url: c[wonderSdk.platformId],
    data: e,
    dataType: "json",
    success: t,
    fail: o,
    complete: r
  });
};
exports.serverTime = function (e, t, o) {
  undefined === e && (e = function () {});
  undefined === t && (t = function () {});
  undefined === o && (o = function () {});
  $2Request.default.get({
    url: $2config.DOMAIN + "/common/common/time",
    dataType: "json",
    success: e,
    fail: t,
    complete: o
  });
};