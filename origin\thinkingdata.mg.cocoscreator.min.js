function o(e) {
  return (o = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (e) {
    return typeof e;
  } : function (e) {
    if (e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype) {
      return "symbol";
    } else {
      return typeof e;
    }
  })(e);
}
function i(e, t) {
  if (!(e instanceof t)) {
    throw new TypeError("Cannot call a class as a function");
  }
}
function n(e, t) {
  for (var o = 0; o < t.length; o++) {
    var i = t[o];
    i.enumerable = i.enumerable || false;
    i.configurable = true;
    "value" in i && (i.writable = true);
    Object.defineProperty(e, s(i.key), i);
  }
}
function r(e, t, o) {
  t && n(e.prototype, t);
  o && n(e, o);
  Object.defineProperty(e, "prototype", {
    writable: false
  });
  return e;
}
function a(e, t) {
  if ("object" != typeof e || null === e) {
    return e;
  }
  var o = e[Symbol.toPrimitive];
  if (undefined === o) {
    return ("string" === t ? String : Number)(e);
  }
  if ("object" != typeof (o = o.call(e, t || "default"))) {
    return o;
  }
  throw new TypeError("@@toPrimitive must return a primitive value.");
}
function s(e) {
  if ("symbol" == typeof (e = a(e, "string"))) {
    return e;
  } else {
    return String(e);
  }
}
var c = {};
var l = Array.prototype;
var u = Object.prototype;
var p = l.slice;
var f = u.toString;
var h = Object.prototype.hasOwnProperty;
var d = l.forEach;
var g = Array.isArray;
var y = {};
var m = ["utm_source", "utm_medium", "utm_campaign", "utm_content", "utm_term"];
c.each = function (e, t, o) {
  if (null == e) {
    return false;
  }
  if (d && e.forEach === d) {
    e.forEach(t, o);
  } else if (e.length === +e.length) {
    var i = 0;
    for (var n = e.length; i < n; i++) {
      if (i in e && t.call(o, e[i], i, e) === y) {
        return false;
      }
    }
  } else {
    for (var r in e) {
      if (h.call(e, r) && t.call(o, e[r], r, e) === y) {
        return false;
      }
    }
  }
};
c.extend = function (e) {
  c.each(p.call(arguments, 1), function (t) {
    for (var o in t) {
      undefined !== t[o] && (e[o] = t[o]);
    }
  });
  return e;
};
c.extend2Layers = function (e) {
  c.each(p.call(arguments, 1), function (t) {
    for (var o in t) {
      if (undefined !== t[o]) {
        if (c.isObject(t[o]) && c.isObject(e[o])) {
          c.extend(e[o], t[o]);
        } else {
          e[o] = t[o];
        }
      }
    }
  });
  return e;
};
c.isArray = g || function (e) {
  return "[object Array]" === f.call(e);
};
c.isFunction = function (e) {
  try {
    return "function" == typeof e;
  } catch (e) {
    return false;
  }
};
c.isPromise = function (e) {
  return "[object Promise]" === f.call(e) && null != e;
};
c.isObject = function (e) {
  return "[object Object]" === f.call(e) && null != e;
};
c.isEmptyObject = function (e) {
  if (c.isObject(e)) {
    for (var t in e) {
      if (h.call(e, t)) {
        return false;
      }
    }
    return true;
  }
  return false;
};
c.isUndefined = function (e) {
  return undefined === e;
};
c.isString = function (e) {
  return "[object String]" === f.call(e);
};
c.isDate = function (e) {
  return "[object Date]" === f.call(e);
};
c.isBoolean = function (e) {
  return "[object Boolean]" === f.call(e);
};
c.isNumber = function (e) {
  return "[object Number]" === f.call(e) && /[\d\.]+/.test(String(e));
};
c.isJSONString = function (e) {
  try {
    JSON.parse(e);
  } catch (e) {
    return false;
  }
  return true;
};
c.decodeURIComponent = function (e) {
  var t = "";
  try {
    t = decodeURIComponent(e);
  } catch (o) {
    t = e;
  }
  return t;
};
c.encodeURIComponent = function (e) {
  var t = "";
  try {
    t = encodeURIComponent(e);
  } catch (o) {
    t = e;
  }
  return t;
};
c.utf8Encode = function (e) {
  var t;
  var o = "";
  var i = t = 0;
  var n = (e = (e + "").replace(/\r\n/g, "\n").replace(/\r/g, "\n")).length;
  for (var r = 0; r < n; r++) {
    var a = e.charCodeAt(r);
    var s = null;
    if (a < 128) {
      t++;
    } else {
      s = 127 < a && a < 2048 ? String.fromCharCode(a >> 6 | 192, 63 & a | 128) : String.fromCharCode(a >> 12 | 224, a >> 6 & 63 | 128, 63 & a | 128);
    }
    if (null !== s) {
      i < t && (o += e.substring(i, t));
      o += s;
      i = t = r + 1;
    }
  }
  i < t && (o += e.substring(i, e.length));
  return o;
};
c.base64Encode = function (e) {
  var t;
  var o;
  var i;
  var n;
  var r = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
  var a = 0;
  var s = 0;
  var l = "";
  var u = [];
  if (!e) {
    return e;
  }
  for (e = c.utf8Encode(e); t = (n = e.charCodeAt(a++) << 16 | e.charCodeAt(a++) << 8 | e.charCodeAt(a++)) >> 12 & 63, o = n >> 6 & 63, i = 63 & n, u[s++] = r.charAt(n >> 18 & 63) + r.charAt(t) + r.charAt(o) + r.charAt(i), a < e.length;) {
    ;
  }
  l = u.join("");
  switch (e.length % 3) {
    case 1:
      l = l.slice(0, -2) + "==";
      break;
    case 2:
      l = l.slice(0, -1) + "=";
  }
  return l;
};
c.encodeDates = function (e) {
  c.each(e, function (t, o) {
    if (c.isDate(t)) {
      e[o] = c.formatDate(t);
    } else if (c.isObject(t)) {
      e[o] = c.encodeDates(t);
    } else if (c.isArray(t)) {
      for (var i = 0; i < t.length; i++) {
        c.isDate(t[i]) && (e[o][i] = c.formatDate(t[i]));
      }
    }
  });
  return e;
};
c.formatDate = function (e) {
  function t(e) {
    if (e < 10) {
      return "0" + e;
    } else {
      return e;
    }
  }
  return e.getFullYear() + "-" + t(e.getMonth() + 1) + "-" + t(e.getDate()) + " " + t(e.getHours()) + ":" + t(e.getMinutes()) + ":" + t(e.getSeconds()) + "." + ((e = e.getMilliseconds()) < 100 && 9 < e ? "0" + e : e < 10 ? "00" + e : e);
};
c.searchObjDate = function (e) {
  try {
    (c.isObject(e) || c.isArray(e)) && c.each(e, function (t, o) {
      if (c.isObject(t) || c.isArray(t)) {
        c.searchObjDate(e[o]);
      } else {
        c.isDate(t) && (e[o] = c.formatDate(t));
      }
    });
  } catch (t) {
    _.warn(t);
  }
};
c.UUID = function () {
  var e = new Date().getTime();
  return String(Math.random()).replace(".", "").slice(1, 11) + "-" + e;
};
c.UUIDv4 = function () {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (e) {
    var t = 16 * Math.random() | 0;
    return ("x" === e ? t : 3 & t | 8).toString(16);
  });
};
c.setMpPlatform = function (e) {
  c.mpPlatform = e;
};
c.getMpPlatform = function () {
  return c.mpPlatform;
};
c.createExtraHeaders = function () {
  return {
    "TA-Integration-Type": "MG",
    "TA-Integration-Version": "2.2.1",
    "TA-Integration-Count": "1",
    "TA-Integration-Extra": c.getMpPlatform()
  };
};
c.checkAppId = function (e) {
  return e.replace(/\s*/g, "");
};
c.checkUrl = function (e) {
  e = e.replace(/\s*/g, "");
  return c.url("basic", e);
};
c.url = function () {
  function e() {
    return new RegExp(/(.*?)\.?([^.]*?)\.(com|net|org|biz|ws|in|me|co\.uk|co|org\.uk|ltd\.uk|plc\.uk|me\.uk|edu|mil|br\.com|cn\.com|eu\.com|hu\.com|no\.com|qc\.com|sa\.com|se\.com|se\.net|us\.com|uy\.com|ac|co\.ac|gv\.ac|or\.ac|ac\.ac|af|am|as|at|ac\.at|co\.at|gv\.at|or\.at|asn\.au|com\.au|edu\.au|org\.au|net\.au|id\.au|be|ac\.be|adm\.br|adv\.br|am\.br|arq\.br|art\.br|bio\.br|cng\.br|cnt\.br|com\.br|ecn\.br|eng\.br|esp\.br|etc\.br|eti\.br|fm\.br|fot\.br|fst\.br|g12\.br|gov\.br|ind\.br|inf\.br|jor\.br|lel\.br|med\.br|mil\.br|net\.br|nom\.br|ntr\.br|odo\.br|org\.br|ppg\.br|pro\.br|psc\.br|psi\.br|rec\.br|slg\.br|tmp\.br|tur\.br|tv\.br|vet\.br|zlg\.br|br|ab\.ca|bc\.ca|mb\.ca|nb\.ca|nf\.ca|ns\.ca|nt\.ca|on\.ca|pe\.ca|qc\.ca|sk\.ca|yk\.ca|ca|cc|ac\.cn|net\.cn|com\.cn|edu\.cn|gov\.cn|org\.cn|bj\.cn|sh\.cn|tj\.cn|cq\.cn|he\.cn|nm\.cn|ln\.cn|jl\.cn|hl\.cn|js\.cn|zj\.cn|ah\.cn|gd\.cn|gx\.cn|hi\.cn|sc\.cn|gz\.cn|yn\.cn|xz\.cn|sn\.cn|gs\.cn|qh\.cn|nx\.cn|xj\.cn|tw\.cn|hk\.cn|mo\.cn|cn|cx|cz|de|dk|fo|com\.ec|tm\.fr|com\.fr|asso\.fr|presse\.fr|fr|gf|gs|co\.il|net\.il|ac\.il|k12\.il|gov\.il|muni\.il|ac\.in|co\.in|org\.in|ernet\.in|gov\.in|net\.in|res\.in|is|it|ac\.jp|co\.jp|go\.jp|or\.jp|ne\.jp|ac\.kr|co\.kr|go\.kr|ne\.kr|nm\.kr|or\.kr|li|lt|lu|asso\.mc|tm\.mc|com\.mm|org\.mm|net\.mm|edu\.mm|gov\.mm|ms|nl|no|nu|pl|ro|org\.ro|store\.ro|tm\.ro|firm\.ro|www\.ro|arts\.ro|rec\.ro|info\.ro|nom\.ro|nt\.ro|se|si|com\.sg|org\.sg|net\.sg|gov\.sg|sk|st|tf|ac\.th|co\.th|go\.th|mi\.th|net\.th|or\.th|tm|to|com\.tr|edu\.tr|gov\.tr|k12\.tr|net\.tr|org\.tr|com\.tw|org\.tw|net\.tw|ac\.uk|uk\.com|uk\.net|gb\.com|gb\.net|vg|sh|kz|ch|info|ua|gov|name|pro|ie|hk|com\.hk|org\.hk|net\.hk|edu\.hk|us|tk|cd|by|ad|lv|eu\.lv|bz|es|jp|cl|ag|mobi|eu|co\.nz|org\.nz|net\.nz|maori\.nz|iwi\.nz|io|la|md|sc|sg|vc|tw|travel|my|se|tv|pt|com\.pt|edu\.pt|asia|fi|com\.ve|net\.ve|fi|org\.ve|web\.ve|info\.ve|co\.ve|tel|im|gr|ru|net\.ru|org\.ru|hr|com\.hr|ly|xyz)$/);
  }
  function t(e, t) {
    var o = e.charAt(0);
    t = t.split(o);
    if (o === e) {
      return t;
    } else {
      return t[(e = parseInt(e.substring(1), 10)) < 0 ? t.length + e : e - 1];
    }
  }
  function o(e, t) {
    var o;
    var i = e.charAt(0);
    var n = t.split("&");
    var r = [];
    var a = {};
    var s = e.substring(1);
    var l = 0;
    for (var u = n.length; l < u; l++) {
      if ("" !== (r = (r = n[l].match(/(.*?)=(.*)/)) || [n[l], n[l], ""])[1].replace(/\s/g, "")) {
        r[2] = (o = r[2] || "", c.decodeURIComponent(o.replace(/\+/g, " ")));
        if (s === r[1]) {
          return r[2];
        }
        if (o = r[1].match(/(.*)\[([0-9]+)\]/)) {
          a[o[1]] = a[o[1]] || [];
          a[o[1]][o[2]] = r[2];
        } else {
          a[r[1]] = r[2];
        }
      }
    }
    if (i === e) {
      return a;
    } else {
      return a[s];
    }
  }
  return function (i, n) {
    var r;
    var a = {};
    if ("tld?" === i) {
      return e();
    }
    n = n || window.location.toString();
    if (!i) {
      return n;
    }
    i = i.toString();
    if (n.match(/^mailto:([^/].+)/)) {
      r = n.match(/^mailto:([^/].+)/);
      a.protocol = "mailto";
      a.email = r[1];
    } else {
      if ((n = n.match(/(.*?)\/#!(.*)/) ? (r = n.match(/(.*?)\/#!(.*)/))[1] + r[2] : n).match(/(.*?)#(.*)/)) {
        r = n.match(/(.*?)#(.*)/);
        a.hash = r[2];
        n = r[1];
      }
      if (a.hash && i.match(/^#/)) {
        return o(i, a.hash);
      }
      if (n.match(/(.*?)\?(.*)/)) {
        r = n.match(/(.*?)\?(.*)/);
        a.query = r[2];
        n = r[1];
      }
      if (a.query && i.match(/^\?/)) {
        return o(i, a.query);
      }
      if (n.match(/(.*?):?\/\/(.*)/)) {
        r = n.match(/(.*?):?\/\/(.*)/);
        a.protocol = r[1].toLowerCase();
        n = r[2];
      }
      if (n.match(/(.*?)(\/.*)/)) {
        r = n.match(/(.*?)(\/.*)/);
        a.path = r[2];
        n = r[1];
      }
      a.path = (a.path || "").replace(/^([^/])/, "/$1").replace(/\/$/, "");
      if ((i = i.match(/^[-0-9]+$/) ? i.replace(/^([^/])/, "/$1") : i).match(/^\//)) {
        return t(i, a.path.substring(1));
      }
      if (r = (r = t("/-1", a.path.substring(1))) && r.match(/(.*?)\.(.*)/)) {
        a.file = r[0];
        a.filename = r[1];
        a.fileext = r[2];
      }
      if (n.match(/(.*):([0-9]+)$/)) {
        r = n.match(/(.*):([0-9]+)$/);
        a.port = r[2];
        n = r[1];
      }
      if (n.match(/(.*?)@(.*)/)) {
        r = n.match(/(.*?)@(.*)/);
        a.auth = r[1];
        n = r[2];
      }
      if (a.auth) {
        r = a.auth.match(/(.*):(.*)/);
        a.user = r ? r[1] : a.auth;
        a.pass = r ? r[2] : undefined;
      }
      a.hostname = n.toLowerCase();
      if ("." === i.charAt(0)) {
        return t(i, a.hostname);
      }
      if (e() && (r = a.hostname.match(e()))) {
        a.tld = r[3];
        a.domain = r[2] ? r[2] + "." + r[3] : undefined;
        a.sub = r[1] || undefined;
      }
      n = a.port ? ":" + a.port : "";
      a.protocol = a.protocol || window.location.protocol.replace(":", "");
      a.port = a.port || ("https" === a.protocol ? "443" : "80");
      a.protocol = a.protocol || ("443" === a.port ? "https" : "http");
      a.basic = a.protocol + "://" + a.hostname + n;
    }
    if (i in a) {
      return a[i];
    } else {
      if ("{}" === i) {
        return a;
      } else {
        return "";
      }
    }
  };
}();
c.createString = function (e) {
  var t = e;
  for (var o = Math.random().toString(36).substr(2); o.length < t;) {
    o += Math.random().toString(36).substr(2);
  }
  return o.substr(0, e);
};
c.createAesKey = function () {
  return c.createString(16);
};
c.generateEncryptyData = function (e, t) {
  if (undefined !== t) {
    var o = t.publicKey;
    t = t.version;
    if (undefined !== o && undefined !== t && "undefined" != typeof CryptoJS && "undefined" != typeof JSEncrypt) {
      var i = c.createAesKey();
      try {
        var n = CryptoJS.enc.Utf8.parse(i);
        var r = CryptoJS.enc.Utf8.parse(JSON.stringify(e));
        var a = c.isUndefined(CryptoJS.pad.Pkcs7) ? CryptoJS.pad.PKCS7 : CryptoJS.pad.Pkcs7;
        var s = CryptoJS.AES.encrypt(r, n, {
          mode: CryptoJS.mode.ECB,
          padding: a
        }).toString();
        var l = new JSEncrypt();
        l.setPublicKey(o);
        var u = l.encrypt(i);
        if (false === u) {
          return _.warn("Encryption failed, return the original data"), e;
        } else {
          return {
            pkv: t,
            ekey: u,
            payload: s
          };
        }
      } catch (e) {
        _.warn("Encryption failed, return the original data: " + e);
      }
    }
  }
  return e;
};
c.getUtm = function () {
  var e = {};
  c.each(m, function (t) {
    try {
      var o = c.getQueryParam(location.href, t);
      o.length && (e[t] = o);
    } catch (t) {
      _.warn("get utm fail: " + t);
    }
  });
  return JSON.stringify(e);
};
c.getQueryParam = function (e, t) {
  t = t.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");
  e = c.decodeURIComponent(e);
  if (null === (t = new RegExp("[\\?&]" + t + "=([^&#]*)").exec(e)) || t && "string" != typeof t[1] && t[1].length) {
    return "";
  } else {
    return c.decodeURIComponent(t[1]);
  }
};
c.getUtmFromQuery = function (e) {
  var t = {};
  c.each(m, function (o) {
    e[o] && (t[o] = e[o]);
  });
  return JSON.stringify(t);
};
c.indexOf = function (e, t) {
  var o = e.indexOf;
  if (o) {
    return o.call(e, t);
  }
  for (var i = 0; i < e.length; i++) {
    if (t === e[i]) {
      return i;
    }
  }
  return -1;
};
c.checkCalibration = function (e) {
  return e;
};
var _ = "object" === o(_) ? _ : {};
_.info = function () {
  if ("object" === ("undefined" == typeof console ? "undefined" : o(console)) && console.log && _.enabled) {
    try {
      return console.log.apply(console, arguments);
    } catch (e) {
      console.log(arguments[0]);
    }
  }
};
_.warn = function () {
  if ("object" === ("undefined" == typeof console ? "undefined" : o(console)) && console.log && _.enabled) {
    try {
      return console.warn.apply(console, arguments);
    } catch (e) {
      console.warn(arguments[0]);
    }
  }
};
var v = function () {
  function e() {
    i(this, e);
    this.config = {
      persistenceName: "thinkingdata",
      persistenceNameOld: "thinkingdata_mg"
    };
  }
  r(e, [{
    key: "getConfig",
    value: function () {
      return this.config;
    }
  }, {
    key: "getStorage",
    value: function (e, t, o) {
      e = localStorage.getItem(e);
      if (!t) {
        if (c.isJSONString(e)) {
          return JSON.parse(e);
        } else {
          return {};
        }
      }
      if (c.isJSONString(e)) {
        o(JSON.parse(e));
      } else {
        o({});
      }
    }
  }, {
    key: "setStorage",
    value: function (e, t) {
      localStorage.setItem(e, t);
    }
  }, {
    key: "removeStorage",
    value: function (e) {
      localStorage.removeItem(e);
    }
  }, {
    key: "_setSystemProxy",
    value: function (e) {
      this._sysCallback = e;
    }
  }, {
    key: "getSystemInfo",
    value: function (e) {
      var t = {
        mp_platform: "web",
        system: this._getOs(),
        screenWidth: window.screen.width,
        screenHeight: window.screen.height,
        systemLanguage: navigator.language
      };
      this._sysCallback && (t = c.extend(t, this._sysCallback(e)));
      e.success(t);
      e.complete();
    }
  }, {
    key: "_getOs",
    value: function () {
      var e = navigator.userAgent;
      if (/Windows/i.test(e)) {
        if (/Phone/.test(e) || /WPDesktop/.test(e)) {
          return "Windows Phone";
        } else {
          return "Windows";
        }
      } else {
        if (/(iPhone|iPad|iPod)/.test(e)) {
          return "iOS";
        } else {
          if (/Android/.test(e)) {
            return "Android";
          } else {
            if (/(BlackBerry|PlayBook|BB10)/i.test(e)) {
              return "BlackBerry";
            } else {
              if (/Mac/i.test(e)) {
                return "MacOS";
              } else {
                if (/Linux/.test(e)) {
                  return "Linux";
                } else {
                  if (/CrOS/.test(e)) {
                    return "ChromeOS";
                  } else {
                    return "";
                  }
                }
              }
            }
          }
        }
      }
    }
  }, {
    key: "getNetworkType",
    value: function (e) {
      e.complete();
    }
  }, {
    key: "onNetworkStatusChange",
    value: function () {}
  }, {
    key: "request",
    value: function (e) {
      var t = {};
      var o = new XMLHttpRequest();
      o.open(e.method, e.url);
      if (e.header) {
        for (var i in e.header) {
          o.setRequestHeader(i, e.header[i]);
        }
      }
      o.onreadystatechange = function () {
        if (4 === o.readyState && 200 === o.status) {
          t.statusCode = 200;
          c.isJSONString(o.responseText) && (t.data = JSON.parse(o.responseText));
          e.success(t);
        } else if (200 !== o.status) {
          t.errMsg = "network error", e.fail(t);
        }
      };
      o.ontimeout = function () {
        t.errMsg = "timeout";
        e.fail(t);
      };
      o.send(e.data);
      return o;
    }
  }, {
    key: "initAutoTrackInstance",
    value: function (e, t) {
      this.instance = e;
      this.autoTrack = t.autoTrack;
      var o = this;
      if ("onpagehide" in window) {
        window.onpagehide = function () {
          o.onPageHide(true);
        };
      } else {
        window.onbeforeunload = function () {
          o.onPageHide(true);
        };
      }
      o.onPageShow();
      o.autoTrack.appHide && o.instance.timeEvent("ta_page_hide");
      "onvisibilitychange" in document && (document.onvisibilitychange = function () {
        if (document.hidden) {
          o.onPageHide(false);
        } else {
          o.onPageShow();
          o.autoTrack.appHide && o.instance.timeEvent("ta_page_hide");
        }
      });
    }
  }, {
    key: "setGlobal",
    value: function (e, t) {
      window[t] = e;
    }
  }, {
    key: "getAppOptions",
    value: function () {}
  }, {
    key: "showToast",
    value: function () {}
  }, {
    key: "onPageShow",
    value: function () {
      var e;
      if (this.autoTrack.appShow) {
        c.extend(e = {}, this.autoTrack.properties);
        c.isFunction(this.autoTrack.callback) && c.extend(e, this.autoTrack.callback("appShow"));
        this.instance._internalTrack("ta_page_show", e);
      }
    }
  }, {
    key: "onPageHide",
    value: function (e) {
      var t;
      if (this.autoTrack.appHide) {
        c.extend(t = {}, this.autoTrack.properties);
        c.isFunction(this.autoTrack.callback) && c.extend(t, this.autoTrack.callback("appHide"));
        this.instance._internalTrack("ta_page_hide", t, new Date(), null, e);
      }
    }
  }], [{
    key: "createInstance",
    value: function () {
      return new e();
    }
  }]);
  return e;
}();
var M = function () {
  function e(t, o) {
    i(this, e);
    this.taInstance = t;
    this.config = o || {};
    this.referrer = "Directly open";
    if (this.config.isPlugin) {
      t.App = function () {
        App.apply(this, arguments);
      };
      inension(t.Page);
    } else {
      o = App;
      App = this._initAppExtention(o);
      t = Page;
      Page = this._initPageExtension(t);
    }
  }
  r(e, [{
    key: "_initPageExtension",
    value: function (e) {
      var t = this;
      return function (o) {
        var i = o.onShow;
        var n = o.onShareAppMessage;
        o.onShow = function (e) {
          t.onPageShow();
          "function" == typeof i && i.call(this, e);
        };
        "function" == typeof n && (o.onShareAppMessage = function (e) {
          e = n.call(this, e);
          return t.onPageShare(e);
        });
        return e(o);
      };
    }
  }, {
    key: "_initAppExtention",
    value: function (e) {
      var t = this;
      return function (o) {
        var i = o.onLaunch;
        var n = o.onShow;
        var r = o.onHide;
        o.onLaunch = function (e) {
          t.onAppLaunch(e, this);
          "function" == typeof i && i.call(this, e);
        };
        o.onShow = function (e) {
          t.onAppShow(e);
          "function" == typeof n && n.call(this, e);
        };
        o.onHide = function () {
          t.onAppHide();
          "function" == typeof r && r.call(this);
        };
        return e(o);
      };
    }
  }, {
    key: "onAppLaunch",
    value: function (e, t) {
      this._setAutoTrackProperties(e);
      c.isUndefined(t) || (t[this.taInstance.name] = this.taInstance);
      if (this.config.appLaunch) {
        t = {};
        e && e.path && (t["#url_path"] = this._getPath(e.path));
        e && e.query && (t["#utm"] = c.getUtmFromQuery(e.query));
        this.taInstance._internalTrack("ta_mp_launch", t);
      }
    }
  }, {
    key: "onAppShow",
    value: function (e) {
      var t;
      this.config.appHide && this.taInstance.timeEvent("ta_mp_hide");
      this._setAutoTrackProperties(e);
      if (this.config.appShow) {
        t = {};
        e && e.path && (t["#url_path"] = this._getPath(e.path));
        e && e.query && (t["#utm"] = c.getUtmFromQuery(e.query));
        c.extend(t, this.config.properties);
        c.isFunction(this.config.callback) && c.extend(t, this.config.callback("appShow"));
        this.taInstance._internalTrack("ta_mp_show", t);
      }
    }
  }, {
    key: "onAppHide",
    value: function () {
      var e;
      if (this.config.appHide) {
        e = {
          "#url_path": this._getCurrentPath()
        };
        c.extend(e, this.config.properties);
        c.isFunction(this.config.callback) && c.extend(e, this.config.callback("appHide"));
        this.taInstance._internalTrack("ta_mp_hide", e);
      }
    }
  }, {
    key: "_getCurrentPath",
    value: function () {
      var e = "Not to get";
      try {
        var t = getCurrentPages();
        e = t[t.length - 1].route;
      } catch (e) {
        _.info(e);
      }
      return e;
    }
  }, {
    key: "_setAutoTrackProperties",
    value: function (e) {
      e = {
        "#scene": e.scene
      };
      this.taInstance._setAutoTrackProperties(e);
    }
  }, {
    key: "_getPath",
    value: function (e) {
      if ("string" == typeof e) {
        return e.replace(/^\//, "");
      } else {
        return "Abnormal values";
      }
    }
  }, {
    key: "onPageShare",
    value: function (e) {
      this.config.pageShare && this.taInstance._internalTrack("ta_mp_share", {
        "#url_path": this._getCurrentPath()
      });
      if (c.isObject(e)) {
        return e;
      } else {
        return {};
      }
    }
  }, {
    key: "onPageShow",
    value: function () {
      var e;
      var t;
      if (this.config.pageShow) {
        t = {
          "#url_path": (e = this._getCurrentPath()) || "The system did not get a value",
          "#referrer": this.referrer
        };
        this.referrer = e;
        this.taInstance._internalTrack("ta_mp_view", t);
      }
    }
  }]);
  return e;
}();
var b = function () {
  function e(t, o, n) {
    var r = this;
    i(this, e);
    this.taInstance = t;
    this.config = o || {};
    t = n.getLaunchOptionsSync();
    this._onShow(t);
    this.startTracked = true;
    n.onShow(function (e) {
      r._onShow(e);
    });
    n.onHide(function () {
      var e;
      r.startTracked = false;
      if (r.config.appHide) {
        c.extend(e = {}, r.config.properties);
        c.isFunction(r.config.callback) && c.extend(e, r.config.callback("appHide"));
        r.taInstance._internalTrack("ta_mg_hide", e);
      }
    });
  }
  r(e, [{
    key: "_onShow",
    value: function (e) {
      if (!this.startTracked) {
        this.config.appHide && this.taInstance.timeEvent("ta_mg_hide");
        e && e.scene && this.taInstance._setAutoTrackProperties({
          "#scene": e.scene
        });
        if (this.config.appShow) {
          c.extend(e = {}, this.config.properties), c.isFunction(this.config.callback) && c.extend(e, this.config.callback("appShow")), this.taInstance._internalTrack("ta_mg_show", e);
        }
      }
    }
  }]);
  return e;
}();
var C = function () {
  function e(t, o, n) {
    i(this, e);
    this.api = t;
    this.config = o;
    this._config = n;
  }
  r(e, [{
    key: "getConfig",
    value: function () {
      return this.config;
    }
  }, {
    key: "getStorage",
    value: function (e, t, o) {
      if (!t) {
        if ("dd_mp" === this._config.platform) {
          return t = this.api.getStorageSync({
            key: e
          }), c.isJSONString(t.data) ? JSON.parse(t.data) : {};
        } else {
          return t = this.api.getStorageSync(e), c.isJSONString(t) ? JSON.parse(t) : {};
        }
      }
      this.api.getStorage({
        key: e,
        success: function (e) {
          e = c.isJSONString(e.data) ? JSON.parse(e.data) : {};
          o(e);
        },
        fail: function () {
          _.warn("getStorage faild");
          o({});
        }
      });
    }
  }, {
    key: "setStorage",
    value: function (e, t) {
      this.api.setStorage({
        key: e,
        data: t
      });
    }
  }, {
    key: "removeStorage",
    value: function (e) {
      if (c.isFunction(this.api.removeStorage)) {
        this.api.removeStorage({
          key: e
        });
      } else {
        c.isFunction(this.api.deleteStorage) && this.api.deleteStorage({
          key: e
        });
      }
    }
  }, {
    key: "_getPlatform",
    value: function () {
      return "";
    }
  }, {
    key: "getSystemInfo",
    value: function (e) {
      var t = this._config.mpPlatform;
      this.api.getSystemInfo({
        success: function (o) {
          if (c.isFunction(t)) {
            o.mp_platform = t(o);
          } else {
            o.mp_platform = t;
          }
          e.success(o);
          "wechat" === t && e.complete();
        },
        complete: function () {
          e.complete();
        }
      });
    }
  }, {
    key: "getNetworkType",
    value: function (e) {
      if (c.isFunction(this.api.getNetworkType)) {
        this.api.getNetworkType({
          success: function (t) {
            e.success(t);
          },
          complete: function () {
            e.complete();
          }
        });
      } else {
        e.success({});
        e.complete();
      }
    }
  }, {
    key: "onNetworkStatusChange",
    value: function (e) {
      if (c.isFunction(this.api.onNetworkStatusChange)) {
        this.api.onNetworkStatusChange(e);
      } else {
        e({});
      }
    }
  }, {
    key: "request",
    value: function (e) {
      var t;
      if ("ali_mp" === this._config.platform || "dd_mp" === this._config.platform) {
        return (t = c.extend({}, e)).headers = e.header, t.success = function (t) {
          t.statusCode = t.status;
          e.success(t);
        }, t.fail = function (t) {
          t.errMsg = t.errorMessage;
          e.fail(t);
        }, "dd_mp" === this._config.platform ? this.api.httpRequest(t) : this.api.request(t);
      } else {
        return this.api.request(e);
      }
    }
  }, {
    key: "initAutoTrackInstance",
    value: function (e, t) {
      c.isObject(t.autoTrack) && (t.autoTrack.isPlugin = t.is_plugin);
      return new (this._config.mp ? M : b)(e, t.autoTrack, this.api);
    }
  }, {
    key: "setGlobal",
    value: function (e, t) {
      if (this._config.mp) {
        _.warn("ThinkingAnalytics: we do not set global name for TA instance when you do not enable auto track.");
      } else {
        GameGlobal[t] = e;
      }
    }
  }, {
    key: "getAppOptions",
    value: function (e) {
      var t = {};
      try {
        t = this.api.getLaunchOptionsSync();
      } catch (e) {
        _.warn("Cannot get launch options.");
      }
      if (c.isFunction(e)) {
        try {
          if (this._config.mp) {
            this.api.onAppShow(e);
          } else {
            this.api.onShow(e);
          }
        } catch (e) {
          _.warn("Cannot register onShow callback.");
        }
      }
      return t;
    }
  }, {
    key: "showToast",
    value: function (e) {
      var t;
      if (c.isFunction(this.api.showToast)) {
        t = {
          title: e
        };
        "dd_mp" !== this._config.platform && "ali_mp" !== this._config.platform || (t.content = e);
        this.api.showToast(t);
      }
    }
  }], [{
    key: "createInstance",
    value: function () {
      return this._createInstance("R_CURRENT_PLATFORM");
    }
  }, {
    key: "_createInstance",
    value: function (t) {
      switch (t) {
        case "wechat_mp":
          return new e(wx, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_wechat"
          }, {
            mpPlatform: "wechat",
            mp: true,
            platform: t
          });
        case "wechat_mg":
          return new e(wx, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_wechat_game"
          }, {
            mpPlatform: "wechat",
            platform: t
          });
        case "qq_mp":
          return new e(qq, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_qq"
          }, {
            mpPlatform: "qq",
            mp: true,
            platform: t
          });
        case "qq_mg":
          return new e(qq, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_qq_game"
          }, {
            mpPlatform: "qq",
            platform: t
          });
        case "baidu_mp":
          return new e(swan, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_swan"
          }, {
            mpPlatform: function (e) {
              return e.host;
            },
            mp: true,
            platform: t
          });
        case "baidu_mg":
          return new e(swan, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_swan_game"
          }, {
            mpPlatform: function (e) {
              return e.host;
            },
            platform: t
          });
        case "tt_mg":
          return new e(tt, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_tt_game"
          }, {
            mpPlatform: function (e) {
              return e.appName;
            },
            platform: t
          });
        case "tt_mp":
          return new e(tt, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_tt"
          }, {
            mpPlatform: function (e) {
              return e.appName;
            },
            mp: true,
            platform: t
          });
        case "ali_mp":
          return new e(my, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_ali"
          }, {
            mpPlatform: function (e) {
              return e.app;
            },
            mp: true,
            platform: t
          });
        case "dd_mp":
          return new e(dd, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_dd"
          }, {
            mpPlatform: "dingding",
            mp: true,
            platform: t
          });
        case "bl_mg":
          return new e(bl, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_mg"
          }, {
            mpPlatform: "bilibili",
            platform: t
          });
        case "kuaishou_mp":
          return new e(ks, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_kuaishou"
          }, {
            mpPlatform: "kuaishou",
            mp: true,
            platform: t
          });
        case "qh360_mg":
          return new e(qh, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_qh360"
          }, {
            mpPlatform: "qh360",
            platform: t
          });
        case "tb_mp":
          return new e(my, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_tb"
          }, {
            mpPlatform: "tb",
            mp: true,
            platform: t
          });
        case "jd_mp":
          return new e(jd, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_jd"
          }, {
            mpPlatform: "jd",
            mp: true,
            platform: t
          });
        case "qh360_mp":
          return new e(qh, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_qh360"
          }, {
            mpPlatform: "qh360",
            mp: true,
            platform: t
          });
        case "WEB":
          return new v.createInstance();
      }
    }
  }]);
  return e;
}();
var w = r(function e(t, o) {
  var n = this;
  i(this, e);
  this.taInstance = t;
  this.config = o || {};
  this.config.appShow && this.taInstance._internalTrack("ta_mg_show");
  this.config.appHide && this.taInstance.timeEvent("ta_mg_hide");
  qg.onShow(function () {
    var e;
    n.config.appHide && n.taInstance.timeEvent("ta_mg_hide");
    if (n.config.appShow) {
      c.extend(e = {}, n.config.properties);
      c.isFunction(n.config.callback) && c.extend(e, n.config.callback("appShow"));
      n.taInstance._internalTrack("ta_mg_show");
    }
  });
  qg.onHide(function () {
    var e;
    if (n.config.appHide) {
      c.extend(e = {}, n.config.properties);
      c.isFunction(n.config.callback) && c.extend(e, n.config.callback("appHide"));
      n.taInstance._internalTrack("ta_mg_hide");
    }
  });
});
var S = function () {
  function e() {
    i(this, e);
    this.config = {
      persistenceName: "thinkingdata",
      persistenceNameOld: "thinkingdata_qg_vivo_game",
      asyncPersistence: true
    };
  }
  r(e, [{
    key: "getConfig",
    value: function () {
      return this.config || {};
    }
  }, {
    key: "getStorage",
    value: function (e, t, o) {
      if (!t) {
        t = qg.getStorageSync({
          key: e
        });
        if (c.isJSONString(t)) {
          return JSON.parse(t);
        } else {
          return {};
        }
      }
      qg.getStorage({
        key: e,
        success: function (e) {
          e = c.isJSONString(e) ? JSON.parse(e) : {};
          o(e);
        },
        fail: function () {
          o({});
        }
      });
    }
  }, {
    key: "setStorage",
    value: function (e, t) {
      qg.setStorage({
        key: e,
        value: t
      });
    }
  }, {
    key: "removeStorage",
    value: function (e) {
      qg.deleteStorage({
        key: e
      });
    }
  }, {
    key: "getSystemInfo",
    value: function (e) {
      qg.getSystemInfo({
        success: function (t) {
          var o = t;
          var i = [t.osType, t.osVersionName].join(" ");
          o.brand = t.manufacturer;
          o.system = i;
          o.mp_platform = "vivo_qg";
          e.success(o);
        },
        complete: function () {
          e.complete();
        }
      });
    }
  }, {
    key: "getNetworkType",
    value: function (e) {
      qg.getNetworkType({
        success: function (t) {
          var o = t;
          o.networkType = t.type;
          e.success(o);
        },
        complete: function () {
          e.complete();
        }
      });
    }
  }, {
    key: "onNetworkStatusChange",
    value: function (e) {
      qg.subscribeNetworkStatus({
        callback: function (t) {
          var o = t;
          o.networkType = t.type;
          e(o);
        }
      });
    }
  }, {
    key: "request",
    value: function (e) {
      return qg.request({
        url: e.url,
        data: e.data,
        method: e.method,
        header: e.header,
        success: function (t) {
          e.success(t);
        },
        fail: function (t) {
          e.fail(t);
        }
      });
    }
  }, {
    key: "initAutoTrackInstance",
    value: function (e, t) {
      return new w(e, t.autoTrack);
    }
  }, {
    key: "setGlobal",
    value: function (e, t) {
      globalThis[t] = e;
    }
  }, {
    key: "getAppOptions",
    value: function () {
      return {};
    }
  }, {
    key: "showToast",
    value: function (e) {
      qg.showToast({
        message: e,
        duration: 0
      });
    }
  }], [{
    key: "createInstance",
    value: function () {
      return new e();
    }
  }]);
  return e;
}();
var k = r(function e(t, o, n) {
  var r = this;
  i(this, e);
  this.taInstance = t;
  this.config = o || {};
  if (this.config.appShow) {
    c.extend(t = {}, this.config.properties);
    c.isFunction(this.config.callback) && c.extend(t, this.config.callback("appShow"));
    this.taInstance._internalTrack("ta_mg_show", t);
  }
  this.config.appHide && this.taInstance.timeEvent("ta_mg_hide");
  n.onShow(function () {
    var e;
    r.config.appHide && r.taInstance.timeEvent("ta_mg_hide");
    if (r.config.appShow) {
      c.extend(e = {}, r.config.properties);
      c.isFunction(r.config.callback) && c.extend(e, r.config.callback("appShow"));
      r.taInstance._internalTrack("ta_mg_show", e);
    }
  });
  n.onHide(function () {
    var e;
    if (r.config.appHide) {
      c.extend(e = {}, r.config.properties);
      c.isFunction(r.config.callback) && c.extend(e, r.config.callback("appHide"));
      r.taInstance._internalTrack("ta_mg_hide", e);
    }
  });
});
var P = function () {
  function e(t, o, n) {
    i(this, e);
    this.api = t;
    this.config = o;
    this._config = n;
  }
  r(e, [{
    key: "getConfig",
    value: function () {
      return this.config || {};
    }
  }, {
    key: "getStorage",
    value: function (e, t, o) {
      e = localStorage.getItem(e);
      if (!t) {
        if (c.isJSONString(e)) {
          return JSON.parse(e);
        } else {
          return {};
        }
      }
      if (c.isJSONString(e)) {
        o(JSON.parse(e));
      } else {
        o({});
      }
    }
  }, {
    key: "setStorage",
    value: function (e, t) {
      localStorage.setItem(e, t);
    }
  }, {
    key: "removeStorage",
    value: function (e) {
      localStorage.removeItem(e);
    }
  }, {
    key: "getSystemInfo",
    value: function (e) {
      var t = this._config.mpPlatform;
      this.api.getSystemInfo({
        success: function (o) {
          o.mp_platform = t;
          e.success(o);
        },
        complete: function () {
          e.complete();
        }
      });
    }
  }, {
    key: "getNetworkType",
    value: function (e) {
      this.api.getNetworkType({
        success: function (t) {
          e.success(t);
        },
        complete: function () {
          e.complete();
        }
      });
    }
  }, {
    key: "onNetworkStatusChange",
    value: function (e) {
      this.api.onNetworkStatusChange({
        callback: function (t) {
          e(t);
        }
      });
    }
  }, {
    key: "request",
    value: function (e) {
      var t = {};
      var o = new XMLHttpRequest();
      o.open(e.method, e.url);
      if (e.header) {
        for (var i in e.header) {
          o.setRequestHeader(i, e.header[i]);
        }
      }
      o.onreadystatechange = function () {
        if (4 === o.readyState && 200 === o.status) {
          t.statusCode = 200;
          c.isJSONString(o.responseText) && (t.data = JSON.parse(o.responseText));
          e.success(t);
        } else if (200 !== o.status) {
          t.errMsg = "network error", e.fail(t);
        }
      };
      o.ontimeout = function () {
        t.errMsg = "timeout";
        e.fail(t);
      };
      o.send(e.data);
      return o;
    }
  }, {
    key: "initAutoTrackInstance",
    value: function (e, t) {
      return new k(e, t.autoTrack, this.api);
    }
  }, {
    key: "setGlobal",
    value: function (e, t) {
      globalThis[t] = e;
    }
  }, {
    key: "getAppOptions",
    value: function () {
      return this.api.getLaunchOptionsSync();
    }
  }, {
    key: "showToast",
    value: function (e) {
      this.api.showToast({
        title: e,
        icon: "none",
        duration: 2e3
      });
    }
  }], [{
    key: "createInstance",
    value: function () {
      return this._createInstance("R_CURRENT_PLATFORM");
    }
  }, {
    key: "_createInstance",
    value: function (t) {
      switch (t) {
        case "oppo":
          return new e(qg, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_qg_oppo_game"
          }, {
            mpPlatform: "oppo_qg"
          });
        case "huawei":
          return new e(hbs, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_qg_huawei_game"
          }, {
            mpPlatform: "huawei_qg"
          });
        case "mz":
          return new e(qg, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_qg_mz_game"
          }, {
            mpPlatform: "mz"
          });
        case "xiaomi":
          return new e(qg, {
            persistenceName: "thinkingdata",
            persistenceNameOld: "thinkingdata_qg"
          }, {
            mpPlatform: "xiaomi"
          });
      }
    }
  }]);
  return e;
}();
var O = function () {
  function e() {
    i(this, e);
  }
  r(e, null, [{
    key: "createInstance",
    value: function () {
      var e;
      var t = Object.freeze({
        WECHAT_GAME: 104,
        QQ_PLAY: 105,
        BAIDU_GAME: 107,
        VIVO_GAME: 108,
        OPPO_GAME: 109,
        HUAWEI_GAME: 110,
        XIAOMI_GAME: 111,
        BYTEDANCE_GAME: 117,
        QTT_GAME: 116,
        LINKSURE: 119,
        WECHAT_MINI_GAME: "WECHAT_GAME",
        BAIDU_MINI_GAME: "BAIDU_MINI_GAME",
        XIAOMI_QUICK_GAME: "XIAOMI_QUICK_GAME",
        OPPO_MINI_GAME: "OPPO_MINI_GAME",
        VIVO_MINI_GAME: "VIVO_MINI_GAME",
        HUAWEI_QUICK_GAME: "HUAWEI_QUICK_GAME",
        BYTEDANCE_MINI_GAME: "BYTEDANCE_MINI_GAME",
        QTT_MINI_GAME: "QTT_MINI_GAME",
        LINKSURE_MINI_GAME: "LINKSURE_MINI_GAME"
      });
      if (cc.sys.platform === t.WECHAT_GAME || cc.sys.platform === t.WECHAT_MINI_GAME) {
        return C._createInstance("wechat_mg");
      } else {
        if (cc.sys.platform === t.BAIDU_GAME || cc.sys.platform === t.BAIDU_MIN_GAME) {
          return C._createInstance("baidu_mg");
        } else {
          if (cc.sys.platform === t.VIVO_GAME || cc.sys.platform === t.VIVO_MINI_GAME) {
            return S.createInstance();
          } else {
            if (cc.sys.platform === t.QQ_PLAY) {
              return C._createInstance("qq_mg");
            } else {
              if (cc.sys.platform === t.OPPO_GAME || cc.sys.platform === t.OPPO_MINI_GAME) {
                return P._createInstance("oppo");
              } else {
                if (cc.sys.platform === t.HUAWEI_GAME || cc.sys.platform === t.HUAWEI_QUICK_GAME) {
                  return P._createInstance("huawei");
                } else {
                  if (cc.sys.platform === t.XIAOMI_GAME || cc.sys.platform === t.XIAOMI_QUICK_GAME) {
                    return P._createInstance("xiaomi");
                  } else {
                    if (cc.sys.platform === t.BYTEDANCE_GAME || cc.sys.platform === t.BYTEDANCE_MINI_GAME) {
                      return C._createInstance("tt_mg");
                    } else {
                      return (e = v.createInstance())._sysCallback = function () {
                        return {
                          system: cc.sys.os.replace(" ", "") + " " + cc.sys.osVersion
                        };
                      }, e.getNetworkType = function (e) {
                        var t = {};
                        switch (cc.sys.getNetworkType()) {
                          case cc.sys.NetworkType.LAN:
                            t.networkType = "WIFI";
                            break;
                          case cc.sys.NetworkType.WWAN:
                            t.networkType = "WWAN";
                            break;
                          default:
                            t.networkType = "NONE";
                        }
                        e.success(t);
                        e.complete();
                      }, e.getSystemInfo = function (t) {
                        var o = {
                          mp_platform: cc.sys.platform.toString(),
                          system: e._getOs(),
                          screenWidth: window.screen.width,
                          screenHeight: window.screen.height
                        };
                        e._sysCallback && (o = c.extend(o, e._sysCallback(t)));
                        t.success(o);
                        t.complete();
                      }, e;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }]);
  return e;
}();
var I = function () {
  function e() {
    i(this, e);
  }
  r(e, null, [{
    key: "_getCurrentPlatform",
    value: function () {
      return this.currentPlatform || (this.currentPlatform = O.createInstance());
    }
  }, {
    key: "getConfig",
    value: function () {
      return this._getCurrentPlatform().getConfig();
    }
  }, {
    key: "getStorage",
    value: function (e, t, o) {
      return this._getCurrentPlatform().getStorage(e, t, o);
    }
  }, {
    key: "setStorage",
    value: function (e, t) {
      return this._getCurrentPlatform().setStorage(e, t);
    }
  }, {
    key: "removeStorage",
    value: function (e) {
      return this._getCurrentPlatform().removeStorage(e);
    }
  }, {
    key: "getSystemInfo",
    value: function (e) {
      return this._getCurrentPlatform().getSystemInfo(e);
    }
  }, {
    key: "getNetworkType",
    value: function (e) {
      return this._getCurrentPlatform().getNetworkType(e);
    }
  }, {
    key: "onNetworkStatusChange",
    value: function (e) {
      this._getCurrentPlatform().onNetworkStatusChange(e);
    }
  }, {
    key: "request",
    value: function (e) {
      return this._getCurrentPlatform().request(e);
    }
  }, {
    key: "initAutoTrackInstance",
    value: function (e, t) {
      return this._getCurrentPlatform().initAutoTrackInstance(e, t);
    }
  }, {
    key: "setGlobal",
    value: function (e, t) {
      e && t && this._getCurrentPlatform().setGlobal(e, t);
    }
  }, {
    key: "getAppOptions",
    value: function (e) {
      return this._getCurrentPlatform().getAppOptions(e);
    }
  }, {
    key: "showDebugToast",
    value: function (e) {
      this._getCurrentPlatform().showToast(e);
    }
  }]);
  return e;
}();
var D = /^[a-zA-Z][a-zA-Z0-9_]{0,49}$/;
var T = function () {
  function e() {
    i(this, e);
  }
  r(e, null, [{
    key: "stripProperties",
    value: function (e) {
      c.isObject(e) && c.each(e, function (e, t) {
        c.isString(e) || c.isNumber(e) || c.isDate(e) || c.isBoolean(e) || c.isArray(e) || c.isObject(e) || _.warn("Your data -", t, e, "- format does not meet requirements and may not be stored correctly. Attribute values only support String, Number, Date, Boolean, Array, Object");
      });
      return e;
    }
  }, {
    key: "_checkPropertiesKey",
    value: function (e) {
      var t = true;
      c.each(e, function (e, o) {
        if (!D.test(o)) {
          _.warn("Invalid KEY: " + o);
          t = false;
        }
      });
      return t;
    }
  }, {
    key: "event",
    value: function (e) {
      return !(!c.isString(e) || !D.test(e)) || (_.warn("Check the parameter format. The eventName must start with an English letter and contain no more than 50 characters including letters, digits, and underscores: " + e), false);
    }
  }, {
    key: "propertyName",
    value: function (e) {
      return !(!c.isString(e) || !D.test(e)) || (_.warn("Check the parameter format. PropertyName must start with a letter and contain letters, digits, and underscores (_). The value is a string of no more than 50 characters: " + e), false);
    }
  }, {
    key: "properties",
    value: function (e) {
      this.stripProperties(e);
      return !(e && (c.isObject(e) ? !this._checkPropertiesKey(e) && (_.warn("Check the parameter format. The properties key must start with a letter, contain digits, letters, and underscores (_), and contain a maximum of 50 characters"), 1) : (_.warn("properties can be none, but it must be an object"), 1)));
    }
  }, {
    key: "propertiesMust",
    value: function (e) {
      this.stripProperties(e);
      if (undefined === e || !c.isObject(e) || c.isEmptyObject(e)) {
        return _.warn("properties must be an object with a value"), false;
      } else {
        return !!this._checkPropertiesKey(e) || (_.warn("Check the parameter format. The properties key must start with a letter, contain digits, letters, and underscores (_), and contain a maximum of 50 characters"), false);
      }
    }
  }, {
    key: "userId",
    value: function (e) {
      return !(!c.isString(e) || !/^.{1,64}$/.test(e)) || (_.warn("The user ID must be a string of less than 64 characters and cannot be null"), false);
    }
  }, {
    key: "userAddProperties",
    value: function (e) {
      if (!this.propertiesMust(e)) {
        return false;
      }
      for (var t in e) {
        if (!c.isNumber(e[t])) {
          _.warn("The attributes of userAdd need to be Number");
          return false;
        }
      }
      return true;
    }
  }, {
    key: "userAppendProperties",
    value: function (e) {
      if (!this.propertiesMust(e)) {
        return false;
      }
      for (var t in e) {
        if (!c.isArray(e[t])) {
          _.warn("The attribute of userAppend must be Array");
          return false;
        }
      }
      return true;
    }
  }]);
  return e;
}();
var A = function () {
  function e(t, o, n, r, a) {
    i(this, e);
    this.data = t;
    this.serverUrl = o;
    this.callback = a;
    this.tryCount = c.isNumber(n) ? n : 1;
    this.timeout = c.isNumber(r) ? r : 3e3;
    this.taClassName = "HttpTask";
  }
  r(e, [{
    key: "run",
    value: function () {
      var e = this;
      var t = c.createExtraHeaders();
      t["content-type"] = "application/json";
      var o = I.request({
        url: this.serverUrl,
        method: "POST",
        data: this.data,
        header: t,
        success: function (t) {
          e.onSuccess(t);
        },
        fail: function (t) {
          e.onFailed(t);
        }
      });
      setTimeout(function () {
        (c.isObject(o) || c.isPromise(o)) && c.isFunction(o.abort) && o.abort();
      }, this.timeout);
    }
  }, {
    key: "onSuccess",
    value: function (e) {
      if (200 === e.statusCode) {
        var t;
        switch (e.data.code) {
          case 0:
            t = "success";
            break;
          case -1:
            t = "invalid data";
            break;
          case -2:
            t = "invalid APP ID";
            break;
          default:
            t = "Unknown return code";
        }
        this.callback({
          code: e.data.code,
          msg: t
        });
      } else {
        this.callback({
          code: -3,
          msg: e.statusCode
        });
      }
    }
  }, {
    key: "onFailed",
    value: function (e) {
      if (0 < --this.tryCount) {
        this.run();
      } else {
        this.callback({
          code: -3,
          msg: e.errMsg
        });
      }
    }
  }]);
  return e;
}();
var R = function () {
  function e(t, o, n, r, a, s, l) {
    i(this, e);
    this.data = t;
    this.serverDebugUrl = o;
    this.callback = l;
    this.tryCount = c.isNumber(n) ? n : 1;
    this.timeout = c.isNumber(r) ? r : 3e3;
    this.dryrun = a;
    this.deviceId = s;
    this.taClassName = "HttpTaskDebug";
  }
  r(e, [{
    key: "run",
    value: function () {
      var e = this;
      var t = "appid=" + this.data["#app_id"] + "&source=client&dryRun=" + this.dryrun + "&deviceId=" + this.deviceId + "&data=" + encodeURIComponent(JSON.stringify(this.data.data[0]));
      var o = c.createExtraHeaders();
      o["content-type"] = "application/x-www-form-urlencoded";
      var i = I.request({
        url: this.serverDebugUrl,
        method: "POST",
        data: t,
        header: o,
        success: function (t) {
          e.onSuccess(t);
        },
        fail: function (t) {
          e.onFailed(t);
        }
      });
      setTimeout(function () {
        (c.isObject(i) || c.isPromise(i)) && c.isFunction(i.abort) && i.abort();
      }, this.timeout);
    }
  }, {
    key: "onSuccess",
    value: function (e) {
      if (200 === e.statusCode) {
        var t;
        if (0 === e.data.errorLevel) {
          t = "Verify data success.";
        } else if (1 === e.data.errorLevel) {
          var o = e.data.errorProperties;
          var i = "";
          for (var n = 0; n < o.length; n++) {
            var r = o[n].errorReason;
            i = i + " propertyName:" + o[n].propertyName + " errorReasons:" + r + "\n";
          }
          t = "Debug data error. errorLevel:" + e.data.errorLevel + " reason:" + i;
        } else {
          2 !== e.data.errorLevel && -1 !== e.data.errorLevel || (t = "Debug data error. errorLevel:" + e.data.errorLevel + " reason:" + e.data.errorReasons);
        }
        _.info(t);
        this.callback({
          code: e.data.errorLevel,
          msg: t
        });
      } else {
        this.callback({
          code: -3,
          msg: e.statusCode
        });
      }
    }
  }, {
    key: "onFailed",
    value: function (e) {
      if (0 < --this.tryCount) {
        this.run();
      } else {
        this.callback({
          code: -3,
          msg: e.errMsg
        });
      }
    }
  }]);
  return e;
}();
var B = new (function () {
  function e() {
    i(this, e);
    this.items = [];
    this.isRunning = false;
    this.showDebug = false;
  }
  r(e, [{
    key: "enqueue",
    value: function (e, t, o) {
      var i = !(3 < arguments.length && undefined !== arguments[3]) || arguments[3];
      var n = this;
      e = "debug" === o.debugMode ? new R(e, t, o.maxRetries, o.sendTimeout, 0, o.deviceId, function (e) {
        n.isRunning = false;
        c.isFunction(o.callback) && o.callback(e);
        n._runNext();
        if (!(false !== n.showDebug || 0 !== e.code && 1 !== e.code && 2 !== e.code)) {
          n.showDebug = true;
          c.isFunction(I.showDebugToast) && I.showDebugToast("The current mode is Debug");
        }
      }) : "debugOnly" === o.debugMode ? new R(e, t, o.maxRetries, o.sendTimeout, 1, o.deviceId, function (e) {
        n.isRunning = false;
        c.isFunction(o.callback) && o.callback(e);
        n._runNext();
        if (!(false !== n.showDebug || 0 !== e.code && 1 !== e.code && 2 !== e.code)) {
          n.showDebug = true;
          c.isFunction(I.showDebugToast) && I.showDebugToast("The current mode is debugOnly");
        }
      }) : new A(JSON.stringify(e), t, o.maxRetries, o.sendTimeout, function (e) {
        n.isRunning = false;
        c.isFunction(o.callback) && o.callback(e);
        n._runNext();
      });
      if (true === i) {
        this.items.push(e);
        this._runNext();
      } else {
        e.run();
      }
    }
  }, {
    key: "_dequeue",
    value: function () {
      return this.items.shift();
    }
  }, {
    key: "_runNext",
    value: function () {
      if (0 < this.items.length && !this.isRunning) {
        this.isRunning = true;
        if ("HttpTask" !== this.items[0].taClassName) {
          this._dequeue().run();
        } else {
          var e = this.items.splice(0, this.items.length);
          var t = e[0];
          var o = JSON.parse(t.data);
          var i = o["#app_id"];
          var n = [];
          n.push(t.callback);
          for (var r = 1; r < e.length; r++) {
            var a = e[r];
            var s = JSON.parse(a.data);
            if (s["#app_id"] === i && t.serverUrl === a.serverUrl) {
              o.data = o.data.concat(s.data);
              n.push(a.callback);
            } else {
              this.items.push(a);
            }
          }
          var c = new Date().getTime();
          o["#flush_time"] = c;
          new A(JSON.stringify(o), t.serverUrl, t.tryCount, t.timeout, function (e) {
            for (var t in n) {
              Object.hasOwnProperty.call(n, t) && (0, n[t])(e);
            }
          }).run();
        }
      }
    }
  }]);
  return e;
}())();
var L = {
  name: "thinkingdata",
  is_plugin: false,
  maxRetries: 3,
  sendTimeout: 3e3,
  enablePersistence: true,
  asyncPersistence: false,
  enableLog: true,
  strict: false,
  debugMode: "none",
  enableCalibrationTime: false
};
var N = {
  properties: {
    "#lib": "MG",
    "#lib_version": "2.2.1"
  },
  initDeviceId: function (e) {
    c.isString(e) && (this.properties["#device_id"] = e);
  },
  getSystemInfo: function (e) {
    var t = this;
    I.onNetworkStatusChange(function (e) {
      t.properties["#network_type"] = e.networkType;
    });
    I.getNetworkType({
      success: function (e) {
        t.properties["#network_type"] = e.networkType;
      },
      complete: function () {
        I.getSystemInfo({
          success: function (e) {
            _.info(JSON.stringify(e, null, 4));
            var o = e.system ? e.system.replace(/\s+/g, " ").split(" ") : [];
            o = {
              "#manufacturer": e.brand,
              "#device_model": e.model,
              "#screen_width": Number(e.screenWidth),
              "#screen_height": Number(e.screenHeight),
              "#os": o[0],
              "#os_version": o[1],
              "#mp_platform": e.mp_platform,
              "#system_language": e.systemLanguage
            };
            c.extend(t.properties, o);
            c.setMpPlatform(e.mp_platform);
          },
          complete: function () {
            e();
          }
        });
      }
    });
  }
};
var E = function () {
  function e(t, o) {
    var n = this;
    i(this, e);
    this.enabled = t.enablePersistence;
    if (this.enabled) {
      if (t.isChildInstance) {
        this.name = t.persistenceName + "_" + t.name, this.nameOld = t.persistenceNameOld + "_" + t.name;
      } else {
        this.name = t.persistenceName, this.nameOld = t.persistenceNameOld;
      }
      if (t.asyncPersistence) {
        this._state = {}, I.getStorage(this.name, true, function (e) {
          if (c.isEmptyObject(e)) {
            I.getStorage(n.nameOld, true, function (e) {
              n._state = c.extend2Layers({}, e, n._state);
              n._init(t, o);
              n._save();
            });
          } else {
            n._state = c.extend2Layers({}, e, n._state);
            n._init(t, o);
            n._save();
          }
        });
      } else {
        this._state = I.getStorage(this.name) || {}, c.isEmptyObject(this._state) && (this._state = I.getStorage(this.nameOld) || {}), this._init(t, o);
      }
    } else {
      this._state = {};
      this._init(t, o);
    }
  }
  r(e, [{
    key: "_init",
    value: function (e, t) {
      this.getDistinctId() || this.setDistinctId(c.UUID());
      if (!e.isChildInstance) {
        this.getDeviceId() || this._setDeviceId(c.UUID());
        N.initDeviceId(this.getDeviceId());
      }
      this.initComplete = true;
      "function" == typeof t && t();
      this._save();
    }
  }, {
    key: "_save",
    value: function () {
      this.enabled && this.initComplete && I.setStorage(this.name, JSON.stringify(this._state));
    }
  }, {
    key: "_set",
    value: function (e, t) {
      var i;
      var n = this;
      if ("string" == typeof e) {
        (i = {})[e] = t;
      } else {
        "object" === o(e) && (i = e);
      }
      c.each(i, function (e, t) {
        n._state[t] = e;
      });
      this._save();
    }
  }, {
    key: "_get",
    value: function (e) {
      return this._state[e];
    }
  }, {
    key: "setEventTimer",
    value: function (e, t) {
      var o = this._state.event_timers || {};
      o[e] = t;
      this._set("event_timers", o);
    }
  }, {
    key: "removeEventTimer",
    value: function (e) {
      var t = (this._state.event_timers || {})[e];
      if (!c.isUndefined(t)) {
        delete this._state.event_timers[e];
        this._save();
      }
      return t;
    }
  }, {
    key: "getDeviceId",
    value: function () {
      return this._state.device_id;
    }
  }, {
    key: "_setDeviceId",
    value: function (e) {
      if (this.getDeviceId()) {
        _.warn("cannot modify the device id.");
      } else {
        this._set("device_id", e);
      }
    }
  }, {
    key: "getDistinctId",
    value: function () {
      return this._state.distinct_id;
    }
  }, {
    key: "setDistinctId",
    value: function (e) {
      this._set("distinct_id", e);
    }
  }, {
    key: "getAccountId",
    value: function () {
      return this._state.account_id;
    }
  }, {
    key: "setAccountId",
    value: function (e) {
      this._set("account_id", e);
    }
  }, {
    key: "getSuperProperties",
    value: function () {
      return this._state.props || {};
    }
  }, {
    key: "setSuperProperties",
    value: function (e, t) {
      t = t ? e : c.extend(this.getSuperProperties(), e);
      this._set("props", t);
    }
  }]);
  return e;
}();
var V = "tab_tampsdk_";
var G = function () {
  function e(t, o) {
    i(this, e);
    this.config = t;
    this.ta = o;
    this.timer = null;
    this.batchConfig = c.extend({
      size: 5,
      interval: 5e3,
      storageLimit: 200
    }, this.config.batchConfig);
    this.batchConfig.size < 1 && (this.batchConfig.size = 1);
    30 < this.batchConfig.size && (this.batchConfig.size = 30);
    this.tabKey = V + this.config.appId;
    this.storageLimit = this.batchConfig.storageLimit;
  }
  r(e, [{
    key: "batchInterval",
    value: function () {
      var e = this;
      e.timer = setTimeout(function () {
        e.recycle();
        e.send();
        clearTimeout(e.timer);
        e.batchInterval();
      }, this.batchConfig.interval);
    }
  }, {
    key: "add",
    value: function (e) {
      var t = "tampsdk_" + this.config.appId + "_" + String(c.UUID());
      var o = I.getStorage(this.tabKey);
      if ((o = c.isArray(o) ? o : []).length <= this.storageLimit) {
        o.push(t);
        I.setStorage(this.tabKey, JSON.stringify(o));
        I.setStorage(t, JSON.stringify(e));
      } else {
        var i = o.splice(0, 20);
        console.log("deleted events data:" + i);
        o.push(t);
        I.setStorage(this.tabKey, JSON.stringify(o));
        I.setStorage(t, JSON.stringify(e));
        o = {};
        var n = [];
        for (var r = 0; r < i.length; r++) {
          var a = I.getStorage(i[r]);
          n.push(a);
        }
        o.data = n;
        o["#app_id"] = this.config.appId;
        this.request(o, i);
      }
    }
  }, {
    key: "flush",
    value: function () {
      clearTimeout(this.timer);
      this.send();
      this.batchInterval();
    }
  }, {
    key: "send",
    value: function () {
      var e = I.getStorage(this.tabKey);
      if (e && e.length) {
        var t = {};
        var o = [];
        var i = [];
        var n = e.length < this.batchConfig.size ? e.length : this.batchConfig.size;
        for (var r = 0; r < n; r++) {
          var a = I.getStorage(e[r]);
          o.push(a);
          i.push(e[r]);
        }
        t.data = o;
        t["#app_id"] = this.config.appId;
        this.request(t, i);
      }
    }
  }, {
    key: "request",
    value: function (e, t) {
      var o = this;
      _.info("flush data: " + JSON.stringify(e));
      B.enqueue(e, this.ta.serverUrl, {
        maxRetries: this.config.maxRetries,
        sendTimeout: this.config.sendTimeout,
        callback: function () {
          o.remove(t);
        },
        debugMode: this.config.debugMode,
        deviceId: this.ta.getDeviceId()
      });
    }
  }, {
    key: "remove",
    value: function (e) {
      var t = I.getStorage(this.tabKey);
      if (t) {
        for (var o = 0; o < e.length; o++) {
          var i = c.indexOf(t, e[o]);
          -1 < i && t.splice(i, 1);
          I.removeStorage(e[o]);
        }
        I.setStorage(this.tabKey, JSON.stringify(t));
      }
    }
  }, {
    key: "recycle",
    value: function () {}
  }]);
  return e;
}();
var j = function () {
  function e(t) {
    i(this, e);
    t.appId = t.appId ? c.checkAppId(t.appId) : c.checkAppId(t.appid);
    t.serverUrl = t.serverUrl ? c.checkUrl(t.serverUrl) : c.checkUrl(t.server_url);
    var o = c.extend({}, L, I.getConfig());
    if (c.isObject(t)) {
      this.config = c.extend(o, t);
    } else {
      this.config = o;
    }
    this._init(this.config);
  }
  r(e, [{
    key: "_init",
    value: function (e) {
      var t = this;
      this.name = e.name;
      this.appId = e.appId || e.appid;
      var o = e.serverUrl || e.server_url;
      this.serverUrl = o + "/sync_xcx";
      this.serverDebugUrl = o + "/data_debug";
      this.configUrl = o + "/config";
      this.autoTrackProperties = {};
      this._queue = [];
      this.updateConfig(this.configUrl, this.appId);
      if (e.isChildInstance) {
        this._state = {};
      } else {
        _.enabled = e.enableLog;
        this.instances = [];
        this._state = {
          getSystemInfo: false,
          initComplete: false
        };
        I.setGlobal(this, this.name);
      }
      this.store = new E(e, function () {
        t.config.asyncPersistence && c.isFunction(t.config.persistenceComplete) && t.config.persistenceComplete(t);
        t._updateState();
      });
      this.enabled = !c.isBoolean(this.store._get("ta_enabled")) || this.store._get("ta_enabled");
      this.isOptOut = !!c.isBoolean(this.store._get("ta_isOptOut")) && this.store._get("ta_isOptOut");
      !e.isChildInstance && e.autoTrack && (this.autoTrack = I.initAutoTrackInstance(this, e));
      if (undefined !== this.config.enableBatch && false !== this.config.enableBatch) {
        this.batchConsumer = new G(this.config, this);
        this.batchConsumer.batchInterval();
      }
    }
  }, {
    key: "initSystemInfo",
    value: function () {
      var e = this;
      this.config.isChildInstance || N.getSystemInfo(function () {
        e._updateState({
          getSystemInfo: true
        });
      });
    }
  }, {
    key: "updateConfig",
    value: function (e, t) {
      var o = this;
      var i = c.createExtraHeaders();
      i["content-type"] = "application/json";
      var n = I.request({
        url: e + "?appid=" + t,
        method: "GET",
        header: i,
        success: function (e) {
          if (!(c.isUndefined(e) || c.isUndefined(e.data) || (_.info("config update success(" + t + ") :" + JSON.stringify(e.data)), c.isUndefined(e.data.data)) || (o.config.syncBatchSize = e.data.data.sync_batch_size, o.config.syncInterval = e.data.data.sync_interval, o.config.disableEventList = e.data.data.disable_event_list, c.isUndefined(e.data.data.secret_key)))) {
            e = e.data.data.secret_key;
            o.config.secretKey = {
              publicKey: e.key,
              version: e.version
            };
          }
        },
        fail: function (e) {
          _.info("config update fail(" + t + ") :" + e.errMsg);
        }
      });
      setTimeout(function () {
        (c.isObject(n) || c.isPromise(n)) && c.isFunction(n.abort) && n.abort();
      }, 3e3);
    }
  }, {
    key: "initInstance",
    value: function (t, o) {
      if (!this.config.isChildInstance) {
        if (c.isString(t) && t !== this.name && c.isUndefined(this[t])) {
          return o = new e(c.extend({}, this.config, {
            enablePersistence: false,
            isChildInstance: true,
            name: t
          }, o)), this[t] = o, this.instances.push(t), this[t]._state = this._state, o;
        } else {
          return void _.warn("initInstance() failed due to the name is invalid: " + t);
        }
      }
      _.warn("initInstance() cannot be called on child instance");
    }
  }, {
    key: "lightInstance",
    value: function (e) {
      return this[e];
    }
  }, {
    key: "_setAutoTrackProperties",
    value: function (e) {
      c.extend(this.autoTrackProperties, e);
    }
  }, {
    key: "init",
    value: function () {
      this.initSystemInfo();
      if (this._state.initComplete) {
        return false;
      }
      this._updateState({
        initComplete: true
      });
      _.info("Thinking Analytics SDK initialized successfully with mode: " + this.config.debugMode + ", APP ID : " + this.config.appId + ", server url: " + this.config.serverUrl + ", libversion: 2.2.1");
    }
  }, {
    key: "_isReady",
    value: function () {
      return this._state.getSystemInfo && this._state.initComplete && this.store.initComplete && this.getDeviceId();
    }
  }, {
    key: "_updateState",
    value: function (e) {
      var t = this;
      c.isObject(e) && c.extend(this._state, e);
      this._onStateChange();
      c.each(this.instances, function (e) {
        t[e]._onStateChange();
      });
    }
  }, {
    key: "_onStateChange",
    value: function () {
      var e = this;
      if (this._isReady() && this._queue && 0 < this._queue.length) {
        c.each(this._queue, function (t) {
          e[t[0]].apply(e, p.call(t[1]));
        });
        this._queue = [];
      }
    }
  }, {
    key: "_hasDisabled",
    value: function () {
      var e = !this.enabled || this.isOptOut;
      e && _.info("ThinkingData is Pause or Stop!");
      return e;
    }
  }, {
    key: "_sendRequest",
    value: function (e, t, o) {
      var i;
      var n;
      if (!this._hasDisabled()) {
        if (!c.isUndefined(this.config.disableEventList) && this.config.disableEventList.includes(e.eventName)) {
          _.info("disabled Event : " + e.eventName);
        } else {
          t = c.isDate(t) ? t : new Date();
          i = {
            data: [{
              "#type": e.type,
              "#time": c.formatDate(t),
              "#distinct_id": this.store.getDistinctId()
            }]
          };
          this.store.getAccountId() && (i.data[0]["#account_id"] = this.store.getAccountId());
          if ("track" === e.type || "track_update" === e.type || "track_overwrite" === e.type) {
            i.data[0]["#event_name"] = e.eventName, "track_update" === e.type || "track_overwrite" === e.type ? i.data[0]["#event_id"] = e.extraId : e.firstCheckId && (i.data[0]["#first_check_id"] = e.firstCheckId), i.data[0].properties = c.extend({
              "#zone_offset": 0 - t.getTimezoneOffset() / 60
            }, N.properties, this.autoTrackProperties, this.store.getSuperProperties(), this.dynamicProperties ? this.dynamicProperties() : {}), t = this.store.removeEventTimer(e.eventName), c.isUndefined(t) || (t = new Date().getTime() - t, 86400 < (t = parseFloat((t / 1e3).toFixed(3))) ? t = 86400 : t < 0 && (t = 0), i.data[0].properties["#duration"] = t);
          } else {
            i.data[0].properties = {};
          }
          c.isObject(e.properties) && !c.isEmptyObject(e.properties) && c.extend(i.data[0].properties, e.properties);
          c.searchObjDate(i.data[0]);
          1 < this.config.maxRetries && (i.data[0]["#uuid"] = c.UUIDv4());
          i["#app_id"] = this.appId;
          _.info(JSON.stringify(i, null, 4));
          t = "debug" === this.config.debugMode || "debugOnly" === this.config.debugMode ? this.serverDebugUrl : this.serverUrl;
          c.isBoolean(this.config.enableEncrypt) && true === this.config.enableEncrypt && (i.data[0] = c.generateEncryptyData(i.data[0], this.config.secretKey));
          if (this.batchConsumer && "none" === this.config.debugMode && !o) {
            this.batchConsumer.add(i.data[0]), c.isFunction(e.onComplete) && e.onComplete({
              code: 0,
              msg: "success"
            });
          } else if (o) {
            o = new FormData(), "debug" === this.config.debugMode || "debugOnly" === this.config.debugMode ? (o.append("source", "client"), o.append("appid", this.appId), o.append("dryRun", "debugOnly" === this.config.debugMode ? 1 : 0), o.append("deviceId", this.getDeviceId()), o.append("data", JSON.stringify(i.data[0]))) : (n = c.base64Encode(JSON.stringify(i)), o.append("data", n)), navigator.sendBeacon(t, o), c.isFunction(e.onComplete) && e.onComplete({
              statusCode: 200
            });
          } else {
            B.enqueue(i, t, {
              maxRetries: this.config.maxRetries,
              sendTimeout: this.config.sendTimeout,
              callback: e.onComplete,
              debugMode: this.config.debugMode,
              deviceId: this.getDeviceId()
            });
          }
        }
      }
    }
  }, {
    key: "_isObjectParams",
    value: function (e) {
      return c.isObject(e) && c.isFunction(e.onComplete);
    }
  }, {
    key: "track",
    value: function (e, t, o, i) {
      var n;
      if (!this._hasDisabled()) {
        if (this._isObjectParams(e)) {
          e = (n = e).eventName, t = n.properties, o = n.time, i = n.onComplete;
        }
        if (T.event(e) && T.properties(t) || !this.config.strict) {
          this._internalTrack(e, t, o, i);
        } else {
          c.isFunction(i) && i({
            code: -1,
            msg: "invalid parameters"
          });
        }
      }
    }
  }, {
    key: "trackUpdate",
    value: function (e) {
      var t;
      var o;
      if (!this._hasDisabled()) {
        if (e && e.eventId && (T.event(e.eventName) && T.properties(e.properties) || !this.config.strict)) {
          if (this._isReady()) {
            t = c.checkCalibration(e.properties, e.time, this.config.enableCalibrationTime);
            o = c.isDate(e.time) ? e.time : new Date();
            this._sendRequest({
              type: "track_update",
              eventName: e.eventName,
              properties: t,
              onComplete: e.onComplete,
              extraId: e.eventId
            }, o);
          } else {
            this._queue.push(["trackUpdate", [e]]);
          }
        } else {
          _.warn("Invalide parameter for trackUpdate: you should pass an object contains eventId to trackUpdate()");
          c.isFunction(e.onComplete) && e.onComplete({
            code: -1,
            msg: "invalid parameters"
          });
        }
      }
    }
  }, {
    key: "trackOverwrite",
    value: function (e) {
      var t;
      var o;
      if (!this._hasDisabled()) {
        if (e && e.eventId && (T.event(e.eventName) && T.properties(e.properties) || !this.config.strict)) {
          if (this._isReady()) {
            t = c.checkCalibration(e.properties, e.time, this.config.enableCalibrationTime);
            o = c.isDate(e.time) ? e.time : new Date();
            this._sendRequest({
              type: "track_overwrite",
              eventName: e.eventName,
              properties: t,
              onComplete: e.onComplete,
              extraId: e.eventId
            }, o);
          } else {
            this._queue.push(["trackOverwrite", [e]]);
          }
        } else {
          _.warn("Invalide parameter for trackOverwrite: you should pass an object contains eventId to trackOverwrite()");
          c.isFunction(e.onComplete) && e.onComplete({
            code: -1,
            msg: "invalid parameters"
          });
        }
      }
    }
  }, {
    key: "trackFirstEvent",
    value: function (e) {
      var t;
      var o;
      if (!this._hasDisabled()) {
        if (e && e.eventName && (T.event(e.eventName) && T.properties(e.properties) || !this.config.strict)) {
          if (this._isReady()) {
            t = c.checkCalibration(e.properties, e.time, this.config.enableCalibrationTime);
            o = c.isDate(e.time) ? e.time : new Date();
            this._sendRequest({
              type: "track",
              eventName: e.eventName,
              properties: t,
              onComplete: e.onComplete,
              firstCheckId: e.firstCheckId || this.getDeviceId()
            }, o);
          } else {
            this._queue.push(["trackFirstEvent", [e]]);
          }
        } else {
          _.warn("Invalide parameter for trackFirstEvent: you should pass an object contains eventName to trackFirstEvent()");
          c.isFunction(e.onComplete) && e.onComplete({
            code: -1,
            msg: "invalid parameters"
          });
        }
      }
    }
  }, {
    key: "_internalTrack",
    value: function (e, t, o, i, n) {
      var r;
      if (!this._hasDisabled()) {
        r = c.checkCalibration(t, o, this.config.enableCalibrationTime);
        o = c.isDate(o) ? o : new Date();
        if (this._isReady()) {
          this._sendRequest({
            type: "track",
            eventName: e,
            properties: r,
            onComplete: i
          }, o, n);
        } else {
          this._queue.push(["_internalTrack", [e, t, o, i]]);
        }
      }
    }
  }, {
    key: "userSet",
    value: function (e, t, o) {
      var i;
      if (!this._hasDisabled()) {
        if (this._isObjectParams(e)) {
          e = (i = e).properties, t = i.time, o = i.onComplete;
        }
        if (T.propertiesMust(e) || !this.config.strict) {
          t = c.isDate(t) ? t : new Date(), this._isReady() ? this._sendRequest({
            type: "user_set",
            properties: e,
            onComplete: o
          }, t) : this._queue.push(["userSet", [e, t, o]]);
        } else {
          _.warn("calling userSet failed due to invalid arguments"), c.isFunction(o) && o({
            code: -1,
            msg: "invalid parameters"
          });
        }
      }
    }
  }, {
    key: "userSetOnce",
    value: function (e, t, o) {
      var i;
      if (!this._hasDisabled()) {
        if (this._isObjectParams(e)) {
          e = (i = e).properties, t = i.time, o = i.onComplete;
        }
        if (T.propertiesMust(e) || !this.config.strict) {
          t = c.isDate(t) ? t : new Date(), this._isReady() ? this._sendRequest({
            type: "user_setOnce",
            properties: e,
            onComplete: o
          }, t) : this._queue.push(["userSetOnce", [e, t, o]]);
        } else {
          _.warn("calling userSetOnce failed due to invalid arguments"), c.isFunction(o) && o({
            code: -1,
            msg: "invalid parameters"
          });
        }
      }
    }
  }, {
    key: "userUnset",
    value: function (e, t, o) {
      var i;
      if (!this._hasDisabled()) {
        if (this._isObjectParams(undefined)) {
          e = (i = undefined).property, t = i.time, o = i.onComplete;
        }
        if (T.propertyName(e) || !this.config.strict) {
          t = c.isDate(t) ? t : new Date(), this._isReady() ? ((i = {})[e] = 0, this._sendRequest({
            type: "user_unset",
            properties: i,
            onComplete: o
          }, t)) : this._queue.push(["userUnset", [e, o, t]]);
        } else {
          _.warn("calling userUnset failed due to invalid arguments"), c.isFunction(o) && o({
            code: -1,
            msg: "invalid parameters"
          });
        }
      }
    }
  }, {
    key: "userDel",
    value: function (e, t) {
      var o;
      if (!this._hasDisabled()) {
        if (this._isObjectParams(e)) {
          e = (o = e).time, t = o.onComplete;
        }
        e = c.isDate(e) ? e : new Date();
        if (this._isReady()) {
          this._sendRequest({
            type: "user_del",
            onComplete: t
          }, e);
        } else {
          this._queue.push(["userDel", [e, t]]);
        }
      }
    }
  }, {
    key: "userAdd",
    value: function (e, t, o) {
      var i;
      if (!this._hasDisabled()) {
        if (this._isObjectParams(e)) {
          e = (i = e).properties, t = i.time, o = i.onComplete;
        }
        if (T.userAddProperties(e) || !this.config.strict) {
          t = c.isDate(t) ? t : new Date(), this._isReady() ? this._sendRequest({
            type: "user_add",
            properties: e,
            onComplete: o
          }, t) : this._queue.push(["userAdd", [e, t, o]]);
        } else {
          _.warn("calling userAdd failed due to invalid arguments"), c.isFunction(o) && o({
            code: -1,
            msg: "invalid parameters"
          });
        }
      }
    }
  }, {
    key: "userAppend",
    value: function (e, t, o) {
      var i;
      if (!this._hasDisabled()) {
        if (this._isObjectParams(e)) {
          e = (i = e).properties, t = i.time, o = i.onComplete;
        }
        if (T.userAppendProperties(e) || !this.config.strict) {
          t = c.isDate(t) ? t : new Date(), this._isReady() ? this._sendRequest({
            type: "user_append",
            properties: e,
            onComplete: o
          }, t) : this._queue.push(["userAppend", [e, t, o]]);
        } else {
          _.warn("calling userAppend failed due to invalid arguments"), c.isFunction(o) && o({
            code: -1,
            msg: "invalid parameters"
          });
        }
      }
    }
  }, {
    key: "userUniqAppend",
    value: function (e, t, o) {
      var i;
      if (!this._hasDisabled()) {
        if (this._isObjectParams(e)) {
          e = (i = e).properties, t = i.time, o = i.onComplete;
        }
        if (T.userAppendProperties(e) || !this.config.strict) {
          t = c.isDate(t) ? t : new Date(), this._isReady() ? this._sendRequest({
            type: "user_uniq_append",
            properties: e,
            onComplete: o
          }, t) : this._queue.push(["userUniqAppend", [e, t, o]]);
        } else {
          _.warn("calling userAppend failed due to invalid arguments"), c.isFunction(o) && o({
            code: -1,
            msg: "invalid parameters"
          });
        }
      }
    }
  }, {
    key: "flush",
    value: function () {
      this.batchConsumer && "none" === this.config.debugMode && this.batchConsumer.flush();
    }
  }, {
    key: "authorizeOpenID",
    value: function (e) {
      this.identify(e);
    }
  }, {
    key: "identify",
    value: function (e) {
      if (!this._hasDisabled()) {
        if ("number" == typeof e) {
          e = String(e);
        } else if ("string" != typeof e) {
          return false;
        }
        this.store.setDistinctId(e);
      }
    }
  }, {
    key: "getDistinctId",
    value: function () {
      return this.store.getDistinctId();
    }
  }, {
    key: "login",
    value: function (e) {
      if (!this._hasDisabled()) {
        if ("number" == typeof e) {
          e = String(e);
        } else if ("string" != typeof e) {
          return false;
        }
        this.store.setAccountId(e);
      }
    }
  }, {
    key: "getAccountId",
    value: function () {
      return this.store.getAccountId();
    }
  }, {
    key: "logout",
    value: function () {
      this._hasDisabled() || this.store.setAccountId(null);
    }
  }, {
    key: "setSuperProperties",
    value: function (e) {
      if (!this._hasDisabled()) {
        if (T.propertiesMust(e) || !this.config.strict) {
          this.store.setSuperProperties(e);
        } else {
          _.warn("setSuperProperties parameter must be a valid property value");
        }
      }
    }
  }, {
    key: "clearSuperProperties",
    value: function () {
      this._hasDisabled() || this.store.setSuperProperties({}, true);
    }
  }, {
    key: "unsetSuperProperty",
    value: function (e) {
      var t;
      this._hasDisabled() || c.isString(e) && (delete (t = this.getSuperProperties())[e], this.store.setSuperProperties(t, true));
    }
  }, {
    key: "getSuperProperties",
    value: function () {
      return this.store.getSuperProperties();
    }
  }, {
    key: "getPresetProperties",
    value: function () {
      var e = N.properties;
      var t = {};
      var o = e["#os"];
      t.os = c.isUndefined(o) ? "" : o;
      o = e["#screen_width"];
      t.screenWidth = c.isUndefined(o) ? 0 : o;
      o = e["#screen_height"];
      t.screenHeight = c.isUndefined(o) ? 0 : o;
      o = e["#network_type"];
      t.networkType = c.isUndefined(o) ? "" : o;
      o = e["#device_model"];
      t.deviceModel = c.isUndefined(o) ? "" : o;
      o = e["#os_version"];
      t.osVersion = c.isUndefined(o) ? "" : o;
      t.deviceId = this.getDeviceId();
      var i = 0 - new Date().getTimezoneOffset() / 60;
      t.zoneOffset = i;
      o = e["#manufacturer"];
      t.manufacturer = c.isUndefined(o) ? "" : o;
      t.toEventPresetProperties = function () {
        return {
          "#device_model": t.deviceModel,
          "#device_id": t.deviceId,
          "#screen_width": t.screenWidth,
          "#screen_height": t.screenHeight,
          "#os": t.os,
          "#os_version": t.osVersion,
          "#network_type": t.networkType,
          "#zone_offset": i,
          "#manufacturer": t.manufacturer
        };
      };
      return t;
    }
  }, {
    key: "setDynamicSuperProperties",
    value: function (e) {
      if (!this._hasDisabled()) {
        if ("function" == typeof e) {
          if (T.properties(e()) || !this.config.strict) {
            this.dynamicProperties = e;
          } else {
            _.warn("A dynamic public property must return a valid property value");
          }
        } else {
          _.warn("setDynamicSuperProperties parameter must be a function type");
        }
      }
    }
  }, {
    key: "timeEvent",
    value: function (e, t) {
      if (!this._hasDisabled()) {
        t = c.isDate(t) ? t : new Date();
        if (this._isReady()) {
          if (T.event(e) || !this.config.strict) {
            this.store.setEventTimer(e, t.getTime());
          } else {
            _.warn("calling timeEvent failed due to invalid eventName: " + e);
          }
        } else {
          this._queue.push(["timeEvent", [e, t]]);
        }
      }
    }
  }, {
    key: "getDeviceId",
    value: function () {
      return N.properties["#device_id"];
    }
  }, {
    key: "enableTracking",
    value: function (e) {
      this.enabled = e;
      this.store._set("ta_enabled", e);
    }
  }, {
    key: "optOutTracking",
    value: function () {
      this.store.setSuperProperties({}, true);
      this.store.setDistinctId(c.UUID());
      this.store.setAccountId(null);
      this._queue.splice(0, this._queue.length);
      this.isOptOut = true;
      this.store._set("ta_isOptOut", true);
    }
  }, {
    key: "optOutTrackingAndDeleteUser",
    value: function () {
      var e = new Date();
      this._sendRequest({
        type: "user_del"
      }, e);
      this.optOutTracking();
    }
  }, {
    key: "optInTracking",
    value: function () {
      this.isOptOut = false;
      this.store._set("ta_isOptOut", false);
    }
  }, {
    key: "setTrackStatus",
    value: function (e) {
      switch (e) {
        case "PAUSE":
          this.eventSaveOnly = false;
          this.optInTracking();
          this.enableTracking(false);
          break;
        case "STOP":
          this.eventSaveOnly = false;
          this.optOutTracking(true);
          break;
        case "SAVE_ONLY":
          break;
        default:
          this.eventSaveOnly = false;
          this.optInTracking();
          this.enableTracking(true);
      }
      _.info("switch track status:" + e);
    }
  }]);
  return e;
}();
var x = {
  name: "thinkingdata",
  enableLog: true,
  enableNative: false
};
var F = function () {
  function e(t) {
    i(this, e);
    t.appId = t.appId ? c.checkAppId(t.appId) : c.checkAppId(t.appid);
    t.serverUrl = t.serverUrl ? c.checkUrl(t.serverUrl) : c.checkUrl(t.server_url);
    var o = c.extend({}, x, I.getConfig());
    if (c.isObject(t)) {
      this.config = c.extend(o, t);
    } else {
      this.config = o;
    }
    this._init(this.config);
  }
  r(e, [{
    key: "_isNativePlatform",
    value: function () {
      return !(!this._isIOS() && !this._isAndroid() || !this.config.enableNative);
    }
  }, {
    key: "_isIOS",
    value: function () {
      return !(!cc.sys.isNative || "iOS" !== cc.sys.os);
    }
  }, {
    key: "_isAndroid",
    value: function () {
      return !(!cc.sys.isNative || "Android" !== cc.sys.os);
    }
  }, {
    key: "_init",
    value: function (e) {
      this.name = e.name;
      this.appId = e.appId || e.appid;
      if (this._isNativePlatform()) {
        this.initInstanceForNative(this.name, e, this.appId);
        this._readStorage(e);
      } else {
        this.taJs = new ThinkingAnalyticsAPIForJS(e);
      }
    }
  }, {
    key: "_readStorage",
    value: function (e) {
      var t = this;
      var o = e.persistenceName;
      var i = e.persistenceNameOld;
      if (e.isChildInstance) {
        o = e.persistenceName + "_" + e.name;
        i = e.persistenceNameOld + "_" + e.name;
      }
      this._state = I.getStorage(o) || {};
      c.isEmptyObject(this._state) && (this._state = I.getStorage(i) || {});
      if (c.isEmptyObject(this._state)) {
        I.getStorage(o, true, function (e) {
          if (c.isEmptyObject(e)) {
            I.getStorage(i, true, function (e) {
              t._state = c.extend2Layers({}, e, t._state);
            });
          } else {
            t._state = c.extend2Layers({}, e, t._state);
          }
          t._state.distinct_id && t.identifyForNative(t._state.distinct_id);
          t._state.account_id && t.loginForNative(t._state.account_id);
        });
      } else {
        this._state.distinct_id && this.identifyForNative(this._state.distinct_id);
        this._state.account_id && this.loginForNative(this._state.account_id);
      }
    }
  }, {
    key: "initInstance",
    value: function (e, t) {
      if (this._isNativePlatform()) {
        if (c.isUndefined(t)) {
          this[e] = new ThinkingAnalyticsAPI(this.config);
        } else {
          this[e] = new ThinkingAnalyticsAPI(t);
        }
      } else {
        this[e] = this.taJs.initInstance(e, t);
      }
      return this[e];
    }
  }, {
    key: "lightInstance",
    value: function (e) {
      return this[e];
    }
  }, {
    key: "init",
    value: function () {
      var 全局变量_e;
      var t;
      if (this._isNativePlatform()) {
        全局变量_e = window;
        t = this;
        全局变量_e.__autoTrackCallback = function (e) {
          if (c.isFunction(t.config.autoTrack.callback)) {
            return e = t.config.autoTrack.callback(e), JSON.stringify(e);
          } else {
            return "{}";
          }
        };
        this.startThinkingAnalyticsForNative();
      } else {
        this.taJs.init();
      }
    }
  }, {
    key: "track",
    value: function (e, t, o, i) {
      if (this._isNativePlatform()) {
        this.trackForNative(e, t, o, this.appId);
      } else {
        this.taJs.track(e, t, o, i);
      }
    }
  }, {
    key: "trackUpdate",
    value: function (e) {
      if (this._isNativePlatform()) {
        this.trackUpdateForNative(e, this.appId);
      } else {
        this.taJs.trackUpdate(e);
      }
    }
  }, {
    key: "trackOverwrite",
    value: function (e) {
      if (this._isNativePlatform()) {
        this.trackOverwriteForNative(e, this.appId);
      } else {
        this.taJs.trackOverwrite(e);
      }
    }
  }, {
    key: "trackFirstEvent",
    value: function (e) {
      if (this._isNativePlatform()) {
        this.trackFirstEventForNative(e, this.appId);
      } else {
        this.taJs.trackFirstEvent(e);
      }
    }
  }, {
    key: "userSet",
    value: function (e, t, o) {
      if (this._isNativePlatform()) {
        this.userSetForNative(e, this.appId);
      } else {
        this.taJs.userSet(e, t, o);
      }
    }
  }, {
    key: "userSetOnce",
    value: function (e, t, o) {
      if (this._isNativePlatform()) {
        this.userSetOnceForNative(e, this.appId);
      } else {
        this.taJs.userSetOnce(e, t, o);
      }
    }
  }, {
    key: "userUnset",
    value: function (e, t, o) {
      if (this._isNativePlatform()) {
        this.userUnsetForNative(e, this.appId);
      } else {
        this.taJs.userUnset(e, t, o);
      }
    }
  }, {
    key: "userDel",
    value: function (e, t) {
      if (this._isNativePlatform()) {
        this.userDelForNative(this.appId);
      } else {
        this.taJs.userDel(e, t);
      }
    }
  }, {
    key: "userAdd",
    value: function (e, t, o) {
      if (this._isNativePlatform()) {
        this.userAddForNative(e, this.appId);
      } else {
        this.taJs.userAdd(e, t, o);
      }
    }
  }, {
    key: "userAppend",
    value: function (e, t, o) {
      if (this._isNativePlatform()) {
        this.userAppendForNative(e, this.appId);
      } else {
        this.taJs.userAppend(e, t, o);
      }
    }
  }, {
    key: "userUniqAppend",
    value: function (e, t, o) {
      if (this._isNativePlatform()) {
        this.userUniqAppendForNative(e, this.appId);
      } else {
        this.taJs.userUniqAppend(e, t, o);
      }
    }
  }, {
    key: "flush",
    value: function () {
      if (this._isNativePlatform()) {
        this.flushForNative(this.appId);
      } else {
        this.taJs.flush();
      }
    }
  }, {
    key: "authorizeOpenID",
    value: function (e) {
      this.identify(e);
    }
  }, {
    key: "identify",
    value: function (e) {
      if (this._isNativePlatform()) {
        this.identifyForNative(e, this.appId);
      } else {
        this.taJs.identify(e);
      }
    }
  }, {
    key: "getDistinctId",
    value: function () {
      if (this._isNativePlatform()) {
        return this.getDistinctIdForNative(this.appId);
      } else {
        return this.taJs.getDistinctId();
      }
    }
  }, {
    key: "login",
    value: function (e) {
      if (this._isNativePlatform()) {
        this.loginForNative(e, this.appId);
      } else {
        this.taJs.login(e);
      }
    }
  }, {
    key: "getAccountId",
    value: function () {
      if (this._isNativePlatform()) {
        return this.getAccountIdForNative(this.appId);
      } else {
        return this.taJs.getAccountId();
      }
    }
  }, {
    key: "logout",
    value: function () {
      if (this._isNativePlatform()) {
        this.logoutForNative(this.appId);
      } else {
        this.taJs.logout();
      }
    }
  }, {
    key: "setSuperProperties",
    value: function (e) {
      if (this._isNativePlatform()) {
        this.setSuperPropertiesForNative(e, this.appId);
      } else {
        this.taJs.setSuperProperties(e);
      }
    }
  }, {
    key: "clearSuperProperties",
    value: function () {
      if (this._isNativePlatform()) {
        this.clearSuperPropertiesForNative(this.appId);
      } else {
        this.taJs.clearSuperProperties();
      }
    }
  }, {
    key: "unsetSuperProperty",
    value: function (e) {
      if (this._isNativePlatform()) {
        this.unsetSuperPropertyForNative(e, this.appId);
      } else {
        this.taJs.unsetSuperProperty(e);
      }
    }
  }, {
    key: "getSuperProperties",
    value: function () {
      if (this._isNativePlatform()) {
        return this.getSuperPropertiesForNative(this.appId);
      } else {
        return this.taJs.getSuperProperties();
      }
    }
  }, {
    key: "getPresetProperties",
    value: function () {
      var e;
      var t;
      var o;
      var i;
      if (this._isNativePlatform()) {
        return e = this.getPresetPropertiesForNative(this.appId), t = {}, i = e["#os"], t.os = c.isUndefined(i) ? "" : i, i = e["#screen_width"], t.screenWidth = c.isUndefined(i) ? 0 : i, i = e["#screen_height"], t.screenHeight = c.isUndefined(i) ? 0 : i, i = e["#network_type"], t.networkType = c.isUndefined(i) ? "" : i, i = e["#device_model"], t.deviceModel = c.isUndefined(i) ? "" : i, i = e["#os_version"], t.osVersion = c.isUndefined(i) ? "" : i, t.deviceId = this.getDeviceId(), o = 0 - new Date().getTimezoneOffset() / 60, t.zoneOffset = o, i = e["#manufacturer"], t.manufacturer = c.isUndefined(i) ? "" : i, t.toEventPresetProperties = function () {
          return {
            "#device_model": t.deviceModel,
            "#device_id": t.deviceId,
            "#screen_width": t.screenWidth,
            "#screen_height": t.screenHeight,
            "#os": t.os,
            "#os_version": t.osVersion,
            "#network_type": t.networkType,
            "#zone_offset": o,
            "#manufacturer": t.manufacturer
          };
        }, t;
      } else {
        return this.taJs.getPresetProperties();
      }
    }
  }, {
    key: "setDynamicSuperProperties",
    value: function (e) {
      if (this._isNativePlatform()) {
        if ("function" == typeof e) {
          this.dynamicProperties = e;
          window.__dynamicPropertiesForNative = function (t) {
            console.log("__dynamicPropertiesForNative: native msg: ", t);
            t = e();
            t = c.encodeDates(t);
            return JSON.stringify(t);
          };
          this.setDynamicSuperPropertiesForNative("__dynamicPropertiesForNative");
        } else {
          logger.warn("setDynamicSuperProperties parameter must be a function type");
        }
      } else {
        this.taJs.setDynamicSuperProperties(e);
      }
    }
  }, {
    key: "timeEvent",
    value: function (e, t) {
      if (this._isNativePlatform()) {
        return this.timeEventForNative(e, this.appId);
      } else {
        return this.taJs.timeEvent(e, t);
      }
    }
  }, {
    key: "getDeviceId",
    value: function () {
      if (this._isNativePlatform()) {
        return this.getDeviceIdForNative(this.appId);
      } else {
        return this.taJs.getDeviceId();
      }
    }
  }, {
    key: "enableTracking",
    value: function (e) {
      if (this._isNativePlatform()) {
        this.enableTrackingForNative(e, this.appId);
      } else {
        this.taJs.enableTracking(e);
      }
    }
  }, {
    key: "optOutTracking",
    value: function () {
      if (this._isNativePlatform()) {
        this.optOutTrackingForNative(this.appId);
      } else {
        this.taJs.optOutTracking();
      }
    }
  }, {
    key: "optOutTrackingAndDeleteUser",
    value: function () {
      if (this._isNativePlatform()) {
        this.optOutTrackingAndDeleteUserForNative(this.appId);
      } else {
        this.taJs.optOutTrackingAndDeleteUser();
      }
    }
  }, {
    key: "optInTracking",
    value: function () {
      if (this._isNativePlatform()) {
        this.optInTrackingForNative(this.appId);
      } else {
        this.taJs.optInTracking();
      }
    }
  }, {
    key: "setTrackStatus",
    value: function (e) {
      if (this._isNativePlatform()) {
        this.setTrackStatusForNative(e, this.appId);
      } else {
        this.taJs.setTrackStatus(e);
      }
    }
  }, {
    key: "trackForNative",
    value: function (e, t, o, i) {
      o = c.isDate(o) ? c.formatDate(o) : "";
      c.isUndefined(t) && (t = {});
      t = c.extend(t, this.dynamicProperties ? this.dynamicProperties() : {});
      t = c.encodeDates(t);
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "track", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", e, JSON.stringify(t), o, i);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "track:properties:time:appId:", e, JSON.stringify(t), o, i);
      }
    }
  }, {
    key: "trackUpdateForNative",
    value: function (e, t) {
      e.properties = c.extend(c.isUndefined(e.properties) ? {} : e.properties, this.dynamicProperties ? this.dynamicProperties() : {});
      e = c.encodeDates(e);
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "trackUpdate", "(Ljava/lang/String;Ljava/lang/String;)V", JSON.stringify(e), t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "trackUpdate:appId:", JSON.stringify(e), t);
      }
    }
  }, {
    key: "trackFirstEventForNative",
    value: function (e, t) {
      e.properties = c.extend(c.isUndefined(e.properties) ? {} : e.properties, this.dynamicProperties ? this.dynamicProperties() : {});
      e = c.encodeDates(e);
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "trackFirstEvent", "(Ljava/lang/String;Ljava/lang/String;)V", JSON.stringify(e), t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "trackFirstEvent:appId:", JSON.stringify(e), t);
      }
    }
  }, {
    key: "trackOverwriteForNative",
    value: function (e, t) {
      e.properties = c.extend(c.isUndefined(e.properties) ? {} : e.properties, this.dynamicProperties ? this.dynamicProperties() : {});
      e = c.encodeDates(e);
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "trackOverwrite", "(Ljava/lang/String;Ljava/lang/String;)V", JSON.stringify(e), t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "trackOverwrite:appId:", JSON.stringify(e), t);
      }
    }
  }, {
    key: "timeEventForNative",
    value: function (e, t) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "timeEvent", "(Ljava/lang/String;Ljava/lang/String;)V", e, t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "timeEvent:appId:", e, t);
      }
    }
  }, {
    key: "loginForNative",
    value: function (e, t) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "login", "(Ljava/lang/String;Ljava/lang/String;)V", e, t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "login:appId:", e, t);
      }
    }
  }, {
    key: "logoutForNative",
    value: function (e) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "logout", "(Ljava/lang/String;)V", e);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "logout:", e);
      }
    }
  }, {
    key: "setSuperPropertiesForNative",
    value: function (e, t) {
      e = c.encodeDates(e);
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "setSuperProperties", "(Ljava/lang/String;Ljava/lang/String;)V", JSON.stringify(e), t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "setSuperProperties:appId:", JSON.stringify(e), t);
      }
    }
  }, {
    key: "getSuperPropertiesForNative",
    value: function (e) {
      var t = "{}";
      if (this._isAndroid()) {
        t = jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "getSuperProperties", "(Ljava/lang/String;)Ljava/lang/String;", e);
      } else {
        this._isIOS() && (t = jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "getSuperProperties:", e));
      }
      return JSON.parse(t);
    }
  }, {
    key: "unsetSuperPropertyForNative",
    value: function (e, t) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "unsetSuperProperty", "(Ljava/lang/String;Ljava/lang/String;)V", e, t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "unsetSuperProperty:appId:", e, t);
      }
    }
  }, {
    key: "clearSuperPropertiesForNative",
    value: function (e) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "clearSuperProperties", "(Ljava/lang/String;)V", e);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "clearSuperProperties:", e);
      }
    }
  }, {
    key: "userSetForNative",
    value: function (e, t) {
      e = c.encodeDates(e);
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "userSet", "(Ljava/lang/String;Ljava/lang/String;)V", JSON.stringify(e), t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "userSet:appId:", JSON.stringify(e), t);
      }
    }
  }, {
    key: "userSetOnceForNative",
    value: function (e, t) {
      e = c.encodeDates(e);
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "userSetOnce", "(Ljava/lang/String;Ljava/lang/String;)V", JSON.stringify(e), t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "userSetOnce:appId:", JSON.stringify(e), t);
      }
    }
  }, {
    key: "userAppendForNative",
    value: function (e, t) {
      e = c.encodeDates(e);
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "userAppend", "(Ljava/lang/String;Ljava/lang/String;)V", JSON.stringify(e), t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "userAppend:appId:", JSON.stringify(e), t);
      }
    }
  }, {
    key: "userUniqAppendForNative",
    value: function (e, t) {
      e = c.encodeDates(e);
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "userUniqAppend", "(Ljava/lang/String;Ljava/lang/String;)V", JSON.stringify(e), t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "userUniqAppend:appId:", JSON.stringify(e), t);
      }
    }
  }, {
    key: "userAddForNative",
    value: function (e, t) {
      e = c.encodeDates(e);
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "userAdd", "(Ljava/lang/String;Ljava/lang/String;)V", JSON.stringify(e), t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "userAdd:appId:", JSON.stringify(e), t);
      }
    }
  }, {
    key: "userUnsetForNative",
    value: function (e, t) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "userUnset", "(Ljava/lang/String;Ljava/lang/String;)V", e, t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "userUnset:appId:", e, t);
      }
    }
  }, {
    key: "userDelForNative",
    value: function (e) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "userDel", "(Ljava/lang/String;)V", e);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "userDel:", e);
      }
    }
  }, {
    key: "flushForNative",
    value: function (e) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "flush", "(Ljava/lang/String;)V", e);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "flush:", e);
      }
    }
  }, {
    key: "authorizeOpenIDForNative",
    value: function (e, t) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "authorizeOpenID", "(Ljava/lang/String;Ljava/lang/String;)V", e, t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "authorizeOpenID:appId:", e, t);
      }
    }
  }, {
    key: "identifyForNative",
    value: function (e, t) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "identify", "(Ljava/lang/String;Ljava/lang/String;)V", e, t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "identify:appId:", e, t);
      }
    }
  }, {
    key: "initInstanceForNative",
    value: function (e, t, o) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "setCustomerLibInfo", "(Ljava/lang/String;Ljava/lang/String;)V", "MG", "2.2.1");
        if (c.isUndefined(t)) {
          jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "initInstanceAppId", "(Ljava/lang/String;Ljava/lang/String;)V", e, o);
        } else {
          jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "initInstanceConfig", "(Ljava/lang/String;Ljava/lang/String;)V", e, JSON.stringify(t));
        }
      } else if (this._isIOS()) {
        jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "setCustomerLibInfoWithLibName:libVersion:", "MG", "2.2.1"), c.isUndefined(t) ? jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "initInstance:appId:", e, o) : jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "initInstance:config:", e, JSON.stringify(t));
      }
    }
  }, {
    key: "lightInstanceForNative",
    value: function (e, t) {
      if (this._isAndroid()) {
        return jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "lightInstance", "(Ljava/lang/String;Ljava/lang/String;)V", e, t);
      } else {
        if (this._isIOS()) {
          return jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "lightInstance:appId:", e, t);
        } else {
          return undefined;
        }
      }
    }
  }, {
    key: "startThinkingAnalyticsForNative",
    value: function (e) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "startThinkingAnalytics", "(Ljava/lang/String;)V", e);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "startThinkingAnalytics:", e);
      }
    }
  }, {
    key: "setDynamicSuperPropertiesForNative",
    value: function (e, t) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "setDynamicSuperProperties", "(Ljava/lang/String;Ljava/lang/String;)V", e, t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "setDynamicSuperProperties:appId:", e, t);
      }
    }
  }, {
    key: "getDeviceIdForNative",
    value: function (e) {
      if (this._isAndroid()) {
        return jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "getDeviceId", "(Ljava/lang/String;)Ljava/lang/String;", e);
      } else {
        if (this._isIOS()) {
          return jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "getDeviceId:", e);
        } else {
          return undefined;
        }
      }
    }
  }, {
    key: "getDistinctIdForNative",
    value: function (e) {
      if (this._isAndroid()) {
        return jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "getDistinctId", "(Ljava/lang/String;)Ljava/lang/String;", e);
      } else {
        if (this._isIOS()) {
          return jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "getDistinctId:", e);
        } else {
          return undefined;
        }
      }
    }
  }, {
    key: "getAccountIdForNative",
    value: function (e) {
      if (this._isAndroid()) {
        return jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "getAccountId", "(Ljava/lang/String;)Ljava/lang/String;", e);
      } else {
        if (this._isIOS()) {
          return jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "getAccountId:", e);
        } else {
          return undefined;
        }
      }
    }
  }, {
    key: "getPresetPropertiesForNative",
    value: function (e) {
      var t = "{}";
      if (this._isAndroid()) {
        t = jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "getPresetProperties", "(Ljava/lang/String;)Ljava/lang/String;", e);
      } else {
        this._isIOS() && (t = jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "getPresetProperties:", e));
      }
      return JSON.parse(t);
    }
  }, {
    key: "enableTrackingForNative",
    value: function (e, t) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "enableTracking", "(Ljava/lang/String;Ljava/lang/String;)V", e.toString(), t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "enableTracking:appId:", e.toString(), t);
      }
    }
  }, {
    key: "optOutTrackingForNative",
    value: function (e) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "optOutTracking", "(Ljava/lang/String;)V", e);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "optOutTracking:", e);
      }
    }
  }, {
    key: "optOutTrackingAndDeleteUserForNative",
    value: function (e) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "optOutTrackingAndDeleteUser", "(Ljava/lang/String;)V", e);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "optOutTrackingAndDeleteUser:", e);
      }
    }
  }, {
    key: "optInTrackingForNative",
    value: function (e) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "optInTracking", "(Ljava/lang/String;)V", e);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "optInTracking:", e);
      }
    }
  }, {
    key: "setTrackStatusForNative",
    value: function (e, t) {
      if (this._isAndroid()) {
        jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi", "setTrackStatus", "(Ljava/lang/String;Ljava/lang/String;)V", e, t);
      } else {
        this._isIOS() && jsb.reflection.callStaticMethod("CocosCreatorProxyApi", "setTrackStatus:appId:", e, t);
      }
    }
  }]);
  return e;
}();
window.ThinkingAnalyticsAPI = F;
window.ThinkingAnalyticsAPIForJS = j;
module.exports = F;