Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SdkAudioAdapter = undefined;
var i = function () {
  function e() {}
  e.prototype.setAdapter = function (e) {
    this._adapter = e;
  };
  e.prototype.playMusic = function (e) {
    this._adapter.playMusic(e);
  };
  e.prototype.stopMusic = function () {
    this._adapter.stopMusic();
  };
  e.prototype.pauseMusic = function () {
    this._adapter.pauseMusic();
  };
  e.prototype.resumeMusic = function () {
    this._adapter.resumeMusic();
  };
  e.prototype.setMusicEnable = function (e) {
    this._adapter.setMusicEnable(e);
  };
  return e;
}();
exports.SdkAudioAdapter = new i();