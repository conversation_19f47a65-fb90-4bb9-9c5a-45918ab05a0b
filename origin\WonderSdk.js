Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.WonderSdk = undefined;
var $2CallID = require("CallID");
var $2StorageID = require("StorageID");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var $2AudioAdapter = require("AudioAdapter");
var $2SelectAlertAdapter = require("SelectAlertAdapter");
var $2SdkConfig = require("SdkConfig");
var $2BaseNet = require("BaseNet");
var $2BaseSdk = require("BaseSdk");
var exp_WonderSdk = function () {
  function _ctor(o) {
    this._isShiledIp = 0;
    this._bannerTime = 0;
    this.VideoAdCode = $2BaseSdk.VideoAdCode;
    this.ShareType = $2BaseSdk.ShareType;
    this.isTest = false;
    this._platformId = o;
    var i = $2SdkConfig.SdkClass[this._platformId];
    window.wonderSdk = this;
    if (i) {
      _ctor.isInit = true;
      var n = require(i);
      if (n[i]) {
        this._sdk = new n[i]();
      } else {
        this._sdk = new n.default();
      }
      var r = $2SdkConfig.AppIdList[this._platformId];
      this._sdk.init(r);
    } else {
      console.error("Can't find the SdkClass = " + o + ". Sdk init fail please confirm!");
      _ctor.isInit = false;
    }
  }
  Object.defineProperty(_ctor.prototype, "sdk", {
    get: function () {
      return this._sdk;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "platformId", {
    get: function () {
      return this._platformId;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isLive", {
    get: function () {
      return this.isWebDev && "1.0.0" == this.BMS_VERSION;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.init = function (e, o) {
    undefined === o && (o = false);
    _ctor.isInit && console.warn("Sdk is initialized. do not init again");
    _ctor._instance = new _ctor(e);
    _ctor._instance.isTest = o;
    return _ctor.isInit;
  };
  Object.defineProperty(_ctor.prototype, "sdkVersion", {
    get: function () {
      return _ctor._version;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isNative", {
    get: function () {
      return !!$2SdkConfig.NativePlatform[this._platformId];
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isHWNative", {
    get: function () {
      return !!$2SdkConfig.HWNative[this._platformId];
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isFXRAndroid", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.FXR_ANDROID;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isHRRAndroid", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.HHR_ANDROID;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isByteDance", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.BYTE_DANCE;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isWebDev", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.WEB_DEV;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "BmsName", {
    get: function () {
      return this.BMS_APP_NAME;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "BmsVersion", {
    get: function () {
      return this.BMS_VERSION;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isKwai", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.KWAI_MICRO;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isAlipayMicro", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.ALIPAY_MICRO;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isBLMicro", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.BL_MICRO;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isQQ", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.QQ;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isBaiDuGame", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.BAIDU;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isWeChat", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.WECHAT_GAME;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isHW_IOS", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.NA_IOS;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isCN_IOS", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.CN_IOS;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isIOS", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.CN_IOS || this._platformId == $2SdkConfig.EPlatform.NA_IOS;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isQttGame", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.QTT_GAME;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isJkwGame", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.JKW_GAME;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isOppoMiniGame", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.OPPP_MICRO;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isOppoAndroid", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.OPPO_ANDROID;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isAndroid233", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.ANDROID_233;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isVivoMiniGame", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.VIVO_MICRO;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isVivoAndroid", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.VIVO_ANDROID;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isXiaoMiAndroid", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.XIAOMI_ANDROID;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isGoogleAndroid", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.GOOGLE_ANDROID;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isAmzAndroid", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.AMZ_ANDROID;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isMeiZuMicro", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.MEIZU_MICRO;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isUcMicro", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.UC_MICRO;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isHuaWeiMicro", {
    get: function () {
      return this._platformId == $2SdkConfig.EPlatform.HUAWEI_MICRO;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isShieldPlatform", {
    get: function () {
      return !!$2SdkConfig.ShieldPlatform[this._platformId];
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "hasPay", {
    get: function () {
      return !!$2SdkConfig.PayPlatform[this._platformId];
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setAudioAdapter = function (e) {
    $2AudioAdapter.SdkAudioAdapter.setAdapter(e);
  };
  _ctor.prototype.setAlertAdpater = function (e) {
    $2SelectAlertAdapter.SdkAlertAdapter.setAdapter(e);
  };
  _ctor.prototype.login = function (e, t) {
    return this._sdk.login(e, t);
  };
  _ctor.prototype.logout = function () {
    this._sdk.logout();
  };
  _ctor.prototype.showBanner = function (e, t, o) {
    var i = $2SdkConfig.BannerIdList[this._platformId];
    if (i) {
      if (!this.isTest) {
        var n = new Date().getTime();
        if (this._bannerTime) {
          if (n - this._bannerTime > 15e3) {
            this.destroyBanner(), this._bannerTime = n;
          }
        } else {
          this._bannerTime = n;
        }
        var r = i[e] || i[0];
        if (!t && o) {
          this._sdk.showBanner(r, o);
        } else if ("function" == typeof t) {
          this._sdk.showBanner(r, t);
        } else if (t && "number" == typeof t.x) {
          this._sdk.showBannerWithNode(r, t, o);
        } else {
          this._sdk.showBannerWithStyle(r, t, o);
        }
      }
    } else {
      console.error("can't find this platform banner");
    }
  };
  _ctor.prototype.hideBanner = function () {
    this._sdk.hideBanner();
  };
  _ctor.prototype.destroyBanner = function () {
    this._sdk.destroyBanner();
  };
  _ctor.prototype.preLoadRewardVideo = function () {
    this._sdk.preLoadRewardVideo();
  };
  _ctor.prototype.showVideoAD = function (e) {
    var o = $2SdkConfig.VideoIdList[this._platformId];
    if ($2Manager.Manager.vo.switchVo.isNotAd) {
      $2ModeBackpackHeroModel.default.instance.addAdCount();
      return void e($2BaseSdk.VideoAdCode.COMPLETE);
    }
    if (o) {
      var i = o.length;
      var n = o[_ctor.random(0, i)] || o[0];
      this._sdk.showVideoAD(n, function (t, o) {
        t == $2BaseSdk.VideoAdCode.COMPLETE && $2ModeBackpackHeroModel.default.instance.addAdCount();
        e && e(t, o);
      });
    } else {
      e && e($2BaseSdk.VideoAdCode.NOT_SUPPORT, "视频拉取失败，请稍后重试");
    }
  };
  _ctor.prototype.showFullVideoAD = function (e, t, o) {
    var i = $2SdkConfig.FullVideoIdList[this._platformId];
    if (i) {
      if (this.isTest) {
        t && t($2BaseSdk.VideoAdCode.COMPLETE, "");
      } else {
        var n = i;
        this._sdk.showFullVideoAD(n, t, o);
      }
    } else {
      t && t($2BaseSdk.VideoAdCode.NOT_SUPPORT, "没有对应广告id");
    }
  };
  _ctor.prototype.sendEvent = function (e, t) {
    this._sdk.sendEvent(e, t);
  };
  _ctor.prototype.setSuperProperties = function (e) {
    this._sdk.setSuperProperties(e);
  };
  _ctor.prototype.setUserProperties = function (e, t) {
    this._sdk.setUserProperties(e, t);
  };
  _ctor.prototype.showInsertAd = function () {
    var e = $2SdkConfig.InsterIdList[this._platformId];
    e && this._sdk.showInsertAd(e);
  };
  _ctor.prototype.showSplashAd = function () {
    var e = $2SdkConfig.SplashIdList[this._platformId];
    e && this._sdk.showSplashAd(e);
  };
  _ctor.prototype.showFeedAd = function (e) {
    var t = $2SdkConfig.FeedAdIdList[this._platformId];
    t && this._sdk.showFeedAd(t, e);
  };
  _ctor.prototype.hideFeedAd = function () {
    this._sdk.hideFeedAd();
  };
  _ctor.prototype.showPrivacy = function (e) {
    this._sdk.showPrivacy(e);
  };
  _ctor.prototype.share = function (e, t, o, i) {
    this._sdk.share(e, t, o, i);
  };
  Object.defineProperty(_ctor.prototype, "BMS_APP_NAME", {
    get: function () {
      return $2SdkConfig.BMSInfoList[this._platformId] && $2SdkConfig.BMSInfoList[this._platformId].BMS_APP_NAME;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "BMS_VERSION", {
    get: function () {
      return $2SdkConfig.BMSInfoList[this._platformId] && $2SdkConfig.BMSInfoList[this._platformId].BMS_VERSION;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "CHANNEL_NAME", {
    get: function () {
      return $2SdkConfig.BMSInfoList[this._platformId] && $2SdkConfig.BMSInfoList[this._platformId].CHANNEL_NAME;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.requestShiledIp = function () {
    var e = this;
    return new Promise(function (t, o) {
      $2BaseNet.BaseNet.Request($2BaseNet.BaseUrl.ServerDomain + $2BaseNet.Url.BMS_IP_IS_ENABLE, {
        app_name: e.BMS_APP_NAME,
        version: e.BMS_VERSION
      }, "GET").then(function (o) {
        e._isShiledIp = parseInt(o.data.is_enable);
        e._sdk.setIpEnable(e._isShiledIp);
        t(!!e._isShiledIp);
      }).catch(function (e) {
        o(e);
      });
    });
  };
  _ctor.prototype.requestSwitchConfig = function () {
    var e = this;
    return new Promise(function (t, o) {
      $2BaseNet.BaseNet.Request($2BaseNet.BaseUrl.ServerDomain + $2BaseNet.Url.BMS_LAUNCH_CONFIG, {
        app_name: e.BMS_APP_NAME,
        version: e.BMS_VERSION
      }, "GET").then(function (o) {
        e._sdk.setBmsVo(o.data);
        t(o);
      }).catch(function (e) {
        o(e);
      });
    });
  };
  _ctor.prototype.pushDataSave = function (e, t) {
    var o = this;
    return new Promise(function (i, n) {
      if ($2SdkConfig.FarDataSaveList.includes(wonderSdk.platformId) && $2Manager.Manager.vo.openId) {
        return void $2BaseNet.BaseNet.Request($2BaseNet.BaseUrl.ServerDomain + $2BaseNet.Url.DATA_SAVE, {
          app_name: o.BMS_APP_NAME,
          version: o.BMS_VERSION,
          uuid: $2Manager.Manager.vo.openId,
          d_key: e,
          d_data: "string" == typeof t ? t : JSON.stringify(t)
        }, "POST").then(function (e) {
          i(e);
        }).catch(function (e) {
          n(e);
        });
      } else {
        return n(null);
      }
    });
  };
  _ctor.prototype.requestSaveData = function (e) {
    var t = this;
    return new Promise(function (o, i) {
      if ($2SdkConfig.FarDataSaveList.includes(wonderSdk.platformId) && $2Manager.Manager.vo.openId) {
        return void $2BaseNet.BaseNet.Request($2BaseNet.BaseUrl.ServerDomain + $2BaseNet.Url.DATA_MULTIGET, {
          app_name: t.BMS_APP_NAME,
          version: t.BMS_VERSION,
          uuid: $2Manager.Manager.vo.openId,
          d_keys: e
        }, "GET").then(function (e) {
          cc.log("[success]", e.data);
          o(e.data[$2StorageID.StorageID.GameTag]);
        }).catch(function (e) {
          i(e);
        });
      } else {
        return i(null);
      }
    });
  };
  _ctor.prototype.requestLoginCode = function (e) {
    var t = this;
    return new Promise(function (o, i) {
      var n = {
        app_name: t.BMS_APP_NAME,
        version: t.BMS_VERSION,
        code: e
      };
      $2Manager.Manager.vo.userVo.ca_code && (n.ca_code = $2Manager.Manager.vo.userVo.ca_code);
      $2BaseNet.BaseNet.Request($2BaseNet.BaseUrl.ServerDomain + $2BaseNet.Url.LOGINCODE, n, "GET").then(function (e) {
        $2Manager.Manager.vo.userVo.ca_code = e.data.ca_code;
        $2Manager.Manager.vo.saveUserData();
        o(e);
      }).catch(function (e) {
        i(e);
      });
    });
  };
  _ctor.prototype.requestIpConfig = function () {
    return new Promise(function (e, t) {
      $2BaseNet.BaseNet.Request($2BaseNet.BaseUrl.GetIpApi, {}, "GET").then(function (t) {
        console.log("getip:", t);
        e(t);
      }).catch(function (e) {
        t(e);
      });
    });
  };
  _ctor.prototype.requestShareConfig = function () {
    var e = this;
    return new Promise(function (t, o) {
      $2BaseNet.BaseNet.Request($2BaseNet.BaseUrl.ServerDomain + $2BaseNet.Url.BMS_SHARE_CONFIG, {
        app_name: e.BMS_APP_NAME,
        version: e.BMS_VERSION
      }, "GET").then(function (o) {
        e._sdk.setShareList(o.data.list);
        t(o.data);
      }).catch(function (e) {
        o(e);
      });
    });
  };
  _ctor.checkContentVaild = function (e) {
    return new Promise(function (t, o) {
      $2BaseNet.BaseNet.Request($2BaseNet.BaseUrl.ServerDomain + $2BaseNet.Url.ANTIDIRT, {
        app_name: "zqsnyxttgame",
        contents: [e]
      }, "POST").then(function (e) {
        if (0 == e.code) {
          if (e.data[0]) {
            t(false);
          } else {
            t(true);
          }
        } else {
          t(false);
        }
      }).catch(function () {
        o(false);
      });
    });
  };
  _ctor.prototype.requestServerTime = function () {
    return $2BaseNet.BaseNet.Request($2BaseNet.BaseUrl.ServerDomain + $2BaseNet.Url.BMS_SERVER_TIME, {}, "GET");
  };
  _ctor.prototype.vibrate = function (e) {
    undefined === e && (e = 0);
    $2Notifier.Notifier.call($2CallID.CallID.Setting_IsEnableShake) && this._sdk.vibrate(e);
  };
  _ctor.prototype.createAppBox = function (e) {
    return !!$2SdkConfig.BoxIdList[this._platformId] && (this._sdk.createAppBox($2SdkConfig.BoxIdList[this._platformId], e), true);
  };
  _ctor.prototype.showAppBox = function () {
    return !!$2SdkConfig.BoxIdList[this._platformId] && (this._sdk.showAppBox($2SdkConfig.BoxIdList[this._platformId]), true);
  };
  _ctor.prototype.showFavoriteGuide = function () {
    return !!this.isBaiDuGame && (this._sdk.showFavoriteGuide(), true);
  };
  _ctor.prototype.goRate = function (e) {
    this._sdk.goRate(e);
  };
  _ctor.prototype.setLoginFinish = function () {
    this._sdk.setLoginFinish();
  };
  _ctor.prototype.toPay = function (e) {
    this._sdk.toPay(e);
  };
  _ctor.prototype.toSubscribe = function (e) {
    this._sdk.toPay(e);
  };
  _ctor.prototype.toRestorePay = function () {
    this._sdk.toRestorePay();
  };
  _ctor.prototype.getNativeAdInfo = function () {
    return this._sdk.getNativeAdInfo();
  };
  _ctor.prototype.reportAdShowByType = function (e, t) {
    console.log("原生 reportAdShowBYType", e, t);
    this._sdk.reportAdShow(e, t);
  };
  _ctor.prototype.nativeAdRefresh = function () {
    this._sdk.nativeAdRefresh();
  };
  _ctor.prototype.toShareFaceBook = function (e) {
    this._sdk.toShareFaceBook(e);
  };
  _ctor.prototype.notifyCreateRole = function (e) {
    this._sdk.notifyCreateRole(e);
  };
  _ctor.prototype.notifyRoleEnterGame = function (e) {
    this._sdk.notifyRoleEnterGame(e);
  };
  _ctor.prototype.notifyRoleLevelUp = function (e) {
    this._sdk.notifyRoleLevelUp(e);
  };
  _ctor.random = function (e, t) {
    return e + Math.floor(Math.random() * (t - e));
  };
  _ctor.prototype.checkContent = function (e) {
    return this._sdk.checkContent(e);
  };
  _ctor.prototype.getStorageItem = function (e) {
    return this._sdk.getStorageItem(e);
  };
  _ctor.prototype.setStorageItem = function (e, t) {
    this._sdk.setStorageItem(e, t);
  };
  _ctor.prototype.clearStorage = function () {
    this._sdk.clearStorage();
  };
  _ctor.prototype.removeStorageItem = function (e) {
    this._sdk.removeStorageItem(e);
  };
  _ctor.isInit = false;
  _ctor._version = "1.1.0";
  return _ctor;
}();
exports.WonderSdk = exp_WonderSdk;