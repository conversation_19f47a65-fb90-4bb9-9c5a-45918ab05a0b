Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.StorageManager = undefined;
var $2Log = require("Log");
var $2StorageID = require("StorageID");
var $2lzstring = require("lzstring");
var $2Manager = require("Manager");
var $2Time = require("Time");
var exp_StorageManager = function () {
  function _ctor() {
    this.SAVELIST = ["GameTaskDay", "GameTaskAchieve", "bph_shopGoodspck", "UserEquipPack", "bph_fragmentspack", "TPDG-Record-MBack", "bph_dailyAdpackpack", "GameTaskWeek", "TPDG-Record-Shop", "saveShopList", "MCatGame", "UserGuides", "SaveGetTaskId", "userdata", "SaveProgressId", "bph_fightinfopack", "TaskProgressData"];
  }
  _ctor.prototype.clear = function () {
    wonderSdk.clearStorage();
  };
  _ctor.prototype.remove = function (e) {
    wonderSdk.removeStorageItem(e);
  };
  _ctor.prototype.getBool = function (e, t) {
    undefined === t && (t = false);
    var o = wonderSdk.getStorageItem(e);
    if (isNullOrEmpty(o)) {
      return t;
    } else {
      return 0 != o;
    }
  };
  _ctor.prototype.setBool = function (e, t) {
    var o = t ? 1 : 0;
    wonderSdk.setStorageItem(e, o);
  };
  _ctor.prototype.getNumber = function (e, t) {
    undefined === t && (t = 0);
    var o = wonderSdk.getStorageItem(e);
    if (isNullOrEmpty(o)) {
      return t;
    } else {
      return o;
    }
  };
  _ctor.prototype.setNumber = function (e, t) {
    wonderSdk.setStorageItem(e, t);
  };
  _ctor.prototype.getString = function (e, t, o) {
    undefined === t && (t = "");
    undefined === o && (o = false);
    var i = wonderSdk.getStorageItem(e);
    if (isNullOrEmpty(i)) {
      return t;
    } else {
      if (o) {
        return $2lzstring.decompressFromBase64(i);
      } else {
        return i;
      }
    }
  };
  _ctor.prototype.getItem = function (e, t) {
    undefined === t && (t = true);
    var o = wonderSdk.getStorageItem(e);
    if (isNullOrEmpty(o)) {
      return "";
    } else {
      if (t) {
        return $2lzstring.decompressFromBase64(o);
      } else {
        return o;
      }
    }
  };
  _ctor.prototype.setItem = function (e, t, o) {
    undefined === o && (o = true);
    this.setString(e, t, o);
  };
  _ctor.prototype.setString = function (e, t, o) {
    undefined === o && (o = false);
    wonderSdk.setStorageItem(e, o ? $2lzstring.compressToBase64(t) : t);
  };
  _ctor.prototype.getObject = function (e, t) {
    undefined === t && (t = null);
    var o = wonderSdk.getStorageItem(e);
    if (isNullOrEmpty(o)) {
      return t;
    }
    var n = JSON.parse(o);
    null == n && $2Log.Log.error("StorageManager.getObject error", e, o);
    return n;
  };
  _ctor.prototype.setObject = function (e, t) {
    if (null != t) {
      var o = JSON.stringify(t);
      wonderSdk.setStorageItem(e, o);
    } else {
      $2Log.Log.error("StorageManager.setObject null", e);
    }
  };
  _ctor.prototype.getArray = function (e, t) {
    undefined === t && (t = null);
    var o = wonderSdk.getStorageItem(e);
    if (isNullOrEmpty(o)) {
      return t;
    }
    var n = JSON.parse(o);
    null == n && $2Log.Log.error("StorageManager.getArray error", e, o);
    return n;
  };
  _ctor.prototype.setArray = function (e, t) {
    if (null != t) {
      var o = JSON.stringify(t);
      wonderSdk.setStorageItem(e, o);
    } else {
      $2Log.Log.error("StorageManager.setArray null", e);
    }
  };
  _ctor.prototype.setPersonKey = function (e) {
    this._personKey = e;
  };
  _ctor.prototype.getPrivyBool = function (e, t) {
    undefined === t && (t = false);
    if (null == this._personKey) {
      return $2Log.Log.error("getPrivyBool _personKey null"), t;
    } else {
      return e = this._personKey + e, this.getBool(e, t);
    }
  };
  _ctor.prototype.setPrivyBool = function (e, t) {
    if (null != this._personKey) {
      e = this._personKey + e;
      this.setBool(e, t);
    } else {
      $2Log.Log.error("setPrivyBool _personKey null");
    }
  };
  _ctor.prototype.getPrivyNumber = function (e, t) {
    undefined === t && (t = 0);
    if (null == this._personKey) {
      return $2Log.Log.error("getPrivyNumber _personKey null"), t;
    } else {
      return e = this._personKey + e, this.getNumber(e, t);
    }
  };
  _ctor.prototype.setPrivyNumber = function (e, t) {
    if (null != this._personKey) {
      e = this._personKey + e;
      this.setNumber(e, t);
    } else {
      $2Log.Log.error("setPrivyNumber _personKey null");
    }
  };
  _ctor.prototype.getPrivyString = function (e, t) {
    undefined === t && (t = "");
    if (null == this._personKey) {
      return $2Log.Log.error("getPrivyString _personKey null"), t;
    } else {
      return e = this._personKey + e, this.getString(e, t);
    }
  };
  _ctor.prototype.setPrivyString = function (e, t) {
    if (null != this._personKey) {
      e = this._personKey + e;
      this.setString(e, t);
    } else {
      $2Log.Log.error("setPrivyString _personKey null");
    }
  };
  _ctor.prototype.getPrivyObject = function (e, t) {
    undefined === t && (t = null);
    if (null == this._personKey) {
      return $2Log.Log.error("getPrivyObject _personKey null"), t;
    } else {
      return e = this._personKey + e, this.getObject(e, t);
    }
  };
  _ctor.prototype.setPrivyObject = function (e, t) {
    cc.log(this._personKey, e, t);
    if (null != this._personKey) {
      e = this._personKey + e;
      this.setObject(e, t);
    } else {
      $2Log.Log.error("setPrivyObject _personKey null");
    }
  };
  _ctor.prototype.getPrivyArray = function (e, t) {
    undefined === t && (t = null);
    if (null == this._personKey) {
      return $2Log.Log.error("getPrivyArray _personKey null"), t;
    } else {
      return e = this._personKey + e, this.getArray(e, t);
    }
  };
  _ctor.prototype.setPrivyArray = function (e, t) {
    if (null != this._personKey) {
      e = this._personKey + e;
      this.setArray(e, t);
    } else {
      $2Log.Log.error("setPrivyArray _personKey null");
    }
  };
  Object.defineProperty(_ctor.prototype, "personKey", {
    get: function () {
      return this._personKey;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.pushAllDataSave = function () {
    var e = this;
    cc.log("[pushAllDataSave][保存数据到远端]");
    if ($2Manager.Manager.vo.openId) {
      $2Manager.Manager.vo.userVo.saveDataTime = $2Time.Time.serverTimeMs;
      $2Manager.Manager.vo.saveUserData();
      var t = {};
      this.SAVELIST.forEach(function (o) {
        var i = e.getString(o);
        i && ("string" != typeof i && "number" != typeof i || (t[o] = i));
      });
      wonderSdk.pushDataSave($2StorageID.StorageID.GameTag, t);
    }
  };
  _ctor.prototype.clearRemoteDataSave = function () {
    wonderSdk.pushDataSave($2StorageID.StorageID.GameTag, "");
  };
  return _ctor;
}();
exports.StorageManager = exp_StorageManager;