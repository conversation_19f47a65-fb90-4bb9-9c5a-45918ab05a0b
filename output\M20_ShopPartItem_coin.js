/**
 * M20_ShopPartItem_coin
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Cfg = require('Cfg');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2GameUtil = require('GameUtil');
const $2M20_PartItem = require('M20_PartItem');
const $2M20_ShopPartItem = require('M20_ShopPartItem');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__spreadArrays = __spreadArrays;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: $2M20_ShopPartItem.default,

    properties: {
    },

    // use this for initialization
    onLoad: function () {
    },

    getList: function () {
        return cc__spreadArrays($2GameUtil.GameUtil.getRandomInArray($2Cfg.Cfg.BagShopItem.filter({
        type: $2CurrencyConfigCfg.CurrencyConfigDefine.Coin
        }), 3));
    },

    resetView: function () {
        this._super();
        for (var t = 0; t < this.content.length; t++) {
        var o = this.content[t];
        var i = this.contentnode.children[t] || cc.instantiate(this.cloneitem);
        i.setAttribute({
        parent: this.contentnode
        });
        i.getComponent($2M20_PartItem.default).setdata(o, false, 1);
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
