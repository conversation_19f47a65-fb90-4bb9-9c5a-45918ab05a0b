/**
 * Bullet_LigaturePonit
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameUtil = require('GameUtil');
const $2BulletBase = require('BulletBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2BulletBase.default,

    properties: {
    },

    ctor: function () {
        this.endTarget = cc.v2(0, 0)
    },

    // use this for initialization
    onLoad: function () {
    },

    setTarget: function (e, t) {
        this.node.height = 0;
        this.node.opacity = 0;
        this.startTarget = e;
        this.endTarget = t;
        this.resetLigature(0);
        cc.tween(this.node).to(.1, {
        opacity: 255
        }).start();
    },

    resetLigature: function (e) {
        var t = cc.Vec2.distance(this.startTarget, this.endTarget) / this.vo.skillCfg.scale;
        var o = cc.misc.lerp(this.node.height, t, 2 * e);
        var i = $2GameUtil.GameUtil.GetAngle(this.startTarget, this.endTarget) + 90;
        this.node.setAttribute({
        height: o,
        angle: i,
        position: this.startTarget
        });
        this.collider.size.height = this.node.height;
        this.collider.offset.y = this.node.height / 2;
    },

    onUpdate: function (t) {
        this.resetLigature(t);
        this._super(t);
    },

    unuse: function () {
        this.node.height = 0;
        this.node.opacity = 0;
        this._super();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
