var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GuidesController = undefined;
var $2ListenID = require("ListenID");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2KnapsackVo = require("KnapsackVo");
var $2GuidesModel = require("GuidesModel");
var exp_GuidesController = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t.setup($2GuidesModel.default.instance);
    t.changeListener(true);
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {
    this._model.reset();
  };
  Object.defineProperty(_ctor.prototype, "classname", {
    get: function () {
      return "GuidesController";
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerAllProtocol = function () {};
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Login_Finish, this.loginFinish, this);
    $2Notifier.Notifier.changeListener(true, $2ListenID.ListenID.M20_GuideHandle, this.handleGuide, this);
  };
  _ctor.prototype.handleGuide = function (e) {
    if (e) {
      $2UIManager.UIManager.Open("ui/common/alert/CommonGuide", $2MVC.MVC.openArgs().setIsNeedLoading(false).setParam({
        isnewuser: 0 == $2Manager.Manager.vo.userVo.loginCount
      }));
    } else {
      $2UIManager.UIManager.Close("ui/common/alert/CommonGuide");
    }
  };
  _ctor.prototype.loginFinish = function () {
    this.mode.pack = new $2KnapsackVo.KnapsackVo.Mgr(true, "UserGuides");
    1 == $2Manager.Manager.vo.switchVo.m20guideToggle[0] && this.handleGuide(true);
    if ($2Manager.Manager.vo.userVo.loginCount > 0) {
      $2Manager.Manager.vo.userVo.guideIndex = 17;
      $2Manager.Manager.vo.saveUserData();
    }
  };
  return _ctor;
}($2MVC.MVC.MController);
exports.GuidesController = exp_GuidesController;