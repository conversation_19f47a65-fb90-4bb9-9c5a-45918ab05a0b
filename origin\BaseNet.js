Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.BaseNet = exports.Url = exports.BaseUrl = undefined;
(function (e) {
  e.ServerDomain = "https://game.zuiqiangyingyu.net";
  e.ServerSourceAPI = "https://api.wanjiazhuhua.com";
  e.GetIpApi = "https://ifconfig.co";
})(exports.BaseUrl || (exports.BaseUrl = {}));
(function (e) {
  e.BMS_LAUNCH_CONFIG = "/common/config/info";
  e.BMS_SHARE_CONFIG = "/common/game/share_list";
  e.BMS_TOFU_CONFIG = "/common/game/ads";
  e.BMS_SIGN_IN_WX = "/common/session/sign_in";
  e.BMS_SIGN_IN_BD = "/common/baidu/sign_in";
  e.BMS_SIGN_IN_QQ = "/common/qqminiapp/sign_in";
  e.BMS_SERVER_TIME = "/common/common/time";
  e.BMS_CARD_SHARE = "/common/share/hit";
  e.B<PERSON>_CARD_SHARE_INFO = "/common/share/info";
  e.BMS_SHARE_SHOW = "/statistics/share/show";
  e.BMS_LOGIN_LOG = "/statistics/login_log";
  e.BMS_GAME = "/statistics/game";
  e.BMS_AD_SHOW = "/statistics/ad/show";
  e.BMS_AD_HIT = "/statistics/ad/hit";
  e.BMS_HINT = "/statistics/hint";
  e.BMS_IP_IS_ENABLE = "/common/is/is";
  e.DECODE_DATA = "/common/wechat/decode_data";
  e.ANTIDIRT = "/common/toutiao/antidirt";
  e.GETADVINFO = "/api/sdk/yw/adv/getAdvInfo";
  e.MATERIALSS = "/common/ads/material-ss";
  e.LOGINCODE = "/common/login-code/check";
  e.DATA_MULTIGET = "/common/game-data/multi-get";
  e.DATA_GET = "/common/game-data/get";
  e.DATA_SAVE = "/common/game-data/save";
  e.GIFT_RECEIVE_REWARD = "/common/toutiao/gift-receive-reward";
})(exports.Url || (exports.Url = {}));
var exp_BaseNet = function () {
  function _ctor() {}
  _ctor.Request = function (t, o, i, n) {
    var r;
    undefined === n && (n = "json");
    switch (i) {
      case "GET":
        r = _ctor.httpGet(t, o, n);
        break;
      case "POST":
        r = _ctor.httpPost(t, o, n);
    }
    return r;
  };
  _ctor.httpGet = function (e, t, o) {
    undefined === o && (o = "json");
    return new Promise(function (i, n) {
      var r = new XMLHttpRequest();
      if (t) {
        var a = "?";
        for (var s in t) {
          "?" != a && (a += "&");
          a += s + "=" + t[s];
        }
        e += a;
      }
      r.open("GET", e, true);
      r.onreadystatechange = function () {
        if (4 == r.readyState) {
          if (200 == r.status) {
            if ("text" == o) {
              i(r.responseText);
            } else if ("json" == o && "string" == typeof r.response) {
              if (r.response.includes("code") && r.response.includes("data")) {
                i(JSON.parse(r.response));
              } else {
                n({
                  code: r.status,
                  msg: r.response,
                  data: {}
                });
              }
            } else {
              i(r.response);
            }
          } else {
            n({
              code: r.status,
              msg: r.statusText,
              data: {}
            });
          }
        }
      };
      switch (o) {
        case "json":
          r.setRequestHeader("content-type", "application/json");
          break;
        case "text":
          r.setRequestHeader("content-type", "text/plain");
      }
      "blob" != o && "arraybuffer" != o && "text" != o || (r.responseType = o);
      r.timeout = 5e3;
      r.ontimeout = function () {
        n({
          code: -1,
          msg: "网络异常，消息发送超时",
          data: {}
        });
      };
      r.onerror = function () {
        n({
          code: -1,
          msg: "网络异常，消息发送超时",
          data: {}
        });
      };
      r.send();
    });
  };
  _ctor._EncodeFormData = function (e) {
    var t = [];
    var o = /%20/g;
    for (var i in e) {
      var n = e[i];
      var r = encodeURIComponent(i).replace(o, "+") + "=" + encodeURIComponent(n).replace(o, "+");
      t.push(r);
    }
    return t.join("&");
  };
  _ctor.httpPost = function (t, o, i) {
    undefined === i && (i = "json");
    return new Promise(function (n, r) {
      var a;
      var s = new XMLHttpRequest();
      s.open("POST", t, true);
      s.onreadystatechange = function () {
        if (4 == s.readyState) {
          if (200 == s.status) {
            if ("text" == i) {
              n(s.responseText);
            } else if ("json" == i && "string" == typeof s.response) {
              if (s.response.includes("code") && s.response.includes("data")) {
                n(JSON.parse(s.response));
              } else {
                r({
                  code: s.status,
                  msg: s.response,
                  data: {}
                });
              }
            } else {
              n(s.response);
            }
          } else {
            r({
              code: s.status,
              msg: s.statusText,
              data: {}
            });
          }
        }
      };
      s.setRequestHeader("content-type", "application/x-www-form-urlencoded");
      cc.sys.isNative && s.setRequestHeader("Accept-Encoding", "gzip,deflate");
      "blob" != i && "arraybuffer" != i && "text" != i || (s.responseType = i);
      o && (a = _ctor._EncodeFormData(o));
      s.timeout = 7e3;
      s.ontimeout = function () {
        r({
          code: -1,
          msg: "网络异常，消息发送超时",
          data: {}
        });
      };
      s.onerror = function () {
        r({
          code: -1,
          msg: "网络异常，消息发送超时",
          data: {}
        });
      };
      s.send(a);
    });
  };
  return _ctor;
}();
exports.BaseNet = exp_BaseNet;