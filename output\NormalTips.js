/**
 * NormalTips
 * 组件类 - 从编译后的JS反编译生成
 */

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        label: {
            type: cc.Label,
            default: null
        }
    },

    ctor: function () {
        this.label = null
    },

    // use this for initialization
    onLoad: function () {
    },

    start: function () {
        // TODO: 实现方法逻辑
    },

    setText: function (e) {
        var t = this;
        this.label.string = e;
        this.node.opacity = 0;
        this.scheduleOnce(function () {
        if (t.isValid) {
        if (t.node.children[0].width > 600) {
        var e = t.node.children[0].getComponent(cc.Layout);
        e.enabled = false;
        t.label.node.width = t.node.children[0].width = 600;
        t.label.overflow = cc.Label.Overflow.RESIZE_HEIGHT;
        e.enabled = true;
        }
        t.node.opacity = 255;
        }
        });
        return this;
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
