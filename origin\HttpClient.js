Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.HttpClient = undefined;
var exp_HttpClient = function () {
  function _ctor() {}
  _ctor.Request = function (t, o, i, n) {
    var r;
    undefined === n && (n = "json");
    switch (i) {
      case "GET":
        r = _ctor.httpGet(t, o, n);
        break;
      case "POST":
        r = _ctor.httpPost(t, o, n);
    }
    return r;
  };
  _ctor.httpGet = function (e, t, o) {
    undefined === o && (o = "json");
    return new Promise(function (i, n) {
      var r = new XMLHttpRequest();
      if (t) {
        var a = "?";
        for (var s in t) {
          "?" != a && (a += "&");
          a += s + "=" + t[s];
        }
        e += a;
      }
      r.open("GET", e, true);
      r.onreadystatechange = function () {
        if (4 == r.readyState) {
          if (200 == r.status) {
            if ("text" == o) {
              i(r.responseText);
            } else if ("json" == o && "string" == typeof r.response) {
              i(JSON.parse(r.response));
            } else {
              i(r.response);
            }
          } else {
            n({
              code: r.status,
              msg: r.statusText,
              data: {}
            });
          }
        }
      };
      switch (o) {
        case "json":
          r.setRequestHeader("content-type", "application/json");
          break;
        case "text":
          r.setRequestHeader("content-type", "text/plain");
      }
      "blob" != o && "arraybuffer" != o && "text" != o || (r.responseType = o);
      r.timeout = 5e3;
      r.ontimeout = function () {
        n({
          code: -1,
          msg: "网络异常，消息发送超时",
          data: {}
        });
      };
      r.onerror = function () {
        n({
          code: -1,
          msg: "网络异常，消息发送超时",
          data: {}
        });
      };
      r.send();
    });
  };
  _ctor._EncodeFormData = function (e) {
    var t = [];
    var o = /%20/g;
    for (var i in e) {
      var n = e[i];
      var r = encodeURIComponent(i).replace(o, "+") + "=" + encodeURIComponent(n).replace(o, "+");
      t.push(r);
    }
    return t.join("&");
  };
  _ctor.httpPost = function (t, o, i) {
    undefined === i && (i = "json");
    return new Promise(function (n, r) {
      var a;
      var s = new XMLHttpRequest();
      s.open("POST", t, true);
      s.onreadystatechange = function () {
        if (4 == s.readyState) {
          if (200 == s.status) {
            if ("text" == i) {
              n(s.responseText);
            } else if ("json" == i && "string" == typeof s.response) {
              n(JSON.parse(s.response));
            } else {
              n(s.response);
            }
          } else {
            r({
              code: s.status,
              msg: s.statusText,
              data: {}
            });
          }
        }
      };
      s.setRequestHeader("content-type", "application/x-www-form-urlencoded");
      cc.sys.isNative && s.setRequestHeader("Accept-Encoding", "gzip,deflate");
      "blob" != i && "arraybuffer" != i && "text" != i || (s.responseType = i);
      o && (a = _ctor._EncodeFormData(o));
      s.timeout = 5e3;
      s.ontimeout = function () {
        r({
          code: -1,
          msg: "timeout",
          data: {}
        });
      };
      s.onerror = function (e) {
        r({
          code: -1,
          msg: "onerror",
          data: e
        });
      };
      s.send(a);
    });
  };
  return _ctor;
}();
exports.HttpClient = exp_HttpClient;