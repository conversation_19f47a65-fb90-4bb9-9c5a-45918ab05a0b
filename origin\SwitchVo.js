Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SwitchVo = undefined;
var $2TestController = require("TestController");
var exp_SwitchVo = function () {
  function _ctor() {
    this.GM = 1;
    this.isNotAd = false;
    this.isLog = false;
    this.isTaLog = 1;
    this.shardAdapter = 1;
    this.superSkillCount = [1, 1];
    this.weather = [1, 10, 5];
    this.weatherWegiht = [1, 10, 2, 10, 3, 10, 4, 10, 5, 10, 6, 10, 7, 10];
    this.fightStamina = 2;
    this.minigame = [0];
    this.feedback = [0];
    this.dmm = [1, 30];
    this.tryWeaponList = ["10-4001-3-15", "18-4010-3-20", "25-4020-3-30"];
    this.modeUnlock = [{
      mid: 1,
      type: 5,
      num: 2
    }];
    this.onGameHighWpRate = 3;
    this.buildModeAirDropReward = [200, 300, 300, 100, 400, 30, 1e3, 2];
    this.buildModeAirDrop = [1, 1, 5, 3];
    this.refreshSetting = [3600, 20];
    this.adBuyStamina = [1, 10, 5];
    this.diamondBuyStamina = [150, 15, 3];
    this.lvSweep = [3, 2, 3];
    this.adGetSilverCoin = 30;
    this.adReviveSliverCoin = 100;
    this.gridWeight = [1, 15];
    this.lvDiff = [[1, 0, 5, .4, .02], [1, 5, 8, .5, .03]];
    this.lvDiffAd = 6;
    this.lvDiffEasy = [[1, 0, 5, .4, .02], [1, 5, 8, .5, .03]];
    this.lv2Equip = [[1, 6, 15], [2, 8, 10], [3, 8, 5], [4, 5, 2], [9999, 5, 15]];
    this.Lv1EquipAppear = [[1, 0, 3, .3, .2, .2]];
    this.grid12Appear = [[1, 0, 3, 45, 20, 0], [1, 3, 6, 35, 10, 10], [1, 6, 10, 10, 5, 10], [2, 0, 3, 45, 20, 0], [2, 3, 6, 35, 10, 10], [2, 6, 10, 10, 5, 10], [3, 0, 3, 45, 20, 0], [3, 3, 6, 35, 10, 10], [3, 6, 10, 10, 5, 10], [3, 10, 15, 5, 5, 12], [3, 15, 20, 5, 5, 10], [4, 0, 3, 45, 20, 0], [4, 3, 6, 35, 10, 10], [4, 6, 10, 10, 5, 10], [4, 10, 15, 5, 5, 12], [4, 15, 20, 5, 5, 10], [5, 0, 3, 45, 20, 0], [5, 3, 6, 35, 10, 10], [5, 6, 10, 10, 5, 10], [5, 10, 15, 5, 5, 12], [5, 15, 20, 5, 5, 10], [6, 0, 3, 45, 20, 0], [6, 3, 6, 35, 10, 10], [6, 6, 10, 10, 5, 10], [6, 10, 15, 5, 5, 12], [6, 15, 20, 5, 5, 10], [7, 0, 3, 45, 20, 0], [7, 3, 6, 35, 10, 10], [7, 6, 10, 10, 5, 10], [7, 10, 15, 5, 5, 12], [7, 15, 20, 5, 5, 10], [8, 0, 3, 45, 20, 0], [8, 3, 6, 35, 10, 10], [8, 6, 10, 10, 5, 10], [8, 10, 15, 5, 5, 12], [8, 15, 20, 5, 5, 10], [9, 0, 3, 45, 20, 0], [9, 3, 6, 35, 10, 10], [9, 6, 10, 10, 5, 10], [9, 10, 15, 5, 5, 12], [9, 15, 20, 5, 5, 10], [10, 0, 3, 45, 20, 0], [10, 3, 6, 35, 10, 10], [10, 6, 10, 10, 5, 10], [10, 10, 15, 5, 5, 12], [10, 15, 20, 5, 5, 10], [11, 0, 3, 45, 20, 0], [11, 3, 6, 35, 10, 10], [11, 6, 10, 10, 5, 10], [11, 10, 15, 5, 5, 12], [11, 15, 20, 5, 5, 10], [12, 0, 3, 45, 20, 0], [12, 3, 6, 35, 10, 10], [12, 6, 10, 10, 5, 10], [12, 10, 15, 5, 5, 12], [12, 15, 20, 5, 5, 10], [13, 0, 3, 45, 20, 0], [13, 3, 6, 35, 10, 10], [13, 6, 10, 10, 5, 10], [13, 10, 15, 5, 5, 12], [13, 15, 20, 5, 5, 10], [14, 0, 3, 45, 20, 0], [14, 3, 6, 35, 10, 10], [14, 6, 10, 10, 5, 10], [14, 10, 15, 5, 5, 12], [14, 15, 20, 5, 5, 10], [15, 0, 3, 45, 20, 0], [15, 3, 6, 35, 10, 10], [15, 6, 10, 10, 5, 10], [15, 10, 15, 5, 5, 12], [15, 15, 20, 5, 5, 10], [16, 0, 3, 45, 20, 0], [16, 3, 6, 35, 10, 10], [16, 6, 10, 10, 5, 10], [16, 10, 15, 5, 5, 12], [16, 15, 20, 5, 5, 10], [9999, 0, 3, 45, 20, 0], [9999, 3, 6, 35, 10, 10], [9999, 6, 10, 10, 5, 10]];
    this.shopDiscount = [[.5, .7, .9, 1], [20, 40, 40, 20]];
    this.m20guideToggle = [1, 1];
    this.adGetHeroFragmentCount = 2;
    this.heroBfragmentWeight = [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20], [80, 30, 10, 9, 9, 7, 5, 3, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1]];
    this.heroAfragmentWeight = [[2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40], [200, 80, 8, 6, 5, 4, 3, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1]];
    this.shopAdGift = [[2, 3, 4], [1e3, 5, 5]];
    this.adRevive = 1;
    this.isEnterBackpack = 1;
    this.limit2Grid = .7;
    this.fit3GridEquip = [.5, .2];
    this.Lv3EquipAppear = [1, 3, 5, .25, .85];
    this.speedUnlock = 1;
    this.gameSpeed = 1;
    this.equipbuff = [[1, .7, .8], [2, .7, .8], [3, 0, 0]];
    this.adGrid = [3, .6];
    this.gemAppearBegin = [[1, 4, 99, 99, .4], [2, 0, 99, 99, .4]];
    this.gemAppearWeight = [[1, 0, 3, 5, 5], [1, 3, 6, 10, 15], [1, 6, 10, 15, 20], [2, 0, 3, 5, 5], [2, 3, 6, 10, 15], [2, 6, 10, 15, 20], [3, 0, 3, 5, 5], [3, 3, 6, 10, 15], [3, 6, 10, 15, 20], [3, 10, 15, 20, 25], [3, 15, 20, 15, 20], [9999, 0, 3, 5, 5], [9999, 3, 6, 10, 15], [9999, 6, 10, 15, 20]];
    this.gemAppearSwitch = 1;
    this.gemSuccessWeight = [[1, 1, .8, .6], [2, 1, .8, .6]];
    this.redeemCode = [{
      code: "avge",
      reward: [[1, 2, 70], [1, 2, 20], [1, 2, 10]],
      rewardCount: [[1, 10], [2, 10]],
      type: 1,
      startTime: 1723450965,
      endTime: 1723450965
    }];
    this.dailysign = 1;
    this.task = 0;
    this.greedGem = [[1, 85, 2, 15], [1, 10, 2, 90]];
    this.shopConfig = [[11, 10], [2, 2], [9, 3], [25, 3]];
    this.adRefreshEquip = [99, 1, .9, .1];
    this.deadAnim = 0;
    this.equip3selectAd = 1;
    this.equip3SelectShow = [1, 1, 4, 6, 6];
    this.eggGemAgain = 1;
    this.eggAppearWeight = [.1, .3, .5];
    this.actCoinSweep = 1;
    this.eliteLv = 1;
    this.GameKnife = 1;
    this.GameZombieDef = 1;
    this.dragonCrzay = [[0, 20, .2, 1], [20, 40, .3, 1], [40, 70, .5, 1]];
    this.dragonDiff = [[6e4, 20, 30, .3], [6e4, 30, 70, .5], [60001, 30, 70, .5], [60001, 30, 70, .5]];
    this.miniGameLv = [3005, 3006];
    this.miniGameAdUnlock = 1;
    this.dragonRefreshBuff = [5, 5];
    this.dragonBossDiff = [[6024, 1, .3, 1, 1], [6024, 1, 0, .3, 2], [6024, 2, 0, 1, 5, 2], [6024, 2, .1, .3, 2], [6024, 2, 0, .1, 3]];
    this.diffSelectCfg = [[1, 1], [1, 1]];
    this.diffSelect = 1;
    this.dragonBuffFree = 3;
    this.dragonRevive = 3;
    this.dragonBoxFree = [[1, 0, 10], [2, 0, 3]];
    this.OVSwitch = 1;
  }
  _ctor.prototype.updateSwitchVo = function (e) {
    Object.getOwnPropertyNames(this).forEach(function (t) {
      var o = t;
      var i = t.indexOf("_");
      0 == i && (o = t.substring(i + 1, t.length));
      e.hasOwnProperty(o) && (this[t] = e[o]);
    }.bind(this));
    this.GM && new $2TestController.TestController();
    this.isLog || (console.log = function () {});
    this.shardAdapter = wonderSdk.isWebDev ? .1 : 1;
  };
  return _ctor;
}();
exports.SwitchVo = exp_SwitchVo;