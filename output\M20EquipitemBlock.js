/**
 * M20EquipitemBlock
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Cfg = require('Cfg');
const $2M20Equipitem = require('M20Equipitem');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: $2M20Equipitem.default,

    properties: {
        lockinfo: {
            type: cc.Label,
            default: null
        }
    },

    ctor: function () {
        this.lockinfo = null
    },

    // use this for initialization
    onLoad: function () {
    },

    setInfo: function (t) {
        this._super(t);
        var o = $2Cfg.Cfg.EquipLv.filter({
        equipId: t
        });
        var i = $2Cfg.Cfg.RoleUnlock.find({
        id: t
        });
        this.equipcfg = i;
        this.eueiplvcfgs = o;
    },

    resetState: function () {
        this._super();
        var t = this.mode.fragmentsPack.getVal(this.equipcfg.id);
        if (1 == this.equipcfg.unlock) {
        this.lockinfo.string = cc.js.formatStr("通关章节%d解锁", this.equipcfg.Count);
        } else if (2 == this.equipcfg.unlock) {
        this.lockinfo.string = cc.js.formatStr("%d个碎片解锁", this.equipcfg.Count - t);
        } else {
        3 == this.equipcfg.unlock && (this.lockinfo.string = cc.js.formatStr("%d个视频解锁", this.equipcfg.Count));
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
