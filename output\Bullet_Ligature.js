/**
 * Bullet_Ligature
 * 组件类 - 从编译后的JS反编译生成
 */

const $2FBoxCollider = require('FBoxCollider');
const $2GameUtil = require('GameUtil');
const $2BulletBase = require('BulletBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2BulletBase.default,

    properties: {
        isGradualHeight: {
            displayName: "是否渐进长度",
            default: true
        }
    },

    ctor: function () {
        this.isGradualHeight = true
    },

    // use this for initialization
    onLoad: function () {
    },

    setBulletVo: function (t) {
        this._super(t);
        this.node.opacity = 0;
        this.node.y = -9999999;
        this.node.height = 0;
    },

    setTarget: function (e, t) {
        this.startTarget = e;
        this.endTarget = t;
        this.resetLigature();
        cc.tween(this.node).to(.1, {
        opacity: 255
        }).start();
    },

    resetLigature: function () {
        var e = cc.Vec2.distance(this.startTarget.position, this.endTarget.position);
        var t = this.node.height >= e ? e : this.node.height + 30;
        var o = $2GameUtil.GameUtil.GetAngle(this.startTarget.position, this.endTarget.position) + 90;
        this.node.setAttribute({
        height: this.isGradualHeight ? t : e,
        angle: o,
        position: this.startTarget.position
        });
        if (this.collider instanceof $2FBoxCollider.default) {
        this.collider.size.height = this.node.height;
        this.collider.offset.y = this.node.height / 2;
        }
    },

    onUpdate: function (t) {
        this._super(t);
        if (this.isActive && this.endTarget) {
        if (this.startTarget.isActive && this.endTarget.isActive) {
        this.resetLigature();
        } else {
        this.node.y = -99999;
        this.node.opacity = 0;
        this.startTarget = null;
        this.endTarget = null;
        this._vo.lifeTime = 0;
        }
        }
    },

    unuse: function () {
        this.endTarget = null;
        this._super();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
