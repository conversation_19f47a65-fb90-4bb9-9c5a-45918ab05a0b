/**
 * M20_ShopPartItem
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Notifier = require('Notifier');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        contentnode: {
            type: cc.Node,
            default: null
        },
        title: {
            type: cc.Label,
            default: null
        },
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        }
    },

    ctor: function () {
        this.contentnode = null
        this.title = null
        this.data = null
        this.cloneitem = null
        this.content = []
    },

    // use this for initialization
    onLoad: function () {
    },

    setData: function (e) {
        var t = this;
        this.data = e;
        this.cloneitem || $2Manager.Manager.loader.loadPrefab(e.prefabe).then(function (e) {
        t.cloneitem = e;
        t.refreshData();
        });
    },

    resetView: function () {
        this.title.string = this.data.title;
    },

    refreshData: function () {
        this.content = this.getList();
        this.resetView();
    },

    getList: function () {
        return [];
    },

    refresh: function () {
        // TODO: 实现方法逻辑
    },

    onEnable: function () {
        this.changeListener(true);
    },

    onDisable: function () {
        this.changeListener(false);
    },

    changeListener: function (e) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Item_GoodsChange, this.resetView, this);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
