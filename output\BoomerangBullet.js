/**
 * BoomerangBullet
 * 组件类 - 从编译后的JS反编译生成
 */

const $2BulletBase = require('BulletBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var u = cc.v2();

exports.default = cc.Class({
    extends: $2BulletBase.default,

    properties: {
    },

    ctor: function () {
        this.isBoomerang = false
        this.isCcomeback = false
    },

    // use this for initialization
    onLoad: function () {
    },

    setBulletVo: function (t) {
        this._super(t);
        this.isCcomeback = false;
        this.isBoomerang = !!this.vo.ower.buffMgr.isHasID([11600, 11408, 10160]);
        this.vo.lifeTime = this.isBoomerang ? 10 : 3;
    },

    onUpdate: function (t) {
        this._super(t);
        if (!(this._vo.lifeTime < 0 || this.isDead)) {
        cc.Vec2.multiplyScalar(u, this._vo.shootDir, this.maxSpeed * t);
        cc.Vec2.add(u, this.position, u);
        this.setPosition(u);
        if (!this.isCcomeback && this.isBoomerang && cc.Vec2.squaredDistance(this.vo.shootDir.mul(this.vo.belongSkill.dis).add(this.vo.ower.position).mul(1.3), this.position) < Math.pow(100, 2)) {
        this.vo.shootDir.set(this.vo.ower.position.sub(this.position).normalizeSelf()), this.isCcomeback = true;
        }
        this.isCcomeback && cc.Vec2.squaredDistance(this.vo.startPos, this.position) < Math.pow(50, 2) && (this.vo.lifeTime = 0);
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
