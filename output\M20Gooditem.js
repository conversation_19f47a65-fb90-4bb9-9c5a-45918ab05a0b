/**
 * M20Gooditem
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Manager = require('Manager');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        Count: {
            type: cc.Label,
            default: null
        },
        goodsbg: {
            type: cc.Sprite,
            default: null
        },
        goodimg: {
            type: cc.Sprite,
            default: null
        }
    },

    ctor: function () {
        this.Count = null
        this.goodsbg = null
        this.goodimg = null
    },

    // use this for initialization
    onLoad: function () {
    },

    setdata: function (e) {
        var t;
        this.Count.node.setActive(e.count);
        this.Count.string = null !== (t = e.count) && undefined !== t ? t : 0;
        $2Manager.Manager.loader.loadSpriteToSprit(e.path, this.goodimg, this.node.parent);
        if (!e.bgpath) {
        var img = this.node.getChildByName("img").getComponent(cc.Sprite);
        img.sizeMode = cc.Sprite.SizeMode.RAW;
        }
        this.node.getChildByName("icon_pintu").active = e.isfrag;
        this.goodsbg.node.active = e.bgpath;
        $2Manager.Manager.loader.loadSpriteToSprit(e.bgpath, this.goodsbg, this.node.parent);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
