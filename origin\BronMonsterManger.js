var i;
var cc__extends = __extends;
var cc__assign = __assign;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.BronMonsterManger = undefined;
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2Notifier = require("Notifier");
var $2GameUtil = require("GameUtil");
var $2NodePool = require("NodePool");
var $2Game = require("Game");
var $2Monster = require("Monster");
var $2SkillModel = require("SkillModel");
var $2RewardEvent = require("RewardEvent");
var g = cc.Vec2.ZERO;
var exp_BronMonsterManger = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.level = 1;
    t._batchNum = 1;
    t._batchTime = 0;
    t._batchSumCount = 0;
    t.batchInterval = [80, 80, 80, 80, 80, 80, 80, 80];
    t.maxCount = 600;
    t._maxLen = 0;
    t.isBossRound = false;
    t._RoundMonster = [];
    t.MonsterType = {
      1: "Monster",
      2: "MonsterElite",
      3: "MonsterBoss",
      4: "CanWreckItem"
    };
    t._monTime = 0;
    t._eventTime = 0;
    t._dtTime = 0;
    t._remain = 0;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mainRole", {
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "batchNum", {
    get: function () {
      return this._batchNum;
    },
    set: function (e) {
      this._batchNum = e;
      this._batchTime = this.batchInterval[e - 1];
      this.RoundMonster = $2Game.ModeCfg.MonsterLv.filter({
        lv: this.level,
        round: this._batchNum
      });
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_GameRound, e);
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_CountDown, this._batchTime);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "RoundMonster", {
    get: function () {
      return this._RoundMonster;
    },
    set: function (e) {
      var t = this;
      this._RoundMonster.length = 0;
      e.push.apply(e, $2Game.ModeCfg.MonsterLv.filter({
        lv: -1
      }));
      e.forEach(function (e) {
        var o = $2GameUtil.GameUtil.getRandomInArray(e.monId, 1)[0];
        var i = $2Cfg.Cfg.Monster.get(o);
        i && t._RoundMonster.push(cc__assign({
          letTime: 0,
          createNum: 0,
          type: i.type,
          monsterID: o
        }, e));
      });
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onEnable = function () {
    this.changeListener(true);
  };
  _ctor.prototype.onDisable = function () {
    this.changeListener(false);
  };
  _ctor.prototype.changeListener = function () {};
  _ctor.prototype.init = function () {
    this._maxLen = Math.max(cc.winSize.width, cc.winSize.height) / 2;
    this.batchNum = 1;
    $2SkillModel.default.getInstance.cutLevelSkill = $2Game.ModeCfg.Skiilpool.get(this.level);
    this.RewardEvent = new $2RewardEvent.RewardEvent.Manager(this);
    this.RewardEvent.createEventList();
  };
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "role", {
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "randomD", {
    get: function () {
      cc.Vec2.UP.rotate(Math.PI * (2 * Math.random() - 1), g);
      return g;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "randomL", {
    get: function () {
      return $2Game.Game.random(this._maxLen + 100, this._maxLen + 200);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "randomPos", {
    get: function () {
      var e = this.mainRole.position.add(this.randomD.mul(this.randomL));
      return {
        x: e.x,
        y: e.y
      };
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.getMonSpPos = function (e, t) {
    undefined === t && (t = 0);
    return null;
  };
  _ctor.prototype.isArriveOn = function () {
    return false;
  };
  _ctor.prototype.getRandomPos = function (e) {
    var t = this.mainRole.position.add(this.randomD.mul(e));
    return {
      x: t.x,
      y: t.y
    };
  };
  _ctor.prototype.getRandomPosByAngle = function (e, t) {
    cc.Vec2.UP.rotate(e + 30, g);
    var o = this.mainRole.position.add(g.mul(t));
    return {
      x: o.x,
      y: o.y
    };
  };
  _ctor.prototype.addMonsterTide = function (e) {
    var t = this;
    cc.log("生成兽潮");
    $2Notifier.Notifier.send($2ListenID.ListenID.Fight_GameMonsterCome);
    var o = 0;
    var i = this.game.mainRole.position;
    var n = this.randomL;
    $2Game.Game.timer(function () {
      g = i.add($2GameUtil.GameUtil.AngleAndLenToPos(6 * o, n + o % 2 * 200));
      t.createMonster(e, {
        x: g.x,
        y: g.y
      }).then(function (e) {
        e.toMove();
      });
      o++;
    }, .05, e.Count || 60);
  };
  _ctor.prototype.addSprintMonster = function (e) {
    var t = this;
    var o = $2GameUtil.GameUtil.getRandomInArray([45, 135, 225, 315], $2Game.Game.random(2, 4));
    var i = this.randomL;
    o.forEach(function (o, n) {
      $2Game.Game.timerOnce(function () {
        g = t.mainRole.position.add(t.mainRole.forwardDirection.normalize().mul(100));
        var n = $2GameUtil.GameUtil.AngleAndLenToPos(o, 1.5 * i).add(g);
        var r = $2GameUtil.GameUtil.AngleAndLenToPos(o + 180, 2 * i).add(g);
        $2Game.Game.timer(function () {
          t.createMonster(e, {
            x: n.x,
            y: n.y
          }).then(function (e) {
            e.toSprint(r);
          });
        }, 0, e.Count || 60);
      }, n);
    });
  };
  _ctor.prototype.getMonsterSetType = function () {
    return null;
  };
  _ctor.prototype.createMonster = function (e, t, o) {
    var i = this;
    return new Promise(function (n) {
      var r = $2Cfg.Cfg.Monster.get(e.monsterID);
      if (r) {
        if (!(i.game._entityNode.childrenCount > i.maxCount)) {
          3 == r.type && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_ShowGameTips, 3, "Boss来袭");
          var l = $2NodePool.NodePool.spawn("entity/fight/" + i.MonsterType[r.type]);
          i.getMonsterSetType(e) && (l = i.getMonsterSetType(e));
          l.setNodeAssetFinishCall(function (a) {
            var s;
            var c;
            if (!a) {
              return console.error("怪物生成错误", null == r ? undefined : r.name);
            }
            if (o) {
              var l = a.getComponent($2Monster.Monster);
              l && l.constructor.name != (null === (s = o.toString().match(/^function\s+([a-zA-Z_$][0-9a-zA-Z_$]*)\s*\(/)) || undefined === s ? undefined : s[1]) && l.destroy();
            } else {
              o || (o = $2Monster.Monster);
            }
            var u = a.getORaddComponent(o);
            a.setAttribute({
              parent: i.game._entityNode,
              active: true,
              opacity: 255
            });
            var p = cc.v2(t.x, t.y);
            u.setPosition(p);
            u.monsterId = +r.id;
            u.lvCfg = e;
            u.init();
            null === (c = u.steering) || undefined === c || c.setTargetAgent1(i.mainRole);
            i.game._monsterMap.set(u.ID, u);
            i.game.elementMap.set(u.ID, u);
            n(u);
          });
        }
      } else {
        console.error("没有找到怪物配置", e.name);
      }
    });
  };
  _ctor.prototype.addMonsterById = function (e, t, o) {
    var i = this;
    undefined === o && (o = 0);
    $2Game.Game.timer(function () {
      i.createMonster(e, t || i.randomPos).then(function (e) {
        e.toMove();
      });
    }, e.bronSpeed, e.Count);
    this._batchSumCount--;
  };
  _ctor.prototype.onUpdate = function () {};
  _ctor.prototype.onDestroy = function () {
    this.RewardEvent = null;
  };
  return _ctor;
}(cc.Component);
exports.BronMonsterManger = exp_BronMonsterManger;