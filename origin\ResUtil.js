Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ResUtil = undefined;
var $2ResKeeper = require("ResKeeper");
var $2AssetLoader = require("AssetLoader");
var exp_ResUtil = function () {
  function _ctor() {}
  _ctor.getResKeeper = function (t, o) {
    if (t) {
      return t.getComponent($2ResKeeper.default) || (o ? t.addComponent($2ResKeeper.default) : _ctor.getResKeeper(t.parent, o));
    } else {
      return $2AssetLoader.assetLoader.getResKeeper();
    }
  };
  return _ctor;
}();
exports.ResUtil = exp_ResUtil;