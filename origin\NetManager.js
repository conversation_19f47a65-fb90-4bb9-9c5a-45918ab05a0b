Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.NetManager = undefined;
var $2HttpClient = require("HttpClient");
var exp_NetManager = function () {
  function _ctor() {
    this._channels = {};
    this.socket = null;
  }
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == this._instance && (this._instance = new _ctor());
      return this._instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setNetNode = function (e, t) {
    undefined === t && (t = 0);
    this._channels[t] = e;
  };
  _ctor.prototype.removeNetNode = function (e) {
    delete this._channels[e];
  };
  _ctor.prototype.connect = function (e, t) {
    undefined === t && (t = 0);
    return !!this._channels[t] && this._channels[t].connect(e);
  };
  _ctor.prototype.send = function (e, t, o) {
    undefined === t && (t = false);
    undefined === o && (o = 0);
    var i = this._channels[o];
    return !!i && i.send(e, t);
  };
  _ctor.prototype.request = function (e, t, o, i, n, r) {
    undefined === i && (i = true);
    undefined === n && (n = false);
    undefined === r && (r = 0);
    var a = this._channels[r];
    a && a.request(e, t, o, i, n);
  };
  _ctor.prototype.requestUnique = function (e, t, o, i, n, r) {
    undefined === i && (i = true);
    undefined === n && (n = false);
    undefined === r && (r = 0);
    var a = this._channels[r];
    return !!a && a.requestUnique(e, t, o, i, n);
  };
  _ctor.prototype.close = function (e, t, o) {
    undefined === o && (o = 0);
    if (this._channels[o]) {
      return this._channels[o].closeSocket(e, t);
    }
  };
  _ctor.prototype.setResponseHandler = function (e, t, o, i) {
    undefined === i && (i = 0);
    return !!this._channels[i] && this._channels[i].setResponseHandler(e, t, o);
  };
  _ctor.prototype.httpRequest = function (e, t, o, n) {
    undefined === n && (n = "json");
    return $2HttpClient.HttpClient.Request(e, t, o, n);
  };
  _ctor._instance = null;
  return _ctor;
}();
exports.NetManager = exp_NetManager;