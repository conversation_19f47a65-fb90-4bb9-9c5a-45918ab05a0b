var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2ListenID = require("ListenID");
var $2Notifier = require("Notifier");
var $2BaseSdk = require("BaseSdk");
cc.nativeAndroid = cc.nativeAndroid || {};
var cc_nativeAndroid = cc.nativeAndroid;
var u = window.jsb && jsb.reflection ? jsb.reflection.callStaticMethod : function () {};
var def_NativeAndroid = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.defaultClass = "org/cocos2dx/javascript/AppActivity";
    t.onbannerShow = function () {};
    t._shareCall = null;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.logout = function () {};
  _ctor.prototype.init = function (t) {
    var o = this;
    e.prototype.init.call(this, t);
    $2Notifier.Notifier.changeListener(true, $2ListenID.ListenID.Event_SendEvent, this.sendEvent, this);
    $2Notifier.Notifier.changeCall(true, $2CallID.CallID.Shop_GetProductList, this.getProductList, this);
    cc_nativeAndroid.bannerShow = function () {
      o.onbannerShow && o.onbannerShow();
    };
    cc_nativeAndroid.bannerShowerr = function () {};
    cc_nativeAndroid.videoClose = function () {
      o.onPlayEnd && o.onPlayEnd($2BaseSdk.VideoAdCode.NOT_COMPLITE, "未完整观看广告");
    };
    cc_nativeAndroid.videoFinish = function () {
      o.onPlayEnd && o.onPlayEnd($2BaseSdk.VideoAdCode.COMPLETE, "看完广告");
    };
    cc_nativeAndroid.videoError = function () {
      console.error("[WxAdCtrler][showVideoAD] error");
      o.onPlayEnd && o.onPlayEnd($2BaseSdk.VideoAdCode.AD_ERROR, "内容正在加载中，请稍后再试");
    };
    cc_nativeAndroid.rewardVideoSuccess = function () {
      o.onPlayEnd && o.onPlayEnd($2BaseSdk.VideoAdCode.SHOW_SUCCESS, "");
    };
    cc_nativeAndroid.fullVideoSuccess = function () {
      o.onFullPlayEnd && o.onFullPlayEnd($2BaseSdk.VideoAdCode.SHOW_SUCCESS, "");
    };
    cc_nativeAndroid.fullVideoHide = function () {
      o.onFullPlayEnd && o.onFullPlayEnd($2BaseSdk.VideoAdCode.COMPLETE, "");
    };
    cc_nativeAndroid.fullVideoError = function () {
      o.onFullPlayEnd && o.onFullPlayEnd($2BaseSdk.VideoAdCode.AD_ERROR, "");
    };
    cc_nativeAndroid.onPrivacyAccept = function () {
      o.sendEvent("confirm_privacy", "none");
      o._privacyCallback && o._privacyCallback(true);
    };
    cc_nativeAndroid.onPrivacyReject = function () {
      o._privacyCallback && o._privacyCallback(false);
    };
    cc_nativeAndroid.shareResult = function (e) {
      if (0 == e) {
        o._shareCall && o._shareCall(0);
      } else {
        o._shareCall && o._shareCall(1);
      }
    };
    cc_nativeAndroid.onPurchaseSuccess = function (e) {
      cc.game.emit("payFinish", 200, e);
    };
    cc_nativeAndroid.onPurchaseFail = function (e) {
      cc.game.emit("payFinish", 0, e);
    };
    cc_nativeAndroid.onPurchasedProductsFetched = function (e) {
      cc.log(e);
    };
    u(this.defaultClass, "checkPurchasesInApp", "()V");
  };
  _ctor.prototype.login = function (e) {
    return new Promise(function (t) {
      e && e(null);
      t(null);
    });
  };
  _ctor.prototype.showBannerWithNode = function (e, t, o) {
    this.showBannerWithStyle(e, {}, o);
  };
  _ctor.prototype.showBannerWithStyle = function (e, t, o) {
    this.onbannerShow = o;
  };
  _ctor.prototype.hideBanner = function () {};
  _ctor.prototype.destroyBanner = function () {};
  _ctor.prototype.showVideoAD = function (e, t) {
    this.onPlayEnd = t;
    u(this.defaultClass, "showVideo", "(Ljava/lang/String;)V", e);
  };
  _ctor.prototype.showFullVideoAD = function (e, t) {
    this.onFullPlayEnd = t;
    u(this.defaultClass, "showFullVideo", "(Ljava/lang/String;)V", e);
  };
  _ctor.prototype.sendEvent = function (e, t) {
    var o = e;
    null != t && "" != t && "none" != t || (t = "{}");
    if (!("reward_btn" == e && cc.sys.getNetworkType() == cc.sys.NetworkType.NONE)) {
      console.log("cc", "sendMsg", JSON.stringify(t));
      u(this.defaultClass, "sendMsg", "(Ljava/lang/String;Ljava/lang/String;)V", o, JSON.stringify(t));
    }
  };
  _ctor.prototype.vibrate = function (e) {
    undefined === e && (e = 0);
    u(this.defaultClass, "vibrate", "(I)V", 0 == e ? 10 : 300);
  };
  _ctor.prototype.share = function () {};
  _ctor.prototype.showInsertAd = function () {};
  _ctor.prototype.showSplashAd = function (e) {
    u(this.defaultClass, "showSplashAd", "(Ljava/lang/String;)V", e);
  };
  _ctor.prototype.showPrivacy = function (e) {
    this._privacyCallback = e;
    e && e(true);
  };
  _ctor.prototype.goRate = function () {
    u(this.defaultClass, "requestReview", "()V");
  };
  _ctor.prototype.showDebugAdView = function () {
    u(this.defaultClass, "debugAdView", "(Ljava/lang/String;)V", "test");
  };
  _ctor.prototype.toPay = function (e) {
    u(this.defaultClass, "purchase", "(Ljava/lang/String;)V", e.goodsId + "");
  };
  _ctor.prototype.toSubscribe = function (e) {
    u(this.defaultClass, "subscribe", "(Ljava/lang/String;)V", e.goodsId + "");
  };
  _ctor.prototype.getProductList = function () {
    var e = u(this.defaultClass, "getProductList", "()Ljava/lang/String;");
    console.log("[getProductList]" + typeof e);
    console.log(e);
    var t = "string" == typeof e ? JSON.parse(e) : e;
    $2Notifier.Notifier.send($2ListenID.ListenID.Shop_ShopItemList, t);
    return t;
  };
  _ctor.prototype.toShareFaceBook = function (e) {
    this._shareCall = e;
    u(this.defaultClass, "toShareFaceBook", "()V");
  };
  return _ctor;
}($2BaseSdk.BaseSdk);
exports.default = def_NativeAndroid;