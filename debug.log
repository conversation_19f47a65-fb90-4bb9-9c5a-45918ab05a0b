extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 222, braceCount: 0
Brace matching ended: braceCount=0, i=223
Extracted get body: var e;
      this._target || (this.target = null === (e = $2Notifier.Notifier.call($2CallID.CallID.F...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 248, initial braceCount: 1
Found '}' at 277, braceCount: 0
Brace matching ended: braceCount=0, i=278
Extracted set body: this._target = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 61, braceCount: 0
Brace matching ended: braceCount=0, i=62
Extracted get body: return this.game.packView;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.game.packView;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 61, braceCount: 0
Brace matching ended: braceCount=0, i=62
Extracted get body: return this.game.mainRole;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 62, braceCount: 0
Brace matching ended: braceCount=0, i=63
Extracted get body: return this.master.roleCfg;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.master.roleCfg;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 63, braceCount: 0
Brace matching ended: braceCount=0, i=64
Extracted get body: return this.master.equipCfg;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.master.equipCfg;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 63, braceCount: 0
Brace matching ended: braceCount=0, i=64
Extracted get body: return this.master.mergeCfg;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.master.mergeCfg;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 69, braceCount: 0
Brace matching ended: braceCount=0, i=70
Extracted get body: return this.heroHome.haedPosition;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.heroHome.haedPosition;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 69, braceCount: 0
Brace matching ended: braceCount=0, i=70
Extracted get body: return this.heroHome.bodyPosition;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.heroHome.bodyPosition;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 68, braceCount: 0
Brace matching ended: braceCount=0, i=69
Extracted get body: return this.master.extraProperty;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.master.extraProperty;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 60, braceCount: 0
Brace matching ended: braceCount=0, i=61
Extracted get body: return this.master.curHp;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 86, initial braceCount: 1
Found '}' at 120, braceCount: 0
Brace matching ended: braceCount=0, i=121
Extracted set body: this.master.curHp = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 75, braceCount: 0
Brace matching ended: braceCount=0, i=76
Extracted get body: return 0 == this.game.propSkilling.size;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return 0 == this.game.propSkilling.size;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 61, braceCount: 0
Brace matching ended: braceCount=0, i=62
Extracted get body: return this.game.packView;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.game.packView;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 96, braceCount: 2
Found '}' at 130, braceCount: 1
Found '{' at 137, braceCount: 2
Found '{' at 185, braceCount: 3
Found '}' at 250, braceCount: 2
Found '}' at 260, braceCount: 1
Found '}' at 266, braceCount: 0
Brace matching ended: braceCount=0, i=267
Extracted get body: var e;
      if (null === (e = this.packView) || undefined === e) {
        return undefined;
      ...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      if (null === (e = this.packView) || undefined === e) {
        return undefined;
      } else {
        return e.propList.filter(function (e) {
          ret
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._myData;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 81, initial braceCount: 1
Found '{' at 158, braceCount: 2
Found '{' at 428, braceCount: 3
Found '{' at 539, braceCount: 4
Found '}' at 605, braceCount: 3
Found '}' at 617, braceCount: 2
Found '}' at 629, braceCount: 1
Found '}' at 736, braceCount: 0
Brace matching ended: braceCount=0, i=737
Extracted set body: var t;
      var o = this;
      this._myData = e;
      if (e.spine) {
        this.mySkeleton = th...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 145, braceCount: 2
Found '}' at 182, braceCount: 1
Found '}' at 206, braceCount: 0
Brace matching ended: braceCount=0, i=207
Extracted get body: var e;
      var t = this._curHp;
      null === (e = this.equipList) || undefined === e || e.forEac...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      var t = this._curHp;
      null === (e = this.equipList) || undefined === e || e.forEach(function (e) {
        return t += e.curHp;
      });
      return t
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 154, braceCount: 2
Found '}' at 266, braceCount: 1
Found '}' at 290, braceCount: 0
Brace matching ended: braceCount=0, i=291
Extracted get body: var e;
      var t = this.property.cut.hp;
      null === (e = this.equipList) || undefined === e ||...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      var t = this.property.cut.hp;
      null === (e = this.equipList) || undefined === e || e.forEach(function (e) {
        var o;
        return t += null === 
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 169, braceCount: 0
Brace matching ended: braceCount=0, i=170
Extracted get body: return this.game.passType != $2MBackpackHero.MBPack.PassType.ChallengeCoin && this.reliveNum < $2Man...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 195, initial braceCount: 1
Found '}' at 229, braceCount: 0
Brace matching ended: braceCount=0, i=230
Extracted set body: this._isCanRelive = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 26, initial braceCount: 1
Found '}' at 64, braceCount: 0
Brace matching ended: braceCount=0, i=65
Extracted get body: return this._id;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
        get: function() {
            return this._id;
        },
        enumerable: false,
        configurable: true
    
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 26, initial braceCount: 1
Found '}' at 68, braceCount: 0
Brace matching ended: braceCount=0, i=69
Extracted get body: return this._isDead;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 97, initial braceCount: 1
Found '}' at 264, braceCount: 0
Brace matching ended: braceCount=0, i=265
Extracted set body: var t;
            this._isDead = e;
            this._rTime = 0;
            null === (t = this.col...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 26, initial braceCount: 1
Found '}' at 102, braceCount: 0
Brace matching ended: braceCount=0, i=103
Extracted get body: return this._isActive && !this.isDead && this.isValid;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 131, initial braceCount: 1
Found '}' at 172, braceCount: 0
Brace matching ended: braceCount=0, i=173
Extracted set body: this._isActive = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 26, initial braceCount: 1
Found '}' at 70, braceCount: 0
Brace matching ended: braceCount=0, i=71
Extracted get body: return this._campType;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 99, initial braceCount: 1
Found '}' at 195, braceCount: 0
Brace matching ended: braceCount=0, i=196
Extracted set body: this._campType = e;
            this.atkCamp = e == s.Two ? s.One : s.Two;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 26, initial braceCount: 1
Found '}' at 72, braceCount: 0
Brace matching ended: braceCount=0, i=73
Extracted get body: return this._entityType;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 101, initial braceCount: 1
Found '}' at 144, braceCount: 0
Brace matching ended: braceCount=0, i=145
Extracted set body: this._entityType = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 26, initial braceCount: 1
Found '}' at 68, braceCount: 0
Brace matching ended: braceCount=0, i=69
Extracted get body: return this._radius;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 97, initial braceCount: 1
Found '}' at 136, braceCount: 0
Brace matching ended: braceCount=0, i=137
Extracted set body: this._radius = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 26, initial braceCount: 1
Found '}' at 93, braceCount: 0
Brace matching ended: braceCount=0, i=94
Extracted get body: return this.position.add(this._haedPosition);...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
        get: function() {
            return this.position.add(this._haedPosition);
        },
        enumerable: false,
        configurable: true
    
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 26, initial braceCount: 1
Found '}' at 93, braceCount: 0
Brace matching ended: braceCount=0, i=94
Extracted get body: return this.position.add(this._bodyPosition);...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
        get: function() {
            return this.position.add(this._bodyPosition);
        },
        enumerable: false,
        configurable: true
    
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 26, initial braceCount: 1
Found '}' at 137, braceCount: 0
Brace matching ended: braceCount=0, i=138
Extracted get body: cc.Vec2.set(this._position, this.node.x, this.node.y);
            return this._position;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
        get: function() {
            cc.Vec2.set(this._position, this.node.x, this.node.y);
            return this._position;
        },
        enumerable: false,
        configurable: true
    
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 26, initial braceCount: 1
Found '}' at 71, braceCount: 0
Brace matching ended: braceCount=0, i=72
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
        get: function() {
            return $2Game.Game.mgr;
        },
        enumerable: false,
        configurable: true
    
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 26, initial braceCount: 1
Found '}' at 71, braceCount: 0
Brace matching ended: braceCount=0, i=72
Extracted get body: return this.node.scale;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
        get: function() {
            return this.node.scale;
        },
        enumerable: false,
        configurable: true
    
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 26, initial braceCount: 1
Found '}' at 71, braceCount: 0
Brace matching ended: braceCount=0, i=72
Extracted get body: return this.node.angle;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
        get: function() {
            return this.node.angle;
        },
        enumerable: false,
        configurable: true
    
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 26, initial braceCount: 1
Found '}' at 67, braceCount: 0
Brace matching ended: braceCount=0, i=68
Extracted get body: return this._isTag;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 96, initial braceCount: 1
Found '}' at 134, braceCount: 0
Brace matching ended: braceCount=0, i=135
Extracted set body: this._isTag = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 26, initial braceCount: 1
Found '}' at 57, braceCount: 0
Brace matching ended: braceCount=0, i=58
Extracted get body: return 1;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
        get: function() {
            return 1;
        },
        enumerable: false,
        configurable: true
    
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 26, initial braceCount: 1
Found '}' at 68, braceCount: 0
Brace matching ended: braceCount=0, i=69
Extracted get body: return this._horDir;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 97, initial braceCount: 1
Found '}' at 264, braceCount: 0
Brace matching ended: braceCount=0, i=265
Extracted set body: this._horDir = e;
            this.node && (this.node.children[0].scaleX = this._horDir * Math.abs(t...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 76, braceCount: 0
Brace matching ended: braceCount=0, i=77
Extracted get body: return $2BottomBarModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2BottomBarModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 61, braceCount: 0
Brace matching ended: braceCount=0, i=62
Extracted get body: return this.config.rarity;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.config.rarity;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=not found
propBody sample: 
    set: function (e) {
      this._onCollisionCall = e;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /get\s*:\s*function/: not found
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 24, initial braceCount: 1
Found '}' at 62, braceCount: 0
Brace matching ended: braceCount=0, i=63
Extracted set body: this._onCollisionCall = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 51, braceCount: 0
Brace matching ended: braceCount=0, i=52
Extracted get body: return this._vo;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this._vo;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 56, braceCount: 0
Brace matching ended: braceCount=0, i=57
Extracted get body: return this.vo.scale;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.vo.scale;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 53, braceCount: 0
Brace matching ended: braceCount=0, i=54
Extracted get body: return this._hurt;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 79, initial braceCount: 1
Found '}' at 131, braceCount: 0
Brace matching ended: braceCount=0, i=132
Extracted set body: this._hurt = null;
      this._hurt = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 57, braceCount: 0
Brace matching ended: braceCount=0, i=58
Extracted get body: return this._bulletId;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 83, initial braceCount: 1
Found '}' at 174, braceCount: 0
Brace matching ended: braceCount=0, i=175
Extracted set body: this._bulletId = e;
      this.cfg = $2Cfg.Cfg.BulletEffect.get(this.bulletId);...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 49, braceCount: 2
Found '}' at 114, braceCount: 1
Found '{' at 121, braceCount: 2
Found '}' at 199, braceCount: 1
Found '}' at 205, braceCount: 0
Brace matching ended: braceCount=0, i=206
Extracted get body: if (this.bulletId) {
        return "entity/fight/Bullet/" + this.cfg.prefab;
      } else {
       ...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      if (this.bulletId) {
        return "entity/fight/Bullet/" + this.cfg.prefab;
      } else {
        return "entity/fight/Bullet/" + this.belongSkill.skillMainID;
      }
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 62, braceCount: 0
Brace matching ended: braceCount=0, i=63
Extracted get body: return this.cfg.soundhitId;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.cfg.soundhitId;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._belong;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 81, initial braceCount: 1
Found '}' at 110, braceCount: 0
Brace matching ended: braceCount=0, i=111
Extracted set body: this._belong = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 99, braceCount: 2
Found '}' at 133, braceCount: 1
Found '{' at 140, braceCount: 2
Found '}' at 172, braceCount: 1
Found '}' at 178, braceCount: 0
Brace matching ended: braceCount=0, i=179
Extracted get body: var e;
      if (null === (e = this.belongSkill) || undefined === e) {
        return undefined;
   ...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      if (null === (e = this.belongSkill) || undefined === e) {
        return undefined;
      } else {
        return e.cutVo;
      }
    },
    enumerable: fal
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 99, braceCount: 2
Found '}' at 133, braceCount: 1
Found '{' at 140, braceCount: 2
Found '}' at 177, braceCount: 1
Found '}' at 183, braceCount: 0
Brace matching ended: braceCount=0, i=184
Extracted get body: var e;
      if (null === (e = this.belongSkill) || undefined === e) {
        return undefined;
   ...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      if (null === (e = this.belongSkill) || undefined === e) {
        return undefined;
      } else {
        return e.getOwner();
      }
    },
    enumerable
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 57, braceCount: 0
Brace matching ended: braceCount=0, i=58
Extracted get body: return this._campType;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 83, initial braceCount: 1
Found '{' at 194, braceCount: 2
Found '}' at 365, braceCount: 1
Found '{' at 403, braceCount: 2
Found '}' at 439, braceCount: 1
Found '{' at 446, braceCount: 2
Found '}' at 567, braceCount: 1
Found '}' at 573, braceCount: 0
Brace matching ended: braceCount=0, i=574
Extracted set body: this.ignore && (this.ignore.length = 0);
      this._campType = e;
      if (3 == this.skillCfg.obje...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 60, braceCount: 0
Brace matching ended: braceCount=0, i=61
Extracted get body: return this.rText.string;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 86, initial braceCount: 1
Found '{' at 279, braceCount: 2
Found '{' at 394, braceCount: 3
Found '}' at 448, braceCount: 2
Found '{' at 480, braceCount: 3
Found '}' at 531, braceCount: 2
Found '}' at 541, braceCount: 1
Found '{' at 653, braceCount: 2
Found '}' at 678, braceCount: 1
Found '{' at 681, braceCount: 2
Found '}' at 723, braceCount: 1
Found '{' at 742, braceCount: 2
Found '}' at 767, braceCount: 1
Found '{' at 787, braceCount: 2
Found '}' at 815, braceCount: 1
Found '}' at 831, braceCount: 0
Brace matching ended: braceCount=0, i=832
Extracted set body: var t = this;
      this.node.scale = 0;
      var o = $2LanguageFun.LanguageFun.check(e);
      thi...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 61, braceCount: 0
Brace matching ended: braceCount=0, i=62
Extracted get body: return this.game.mainRole;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 57, braceCount: 0
Brace matching ended: braceCount=0, i=58
Extracted get body: return this._maxSpeed;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 83, initial braceCount: 1
Found '{' at 132, braceCount: 2
Found '}' at 161, braceCount: 1
Found '}' at 177, braceCount: 0
Brace matching ended: braceCount=0, i=178
Extracted set body: $2Game.Game.tween(this).stopLast().to(.5, {
        _maxSpeed: e
      }).start();...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 60, braceCount: 0
Brace matching ended: braceCount=0, i=61
Extracted get body: return this.monCfg.Scale;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.monCfg.Scale;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 137, braceCount: 0
Brace matching ended: braceCount=0, i=138
Extracted get body: return M.setVal(this.footprint[this.footprint.length - 2], this.footprint[this.footprint.length - 1]...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return M.setVal(this.footprint[this.footprint.length - 2], this.footprint[this.footprint.length - 1]);
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 56, braceCount: 2
Found '}' at 113, braceCount: 1
Found '{' at 120, braceCount: 2
Found '}' at 160, braceCount: 1
Found '}' at 166, braceCount: 0
Brace matching ended: braceCount=0, i=167
Extracted get body: if (this.bodyList.length) {
        return this.bodyList.lastVal.position.y;
      } else {
        ...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      if (this.bodyList.length) {
        return this.bodyList.lastVal.position.y;
      } else {
        return this.position.y;
      }
    },
    enumerable: false,
    conf
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 77, braceCount: 0
Brace matching ended: braceCount=0, i=78
Extracted get body: return $2ModeChainsModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 44, braceCount: 0
Brace matching ended: braceCount=0, i=45
Extracted get body: return 1;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return 1;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 67, braceCount: 0
Brace matching ended: braceCount=0, i=68
Extracted get body: return this.owerChains.jointNum;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.owerChains.jointNum;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 63, braceCount: 0
Brace matching ended: braceCount=0, i=64
Extracted get body: return this.itemList.length;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.itemList.length;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 57, braceCount: 0
Brace matching ended: braceCount=0, i=58
Extracted get body: return this._curIndex;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 83, initial braceCount: 1
Found '}' at 176, braceCount: 0
Brace matching ended: braceCount=0, i=177
Extracted set body: e > this._curIndex && (this.oldIndex = this._curIndex);
      this._curIndex = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 166, braceCount: 0
Brace matching ended: braceCount=0, i=167
Extracted get body: var e;
      return (null === (e = this.itemList[this.middleIndex]) || undefined === e ? undefined :...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      return (null === (e = this.itemList[this.middleIndex]) || undefined === e ? undefined : e.position) || cc.v2(0, 9999);
    },
    enumerable: false,
    conf
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 56, braceCount: 0
Brace matching ended: braceCount=0, i=57
Extracted get body: return this.position;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.position;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 56, braceCount: 0
Brace matching ended: braceCount=0, i=57
Extracted get body: return this.position;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.position;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 154, braceCount: 0
Brace matching ended: braceCount=0, i=155
Extracted get body: var e;
      return this.curIndex < 0 || (null === (e = this.buffMgr) || undefined === e ? undefined...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      return this.curIndex < 0 || (null === (e = this.buffMgr) || undefined === e ? undefined : e.isInvincible);
    },
    enumerable: false,
    configurable: tr
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 67, braceCount: 0
Brace matching ended: braceCount=0, i=68
Extracted get body: return this.owerChains.maxSpeed;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 93, initial braceCount: 1
Found '}' at 124, braceCount: 0
Brace matching ended: braceCount=0, i=125
Extracted set body: this._maxSpeed = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 71, braceCount: 0
Brace matching ended: braceCount=0, i=72
Extracted get body: return $2FCollider.ColliderType.Box;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2FCollider.ColliderType.Box;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 53, braceCount: 0
Brace matching ended: braceCount=0, i=54
Extracted get body: return this._size;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 79, initial braceCount: 1
Found '}' at 191, braceCount: 0
Brace matching ended: braceCount=0, i=192
Extracted set body: this._size.width = e.width < 0 ? 0 : e.width;
      this._size.height = e.height < 0 ? 0 : e.height;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 74, braceCount: 0
Brace matching ended: braceCount=0, i=75
Extracted get body: return $2FCollider.ColliderType.Circle;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2FCollider.ColliderType.Circle;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._radius;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 81, initial braceCount: 1
Found '}' at 122, braceCount: 0
Brace matching ended: braceCount=0, i=123
Extracted set body: this._radius = e < 0 ? 0 : e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 48, braceCount: 0
Brace matching ended: braceCount=0, i=49
Extracted get body: return a.Box;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return a.Box;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 61, braceCount: 0
Brace matching ended: braceCount=0, i=62
Extracted get body: return this.node.position;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.node.position;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 53, braceCount: 0
Brace matching ended: braceCount=0, i=54
Extracted get body: return this._comp;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this._comp;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 54, braceCount: 0
Brace matching ended: braceCount=0, i=55
Extracted get body: return this.aabb.x;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.aabb.x;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 54, braceCount: 0
Brace matching ended: braceCount=0, i=55
Extracted get body: return this.aabb.y;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.aabb.y;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return this.aabb.width;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.aabb.width;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 59, braceCount: 0
Brace matching ended: braceCount=0, i=60
Extracted get body: return this.aabb.height;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.aabb.height;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._offset;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 81, initial braceCount: 1
Found '}' at 110, braceCount: 0
Brace matching ended: braceCount=0, i=111
Extracted set body: this._offset = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 61, braceCount: 0
Brace matching ended: braceCount=0, i=62
Extracted get body: return this.game.mainRole;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 75, braceCount: 0
Brace matching ended: braceCount=0, i=76
Extracted get body: return $2FCollider.ColliderType.Polygon;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2FCollider.ColliderType.Polygon;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._points;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 81, initial braceCount: 1
Found '}' at 110, braceCount: 0
Brace matching ended: braceCount=0, i=111
Extracted set body: this._points = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 113, braceCount: 0
Brace matching ended: braceCount=0, i=114
Extracted get body: this._target || (this.target = this.game.mainRole);
      return this._target;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 139, initial braceCount: 1
Found '}' at 168, braceCount: 0
Brace matching ended: braceCount=0, i=169
Extracted set body: this._target = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 151, braceCount: 0
Brace matching ended: braceCount=0, i=152
Extracted get body: var e = cc.Vec2.distance(this.target.position, this.position);
      this._checkTime = e / 100 * .1;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e = cc.Vec2.distance(this.target.position, this.position);
      this._checkTime = e / 100 * .1;
      return e;
    },
    enumerable: false,
    configurable: true

Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 53, braceCount: 0
Brace matching ended: braceCount=0, i=54
Extracted get body: return this._data;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 79, initial braceCount: 1
Found '{' at 457, braceCount: 2
Found '{' at 736, braceCount: 3
Found '}' at 779, braceCount: 2
Found '{' at 821, braceCount: 3
Found '}' at 989, braceCount: 2
Found '}' at 999, braceCount: 1
Found '{' at 1006, braceCount: 2
Found '}' at 1198, braceCount: 1
Found '}' at 1204, braceCount: 0
Brace matching ended: braceCount=0, i=1205
Extracted set body: var t;
      var o;
      this._data = e;
      this.bg || (this.bg = this.node.getComByChild(cc.Spr...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 92, braceCount: 0
Brace matching ended: braceCount=0, i=93
Extracted get body: return Math.trunc(this.myKnapsackMgr.getVal(this.Ename));...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return Math.trunc(this.myKnapsackMgr.getVal(this.Ename));
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 66, braceCount: 0
Brace matching ended: braceCount=0, i=67
Extracted get body: return this.node.worldPosition;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.node.worldPosition;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 57, braceCount: 0
Brace matching ended: braceCount=0, i=58
Extracted get body: return this.data_list;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.data_list;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 54, braceCount: 0
Brace matching ended: braceCount=0, i=55
Extracted get body: return this._param;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 80, initial braceCount: 1
Found '}' at 108, braceCount: 0
Brace matching ended: braceCount=0, i=109
Extracted set body: this._param = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._target;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this._target;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 60, braceCount: 0
Brace matching ended: braceCount=0, i=61
Extracted get body: return this.label.string;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 86, initial braceCount: 1
Found '}' at 138, braceCount: 0
Brace matching ended: braceCount=0, i=139
Extracted set body: this.isValid && (this.label.string = e);...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 59, braceCount: 0
Brace matching ended: braceCount=0, i=60
Extracted get body: return this.equipcfg.id;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.equipcfg.id;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 128, braceCount: 2
Found '}' at 162, braceCount: 1
Found '{' at 169, braceCount: 2
Found '}' at 204, braceCount: 1
Found '}' at 210, braceCount: 0
Brace matching ended: braceCount=0, i=211
Extracted get body: var e;
      if (null === (e = this.mode.userEquipPack.getItem(this.equipID)) || undefined === e) {
...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      if (null === (e = this.mode.userEquipPack.getItem(this.equipID)) || undefined === e) {
        return undefined;
      } else {
        return e.isFitOut;
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 113, braceCount: 0
Brace matching ended: braceCount=0, i=114
Extracted get body: return $2UIManager.UIManager.getView("ui/ModeBackpackHero/M20_PrePare_Equip");...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2UIManager.UIManager.getView("ui/ModeBackpackHero/M20_PrePare_Equip");
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 92, braceCount: 2
Found '}' at 126, braceCount: 1
Found '{' at 133, braceCount: 2
Found '}' at 168, braceCount: 1
Found '}' at 174, braceCount: 0
Brace matching ended: braceCount=0, i=175
Extracted get body: var e;
      if (null === (e = this.game) || undefined === e) {
        return undefined;
      } el...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      if (null === (e = this.game) || undefined === e) {
        return undefined;
      } else {
        return e.packView;
      }
    },
    enumerable: false,

Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 61, braceCount: 0
Brace matching ended: braceCount=0, i=62
Extracted get body: return this.game.mainRole;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return this.roleCfg.id;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.roleCfg.id;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 37, braceCount: 2
Found '}' at 91, braceCount: 1
Found '}' at 98, braceCount: 0
Brace matching ended: braceCount=0, i=99
Extracted get body: return {
        Atk: 0,
        Hp: 0,
        Def: 0
      };...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return {
        Atk: 0,
        Hp: 0,
        Def: 0
      };
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 37, braceCount: 2
Found '}' at 91, braceCount: 1
Found '}' at 98, braceCount: 0
Brace matching ended: braceCount=0, i=99
Extracted get body: return {
        Atk: 0,
        Hp: 0,
        Def: 0
      };...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return {
        Atk: 0,
        Hp: 0,
        Def: 0
      };
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 47, braceCount: 0
Brace matching ended: braceCount=0, i=48
Extracted get body: return null;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return null;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 47, braceCount: 0
Brace matching ended: braceCount=0, i=48
Extracted get body: return null;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return null;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 104, braceCount: 2
Found '}' at 159, braceCount: 1
Found '}' at 183, braceCount: 0
Brace matching ended: braceCount=0, i=184
Extracted get body: var e = this;
      var t = [];
      this.pointList.forEach(function (o) {
        return t.push(o....
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e = this;
      var t = [];
      this.pointList.forEach(function (o) {
        return t.push(o.add(e.node.position));
      });
      return t;
    },
    enumerable
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 104, braceCount: 2
Found '}' at 159, braceCount: 1
Found '}' at 183, braceCount: 0
Brace matching ended: braceCount=0, i=184
Extracted get body: var e = this;
      var t = [];
      this.pointList.forEach(function (o) {
        return t.push(o....
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e = this;
      var t = [];
      this.pointList.forEach(function (o) {
        return t.push(o.add(e.node.position));
      });
      return t;
    },
    enumerable
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 69, braceCount: 0
Brace matching ended: braceCount=0, i=70
Extracted get body: return this._preparationBlockList;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 95, initial braceCount: 1
Found '}' at 215, braceCount: 0
Brace matching ended: braceCount=0, i=216
Extracted set body: var t;
      this._preparationBlockList.length = 0;
      (t = this._preparationBlockList).push.appl...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 37, braceCount: 2
Found '}' at 162, braceCount: 1
Found '}' at 169, braceCount: 0
Brace matching ended: braceCount=0, i=170
Extracted get body: return {
        id: this.mergeCfg.id,
        pos: this.position,
        curHp: this.curHp,
      ...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return {
        id: this.mergeCfg.id,
        pos: this.position,
        curHp: this.curHp,
        isUnlock: this.isUnlock
      };
    },
    enumerable: false,
    c
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 99, braceCount: 0
Brace matching ended: braceCount=0, i=100
Extracted get body: return $2MBackpackHero.MBPack.equipWeapon.includes(this.roleID);...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2MBackpackHero.MBPack.equipWeapon.includes(this.roleID);
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 98, braceCount: 0
Brace matching ended: braceCount=0, i=99
Extracted get body: return $2MBackpackHero.MBPack.equipArmor.includes(this.roleID);...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2MBackpackHero.MBPack.equipArmor.includes(this.roleID);
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 84, braceCount: 0
Brace matching ended: braceCount=0, i=85
Extracted get body: return this.gemStones.length >= this.mergeCfg.lv;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.gemStones.length >= this.mergeCfg.lv;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 96, braceCount: 0
Brace matching ended: braceCount=0, i=97
Extracted get body: return (this.mergeCfg.skill || this.equipCfg.skill || [])[0];...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return (this.mergeCfg.skill || this.equipCfg.skill || [])[0];
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 37, braceCount: 2
Found '}' at 303, braceCount: 1
Found '}' at 310, braceCount: 0
Brace matching ended: braceCount=0, i=311
Extracted get body: return {
        Atk: this.firstSkill ? this.property.cut.atk / this.firstSkill.cutVo.cd : 0,
      ...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return {
        Atk: this.firstSkill ? this.property.cut.atk / this.firstSkill.cutVo.cd : 0,
        Hp: this.firstSkill ? this.extraProperty.heal / this.firstSkill.cutV
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 48, braceCount: 2
Found '}' at 88, braceCount: 1
Found '{' at 223, braceCount: 2
Found '}' at 439, braceCount: 1
Found '}' at 446, braceCount: 0
Brace matching ended: braceCount=0, i=447
Extracted get body: if (!this.nextLv) {
        return this.figureData;
      }
      var e = this.getPropertyCfg(this.e...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      if (!this.nextLv) {
        return this.figureData;
      }
      var e = this.getPropertyCfg(this.equipCfg, this.nextLv);
      var t = e.property;
      var o = e.extra
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 65, braceCount: 2
Found '}' at 146, braceCount: 1
Found '}' at 154, braceCount: 0
Brace matching ended: braceCount=0, i=155
Extracted get body: return $2Cfg.Cfg.EquipMergeLv.find({
        equipId: this.mergeCfg.equipId,
        lv: this.mergeC...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Cfg.Cfg.EquipMergeLv.find({
        equipId: this.mergeCfg.equipId,
        lv: this.mergeCfg.lv + 1
      });
    },
    enumerable: false,
    configurable: tr
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return !this.nextLv;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return !this.nextLv;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 76, braceCount: 2
Found '{' at 247, braceCount: 3
Found '}' at 285, braceCount: 2
Found '{' at 432, braceCount: 3
Found '}' at 471, braceCount: 2
Found '{' at 590, braceCount: 3
Found '}' at 623, braceCount: 2
Found '{' at 652, braceCount: 3
Found '}' at 749, braceCount: 2
Found '}' at 800, braceCount: 1
Found '}' at 807, braceCount: 0
Brace matching ended: braceCount=0, i=808
Extracted get body: var e;
      var t;
      var o;
      return {
        id: this.mergeCfg.id,
        pos: this.posi...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      var t;
      var o;
      return {
        id: this.mergeCfg.id,
        pos: this.position,
        curHp: this.curHp,
        isUnlock: this.isUnlock,
    
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 157, braceCount: 0
Brace matching ended: braceCount=0, i=158
Extracted get body: var e;
      return (null === (e = this.gemData) || undefined === e ? undefined : e.MagicallyChangeI...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      return (null === (e = this.gemData) || undefined === e ? undefined : e.MagicallyChangeID) || this.roleCfg.id;
    },
    enumerable: false,
    configurable:
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 66, braceCount: 2
Found '}' at 130, braceCount: 1
Found '{' at 137, braceCount: 2
Found '}' at 175, braceCount: 1
Found '}' at 181, braceCount: 0
Brace matching ended: braceCount=0, i=182
Extracted get body: if (this.gemData.MagicallyChangeID) {
        return $2Cfg.Cfg.EquipMergeLv.get(this.roleID);
      ...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      if (this.gemData.MagicallyChangeID) {
        return $2Cfg.Cfg.EquipMergeLv.get(this.roleID);
      } else {
        return this.mergeCfg;
      }
    },
    enumerable: 
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 121, braceCount: 2
Found '{' at 226, braceCount: 3
Found '{' at 316, braceCount: 4
Found '}' at 411, braceCount: 3
Found '}' at 471, braceCount: 2
Found '}' at 481, braceCount: 1
Found '}' at 487, braceCount: 0
Brace matching ended: braceCount=0, i=488
Extracted get body: var e = this;
      if (this.roleID == $2MBackpackHero.MBPack.ReactionType.GreedyGemstone) {
       ...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e = this;
      if (this.roleID == $2MBackpackHero.MBPack.ReactionType.GreedyGemstone) {
        return cc__spreadArrays(this.packView.allList, [this.packView.selectP
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 37, braceCount: 2
Found '}' at 193, braceCount: 1
Found '}' at 200, braceCount: 0
Brace matching ended: braceCount=0, i=201
Extracted get body: return {
        id: this.mergeCfg.id,
        pos: this.position,
        curHp: this.curHp,
      ...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return {
        id: this.mergeCfg.id,
        pos: this.position,
        curHp: this.curHp,
        isUnlock: this.isUnlock,
        gemData: this.gemData
      };
    
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 77, braceCount: 0
Brace matching ended: braceCount=0, i=78
Extracted get body: return $2ModeChainsModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 81, braceCount: 2
Found '}' at 116, braceCount: 1
Found '{' at 140, braceCount: 2
Found '}' at 180, braceCount: 1
Found '}' at 188, braceCount: 0
Brace matching ended: braceCount=0, i=189
Extracted get body: return this.mode.userEquipPack.filter(function (e) {
        return e.isFitOut;
      }).sort(functi...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.mode.userEquipPack.filter(function (e) {
        return e.isFitOut;
      }).sort(function (e, t) {
        return e.sort - t.sort;
      });
    },
    enume
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 56, braceCount: 0
Brace matching ended: braceCount=0, i=57
Extracted get body: return this.mode.rVo;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.mode.rVo;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 87, braceCount: 0
Brace matching ended: braceCount=0, i=88
Extracted get body: return this.mode.treemap && this.mode.treemap.fight;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.mode.treemap && this.mode.treemap.fight;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 87, braceCount: 0
Brace matching ended: braceCount=0, i=88
Extracted get body: return this.mode.fightinfopack.getVal("boxlv") || 1;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.mode.fightinfopack.getVal("boxlv") || 1;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 83, braceCount: 0
Brace matching ended: braceCount=0, i=84
Extracted get body: return $2ModeBackpackHeroModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 77, braceCount: 0
Brace matching ended: braceCount=0, i=78
Extracted get body: return $2ModeChainsModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 63, braceCount: 0
Brace matching ended: braceCount=0, i=64
Extracted get body: return this._openArgs.param;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this._openArgs.param;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 63, braceCount: 0
Brace matching ended: braceCount=0, i=64
Extracted get body: return this.param.type || 1;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.param.type || 1;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 77, braceCount: 0
Brace matching ended: braceCount=0, i=78
Extracted get body: return this.game.rVo.poolADMap[this.type];...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.game.rVo.poolADMap[this.type];
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 230, braceCount: 0
Brace matching ended: braceCount=0, i=231
Extracted get body: var e;
      var t;
      return (null === (e = this.poolData) || undefined === e ? undefined : e.fr...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      var t;
      return (null === (e = this.poolData) || undefined === e ? undefined : e.freeResetNum) || (null === (t = this.poolData) || undefined === t ? unde
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 103, braceCount: 0
Brace matching ended: braceCount=0, i=104
Extracted get body: return this.game.recordVo.defaultData.poolADMap[this.type].resetNum;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.game.recordVo.defaultData.poolADMap[this.type].resetNum;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 133, braceCount: 0
Brace matching ended: braceCount=0, i=134
Extracted get body: var e;
      return (null === (e = this.poolData) || undefined === e ? undefined : e.getAll) || 0;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      return (null === (e = this.poolData) || undefined === e ? undefined : e.getAll) || 0;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 101, braceCount: 0
Brace matching ended: braceCount=0, i=102
Extracted get body: return this.game.recordVo.defaultData.poolADMap[this.type].getAll;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.game.recordVo.defaultData.poolADMap[this.type].getAll;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 61, braceCount: 0
Brace matching ended: braceCount=0, i=62
Extracted get body: return this.game.mainRole;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 77, braceCount: 0
Brace matching ended: braceCount=0, i=78
Extracted get body: return $2ModeChainsModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 92, braceCount: 2
Found '}' at 126, braceCount: 1
Found '{' at 133, braceCount: 2
Found '}' at 168, braceCount: 1
Found '}' at 174, braceCount: 0
Brace matching ended: braceCount=0, i=175
Extracted get body: var e;
      if (null === (e = this.game) || undefined === e) {
        return undefined;
      } el...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      if (null === (e = this.game) || undefined === e) {
        return undefined;
      } else {
        return e.mainRole;
      }
    },
    enumerable: false,

Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 77, braceCount: 0
Brace matching ended: braceCount=0, i=78
Extracted get body: return $2ModeChainsModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 77, braceCount: 0
Brace matching ended: braceCount=0, i=78
Extracted get body: return $2ModeChainsModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 77, braceCount: 0
Brace matching ended: braceCount=0, i=78
Extracted get body: return $2ModeChainsModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 105, braceCount: 0
Brace matching ended: braceCount=0, i=106
Extracted get body: return $2UIManager.UIManager.getView("ui/ModeChains/M33_FightUIView");...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2UIManager.UIManager.getView("ui/ModeChains/M33_FightUIView");
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 85, braceCount: 0
Brace matching ended: braceCount=0, i=86
Extracted get body: return $2ModeBulletsReboundModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeBulletsReboundModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._myData;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 81, initial braceCount: 1
Found '{' at 158, braceCount: 2
Found '{' at 430, braceCount: 3
Found '{' at 542, braceCount: 4
Found '}' at 608, braceCount: 3
Found '}' at 620, braceCount: 2
Found '}' at 632, braceCount: 1
Found '}' at 739, braceCount: 0
Brace matching ended: braceCount=0, i=740
Extracted set body: var t;
      var o = this;
      this._myData = e;
      if (e.spine) {
        this.mySkeleton = th...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 63, braceCount: 2
Found '}' at 106, braceCount: 1
Found '}' at 126, braceCount: 0
Brace matching ended: braceCount=0, i=127
Extracted get body: return $2Cfg.Cfg.BagSkill.filter({
        id: this.myData.startSkill
      })[0].bulletId;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Cfg.Cfg.BagSkill.filter({
        id: this.myData.startSkill
      })[0].bulletId;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 77, braceCount: 0
Brace matching ended: braceCount=0, i=78
Extracted get body: return $2ModeChainsModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 77, braceCount: 0
Brace matching ended: braceCount=0, i=78
Extracted get body: return $2ModeChainsModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 67, braceCount: 0
Brace matching ended: braceCount=0, i=68
Extracted get body: return this.property.base.Scale;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.property.base.Scale;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 67, braceCount: 0
Brace matching ended: braceCount=0, i=68
Extracted get body: return this.property.base.Scale;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.property.base.Scale;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 117, braceCount: 0
Brace matching ended: braceCount=0, i=118
Extracted get body: return this.game.bronMonsterMgr.cutStatus == $2MChains.MChains.RoundStatus.BATTLE;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.game.bronMonsterMgr.cutStatus == $2MChains.MChains.RoundStatus.BATTLE;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._myData;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 81, initial braceCount: 1
Found '{' at 161, braceCount: 2
Found '{' at 437, braceCount: 3
Found '{' at 552, braceCount: 4
Found '}' at 617, braceCount: 3
Found '{' at 660, braceCount: 4
Found '}' at 732, braceCount: 3
Found '{' at 969, braceCount: 4
Found '}' at 1084, braceCount: 3
Found '{' at 1091, braceCount: 4
Found '}' at 1154, braceCount: 3
Found '}' at 1167, braceCount: 2
Found '}' at 1182, braceCount: 1
Found '}' at 1289, braceCount: 0
Brace matching ended: braceCount=0, i=1290
Extracted set body: var t;
      var o = this;
      this._myData = e;
      // if (e.spine) {
      //   this.mySkeleto...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 96, braceCount: 0
Brace matching ended: braceCount=0, i=97
Extracted get body: return !this.game.passParam.isHighDiff && this.reliveNum > 0;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 122, initial braceCount: 1
Found '}' at 156, braceCount: 0
Brace matching ended: braceCount=0, i=157
Extracted set body: this._isCanRelive = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 102, braceCount: 2
Found '}' at 163, braceCount: 1
Found '{' at 170, braceCount: 2
Found '}' at 243, braceCount: 1
Found '}' at 249, braceCount: 0
Brace matching ended: braceCount=0, i=250
Extracted get body: if (this.game.passType == $2MChains.MChains.PassType.ForwardMoveExtend) {
        // return "attack"...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      if (this.game.passType == $2MChains.MChains.PassType.ForwardMoveExtend) {
        // return "attack";
        return "gongji";
      } else {
        return $2GameUtil.Ga
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._horDir;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 81, initial braceCount: 1
Found '}' at 110, braceCount: 0
Brace matching ended: braceCount=0, i=111
Extracted set body: this._horDir = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._myData;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 81, initial braceCount: 1
Found '{' at 158, braceCount: 2
Found '{' at 430, braceCount: 3
Found '{' at 541, braceCount: 4
Found '}' at 607, braceCount: 3
Found '}' at 619, braceCount: 2
Found '}' at 631, braceCount: 1
Found '}' at 738, braceCount: 0
Brace matching ended: braceCount=0, i=739
Extracted set body: var t;
      var o = this;
      this._myData = e;
      if (e.spine) {
        this.mySkeleton = th...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 63, braceCount: 2
Found '}' at 106, braceCount: 1
Found '}' at 126, braceCount: 0
Brace matching ended: braceCount=0, i=127
Extracted get body: return $2Cfg.Cfg.BagSkill.filter({
        id: this.myData.startSkill
      })[0].bulletId;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Cfg.Cfg.BagSkill.filter({
        id: this.myData.startSkill
      })[0].bulletId;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 77, braceCount: 0
Brace matching ended: braceCount=0, i=78
Extracted get body: return $2TideDefendModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2TideDefendModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return this._monsterId;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 84, initial braceCount: 1
Found '{' at 207, braceCount: 2
Found '}' at 269, braceCount: 1
Found '{' at 276, braceCount: 2
Found '}' at 323, braceCount: 1
Found '}' at 366, braceCount: 0
Brace matching ended: braceCount=0, i=367
Extracted set body: var t = this._monsterId != e;
      this._monsterId = e;
      this.monCfg = $2Cfg.Cfg.Monster.get(e...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 60, braceCount: 0
Brace matching ended: braceCount=0, i=61
Extracted get body: return this.monCfg.Scale;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.monCfg.Scale;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 194, braceCount: 0
Brace matching ended: braceCount=0, i=195
Extracted get body: var e;
      return $2GameUtil.GameUtil.getDistance(this.node.position, null === (e = this.steering....
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      return $2GameUtil.GameUtil.getDistance(this.node.position, null === (e = this.steering.targetAgent1) || undefined === e ? undefined : e.position);
    },
   
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 77, braceCount: 0
Brace matching ended: braceCount=0, i=78
Extracted get body: return $2TideDefendModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2TideDefendModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 66, braceCount: 0
Brace matching ended: braceCount=0, i=67
Extracted get body: return this.lvCfg.dropExpRatio;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.lvCfg.dropExpRatio;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 64, braceCount: 0
Brace matching ended: braceCount=0, i=65
Extracted get body: return this.lvCfg.bronMatrix;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.lvCfg.bronMatrix;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 77, braceCount: 0
Brace matching ended: braceCount=0, i=78
Extracted get body: return $2TideDefendModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2TideDefendModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 140, braceCount: 0
Brace matching ended: braceCount=0, i=141
Extracted get body: var e;
      return (null === (e = this.property) || undefined === e ? undefined : e.cut.Picking) ||...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      return (null === (e = this.property) || undefined === e ? undefined : e.cut.Picking) || 200;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 63, braceCount: 0
Brace matching ended: braceCount=0, i=64
Extracted get body: return this.property.cut.hp;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.property.cut.hp;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 57, braceCount: 0
Brace matching ended: braceCount=0, i=58
Extracted get body: return this._velocity;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 83, initial braceCount: 1
Found '}' at 117, braceCount: 0
Brace matching ended: braceCount=0, i=118
Extracted set body: this._velocity.set(e);...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 53, braceCount: 0
Brace matching ended: braceCount=0, i=54
Extracted get body: return this._side;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 79, initial braceCount: 1
Found '}' at 106, braceCount: 0
Brace matching ended: braceCount=0, i=107
Extracted set body: this._side = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 56, braceCount: 0
Brace matching ended: braceCount=0, i=57
Extracted get body: return this._heading;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 82, initial braceCount: 1
Found '{' at 112, braceCount: 2
Found '}' at 175, braceCount: 1
Found '{' at 182, braceCount: 2
Found '{' at 288, braceCount: 3
Found '}' at 447, braceCount: 2
Found '}' at 455, braceCount: 1
Found '}' at 461, braceCount: 0
Brace matching ended: braceCount=0, i=462
Extracted set body: if (e.magSqr() < 1e-5) {
        console.error("new heading is zero", this.ID);
      } else {
     ...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 57, braceCount: 0
Brace matching ended: braceCount=0, i=58
Extracted get body: return this._maxSpeed;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 83, initial braceCount: 1
Found '}' at 114, braceCount: 0
Brace matching ended: braceCount=0, i=115
Extracted set body: this._maxSpeed = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 57, braceCount: 0
Brace matching ended: braceCount=0, i=58
Extracted get body: return this._maxForce;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 83, initial braceCount: 1
Found '}' at 114, braceCount: 0
Brace matching ended: braceCount=0, i=115
Extracted set body: this._maxForce = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 116, braceCount: 2
Found '}' at 150, braceCount: 1
Found '{' at 157, braceCount: 2
Found '}' at 192, braceCount: 1
Found '}' at 198, braceCount: 0
Brace matching ended: braceCount=0, i=199
Extracted get body: var e;
      if (null === (e = $2Game.Game.Mgr.instance.mainRole) || undefined === e) {
        retu...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      if (null === (e = $2Game.Game.Mgr.instance.mainRole) || undefined === e) {
        return undefined;
      } else {
        return e.position;
      }
    },
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return this._moveSpeed;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 84, initial braceCount: 1
Found '}' at 177, braceCount: 0
Brace matching ended: braceCount=0, i=178
Extracted set body: f && (e = cc.Vec2.ZERO);
      this._moveSpeed = e;
      this.FlushProperties();...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 45, braceCount: 0
Brace matching ended: braceCount=0, i=46
Extracted get body: return .5;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return .5;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._myData;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 81, initial braceCount: 1
Found '{' at 158, braceCount: 2
Found '{' at 428, braceCount: 3
Found '{' at 540, braceCount: 4
Found '}' at 606, braceCount: 3
Found '}' at 618, braceCount: 2
Found '}' at 630, braceCount: 1
Found '}' at 737, braceCount: 0
Brace matching ended: braceCount=0, i=738
Extracted set body: var t;
      var o = this;
      this._myData = e;
      if (e.spine) {
        this.mySkeleton = th...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._myData;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 81, initial braceCount: 1
Found '{' at 158, braceCount: 2
Found '{' at 430, braceCount: 3
Found '{' at 541, braceCount: 4
Found '}' at 607, braceCount: 3
Found '}' at 619, braceCount: 2
Found '}' at 631, braceCount: 1
Found '}' at 738, braceCount: 0
Brace matching ended: braceCount=0, i=739
Extracted set body: var t;
      var o = this;
      this._myData = e;
      if (e.spine) {
        this.mySkeleton = th...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 63, braceCount: 2
Found '}' at 106, braceCount: 1
Found '}' at 126, braceCount: 0
Brace matching ended: braceCount=0, i=127
Extracted get body: return $2Cfg.Cfg.BagSkill.filter({
        id: this.myData.startSkill
      })[0].bulletId;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Cfg.Cfg.BagSkill.filter({
        id: this.myData.startSkill
      })[0].bulletId;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 61, braceCount: 0
Brace matching ended: braceCount=0, i=62
Extracted get body: return this.game.canSkill;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.game.canSkill;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 47, braceCount: 0
Brace matching ended: braceCount=0, i=48
Extracted get body: return true;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return true;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 96, braceCount: 2
Found '}' at 130, braceCount: 1
Found '{' at 137, braceCount: 2
Found '}' at 173, braceCount: 1
Found '}' at 179, braceCount: 0
Brace matching ended: braceCount=0, i=180
Extracted get body: var e;
      if (null === (e = this.skillMgr) || undefined === e) {
        return undefined;
      ...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      if (null === (e = this.skillMgr) || undefined === e) {
        return undefined;
      } else {
        return e.skills[0];
      }
    },
    enumerable: fa
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 139, braceCount: 0
Brace matching ended: braceCount=0, i=140
Extracted get body: var e;
      return (null === (e = this.buffMgr) || undefined === e ? undefined : e.isBanMove) || fa...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      return (null === (e = this.buffMgr) || undefined === e ? undefined : e.isBanMove) || false;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 95, braceCount: 2
Found '}' at 129, braceCount: 1
Found '{' at 136, braceCount: 2
Found '}' at 175, braceCount: 1
Found '}' at 181, braceCount: 0
Brace matching ended: braceCount=0, i=182
Extracted get body: var e;
      if (null === (e = this.buffMgr) || undefined === e) {
        return undefined;
      }...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      if (null === (e = this.buffMgr) || undefined === e) {
        return undefined;
      } else {
        return e.isInvincible;
      }
    },
    enumerable: 
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 73, braceCount: 0
Brace matching ended: braceCount=0, i=74
Extracted get body: return this._stateMachine.curStateTag;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this._stateMachine.curStateTag;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 61, braceCount: 0
Brace matching ended: braceCount=0, i=62
Extracted get body: return this._stateMachine;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this._stateMachine;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._parent;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 81, initial braceCount: 1
Found '}' at 299, braceCount: 0
Brace matching ended: braceCount=0, i=300
Extracted set body: var t;
      var o;
      null === (o = null === (t = this._parent) || undefined === t ? undefined :...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 54, braceCount: 0
Brace matching ended: braceCount=0, i=55
Extracted get body: return this._curHp;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 80, initial braceCount: 1
Found '}' at 272, braceCount: 0
Brace matching ended: braceCount=0, i=273
Extracted set body: this._curHp = Math.min(this.property.cut.hp, e);
      this.curHpProgress = this._curHp / this.prope...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 57, braceCount: 0
Brace matching ended: braceCount=0, i=58
Extracted get body: return this._curArmor;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 83, initial braceCount: 1
Found '}' at 114, braceCount: 0
Brace matching ended: braceCount=0, i=115
Extracted set body: this._curArmor = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._horDir;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 81, initial braceCount: 1
Found '}' at 233, braceCount: 0
Brace matching ended: braceCount=0, i=234
Extracted set body: this._horDir = e;
      this.node && this.roleNode && (this.roleNode.scaleX = this._horDir * Math.ab...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 180, braceCount: 0
Brace matching ended: braceCount=0, i=181
Extracted get body: var e;
      return $2GameUtil.GameUtil.getDistance(this.node.position, null === (e = this._parent) ...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      return $2GameUtil.GameUtil.getDistance(this.node.position, null === (e = this._parent) || undefined === e ? undefined : e.position);
    },
    enumerable: f
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._myData;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 81, initial braceCount: 1
Found '{' at 274, braceCount: 2
Found '{' at 444, braceCount: 3
Found '{' at 553, braceCount: 4
Found '}' at 619, braceCount: 3
Found '}' at 631, braceCount: 2
Found '}' at 641, braceCount: 1
Found '}' at 647, braceCount: 0
Brace matching ended: braceCount=0, i=648
Extracted set body: var t;
      var o = this;
      this._myData = e;
      this.mySkeleton = this.node.getComponentInC...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 63, braceCount: 0
Brace matching ended: braceCount=0, i=64
Extracted get body: return this._openArgs.param;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this._openArgs.param;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 25, initial braceCount: 1
Found '}' at 69, braceCount: 0
Brace matching ended: braceCount=0, i=70
Extracted get body: return this.type == t.Slash;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
      get: function () {
        return this.type == t.Slash;
      },
      enumerable: false,
      configurable: true
    
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 73, braceCount: 0
Brace matching ended: braceCount=0, i=74
Extracted get body: return $2RBadgeModel.default.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2RBadgeModel.default.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 104, braceCount: 2
Found '}' at 220, braceCount: 1
Found '}' at 244, braceCount: 0
Brace matching ended: braceCount=0, i=245
Extracted get body: var e = 0;
      this.mode.getChildBadge(this.myKey).forEach(function (t) {
        var o = $2Notifi...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e = 0;
      this.mode.getChildBadge(this.myKey).forEach(function (t) {
        var o = $2Notifier.Notifier.call($2CallID.CallID.Badge_Get, t);
        o && !o.isTree
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 63, braceCount: 0
Brace matching ended: braceCount=0, i=64
Extracted get body: return this.game.gameCamera;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.game.gameCamera;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 55, braceCount: 0
Brace matching ended: braceCount=0, i=56
Extracted get body: return this._myData;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 81, initial braceCount: 1
Found '{' at 158, braceCount: 2
Found '{' at 428, braceCount: 3
Found '}' at 685, braceCount: 2
Found '}' at 697, braceCount: 1
Found '}' at 860, braceCount: 0
Brace matching ended: braceCount=0, i=861
Extracted set body: var t;
      var o = this;
      this._myData = e;
      if (e.spine) {
        this.mySkeleton = th...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 60, braceCount: 0
Brace matching ended: braceCount=0, i=61
Extracted get body: return this._isCanRelive;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 86, initial braceCount: 1
Found '}' at 120, braceCount: 0
Brace matching ended: braceCount=0, i=121
Extracted set body: this._isCanRelive = e;...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 54, braceCount: 0
Brace matching ended: braceCount=0, i=55
Extracted get body: return this._level;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 80, initial braceCount: 1
Found '{' at 359, braceCount: 2
Found '{' at 545, braceCount: 3
Found '}' at 583, braceCount: 2
Found '}' at 593, braceCount: 1
Found '}' at 599, braceCount: 0
Brace matching ended: braceCount=0, i=600
Extracted set body: var t;
      var o = this._level != e;
      this._level = e;
      this.levelCfg = $2Game.ModeCfg.L...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 135, braceCount: 0
Brace matching ended: braceCount=0, i=136
Extracted get body: return (this.levelExp - this.levelCfg.levelUpExp) / (this._nextLevelExp - this.levelCfg.levelUpExp);...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return (this.levelExp - this.levelCfg.levelUpExp) / (this._nextLevelExp - this.levelCfg.levelUpExp);
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 57, braceCount: 0
Brace matching ended: braceCount=0, i=58
Extracted get body: return this._levelExp;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 83, initial braceCount: 1
Found '}' at 284, braceCount: 0
Brace matching ended: braceCount=0, i=285
Extracted set body: this._levelExp = Math.ceil(e);
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_ExpUpdate, t...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 67, braceCount: 0
Brace matching ended: braceCount=0, i=68
Extracted get body: return $2Game.Game.Mgr.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.Mgr.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 101, braceCount: 0
Brace matching ended: braceCount=0, i=102
Extracted get body: return $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode);...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode);
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 67, braceCount: 0
Brace matching ended: braceCount=0, i=68
Extracted get body: return $2Game.Game.Mgr.instance;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.Mgr.instance;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 102, braceCount: 0
Brace matching ended: braceCount=0, i=103
Extracted get body: return $2Notifier.Notifier.call($2CallID.CallID.Fight_GetMainRole);...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Notifier.Notifier.call($2CallID.CallID.Fight_GetMainRole);
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 112, braceCount: 0
Brace matching ended: braceCount=0, i=113
Extracted get body: return this.node.getComByPath(cc.EditBox, "bg/allEditBox").string.split(",");...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this.node.getComByPath(cc.EditBox, "bg/allEditBox").string.split(",");
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 57, braceCount: 0
Brace matching ended: braceCount=0, i=58
Extracted get body: return this._steering;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return this._steering;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 298, braceCount: 0
Brace matching ended: braceCount=0, i=299
Extracted get body: var e;
      var t;
      this._scene || (this._scene = this.viewScene || (null === (e = this.baseVi...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      var t;
      this._scene || (this._scene = this.viewScene || (null === (e = this.baseView) || undefined === e ? undefined : e.eventScene) || (null === (t = t
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return this._isForFree;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=found
Starting brace matching from index 84, initial braceCount: 1
Found '}' at 397, braceCount: 0
Brace matching ended: braceCount=0, i=398
Extracted set body: this._isForFree = e;
      // $2Manager.Manager.loader.loadSpriteToSprit("img/ui/" + (e ? "icon_tltq...
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '{' at 89, braceCount: 2
Found '{' at 165, braceCount: 3
Found '}' at 192, braceCount: 2
Found '{' at 199, braceCount: 3
Found '}' at 247, braceCount: 2
Found '}' at 255, braceCount: 1
Found '}' at 277, braceCount: 0
Brace matching ended: braceCount=0, i=278
Extracted get body: var e;
      for (var t = this.node; !e && t && t.parent;) {
        var o = t.parent.getComponent($...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      for (var t = this.node; !e && t && t.parent;) {
        var o = t.parent.getComponent($2MVC.MVC.BaseView);
        if (o) {
          e = o;
        } else {
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 227, braceCount: 0
Brace matching ended: braceCount=0, i=228
Extracted get body: return $2Manager.Manager.vo.knapsackVo.has($2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out) +...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Manager.Manager.vo.knapsackVo.has($2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out) + $2Manager.Manager.vo.knapsackVo.has($2CurrencyConfigCfg.CurrencyConfi
Trying loose pattern /set\s*:\s*function/: not found
extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 146, braceCount: 0
Brace matching ended: braceCount=0, i=147
Extracted get body: var e;
      return (null === (e = this.game.bronMonsterMgr) || undefined === e ? undefined : e.batc...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      var e;
      return (null === (e = this.game.bronMonsterMgr) || undefined === e ? undefined : e.batchNum) || 1;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
