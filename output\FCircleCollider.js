/**
 * FCircleCollider
 * 组件类 - 从编译后的JS反编译生成
 */

const $2FCollider = require('FCollider');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2FCollider.default,

    properties: {
        type: {
            get() {
                return $2FCollider.ColliderType.Circle;,
            override: true
        396,
            visible: false
        },
        radius: {
            get() {
                return this._radius;
            },
            set(value) {
                this._radius = e < 0 ? 0 : e;
            },
            visible: false
        }
    },

    ctor: function () {
        this.worldPosition = cc.v2(0, 0)
        this.worldRadius = 0
        this._radius = 40
    },

    // use this for initialization
    onLoad: function () {
    },

    setSize: function (e) {
        this.radius = e.width / 2;
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
