var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Log = undefined;
var n = function () {
  function e() {
    this._log = cc;
    this._enableLog = false;
  }
  e.prototype.setLogEnable = function (e) {
    this._enableLog = e;
  };
  e.prototype.log = function () {
    var e;
    var t = [];
    for (var o = 0; o < arguments.length; o++) {
      t[o] = arguments[o];
    }
    this._enableLog && (e = this._log).log.apply(e, cc__spreadArrays(["[Log]:"], t));
  };
  e.prototype.debug = function () {
    var e;
    var t = [];
    for (var o = 0; o < arguments.length; o++) {
      t[o] = arguments[o];
    }
    this._enableLog && (e = this._log).debug.apply(e, cc__spreadArrays(["[Debug]:"], t));
  };
  e.prototype.warn = function () {
    var e;
    var t = [];
    for (var o = 0; o < arguments.length; o++) {
      t[o] = arguments[o];
    }
    this._enableLog && (e = this._log).warn.apply(e, cc__spreadArrays(["[Warn]:"], t));
  };
  e.prototype.error = function () {
    var e;
    var t = [];
    for (var o = 0; o < arguments.length; o++) {
      t[o] = arguments[o];
    }
    this._enableLog && (e = this._log).error.apply(e, cc__spreadArrays(["[Error]:"], t));
  };
  e.prototype.time = function () {
    var e;
    var t = [];
    for (var o = 0; o < arguments.length; o++) {
      t[o] = arguments[o];
    }
    this._enableLog && (e = this._log).time.apply(e, cc__spreadArrays(["[TimeStart]:"], t));
  };
  e.prototype.timeEnd = function () {
    var e;
    var t = [];
    for (var o = 0; o < arguments.length; o++) {
      t[o] = arguments[o];
    }
    this._enableLog && (e = this._log).time.apply(e, cc__spreadArrays(["[TimeEnd]:"], t));
  };
  return e;
}();
exports.Log = new n();