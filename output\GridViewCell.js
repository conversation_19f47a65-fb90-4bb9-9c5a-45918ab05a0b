/**
 * GridViewCell
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Notifier = require('Notifier');
const $2ListenID = require('ListenID');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
    },

    // use this for initialization
    onLoad: function () {
    },

    onInit: function () {
        // TODO: 实现方法逻辑
    },

    onRefresh: function () {
        // TODO: 实现方法逻辑
    },

    onEnable: function () {
        this.changeListener(true);
    },

    onDisable: function () {
        this.changeListener(false);
    },

    changeListener: function (e) {
        this.onRefreshState && $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Refresh_Item, this.onRefreshState, this);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
