Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MathSection = undefined;
var exp_MathSection = function () {
  function _ctor() {
    this.left = Number.NEGATIVE_INFINITY;
    this.right = Number.NEGATIVE_INFINITY;
  }
  _ctor.prototype.adjust = function () {
    var e = this.left;
    var t = this.right;
    if (e > t) {
      this.left = t;
      this.right = e;
    }
  };
  _ctor.prototype.isNullRange = function () {
    return this.left === Number.NEGATIVE_INFINITY && this.right === Number.NEGATIVE_INFINITY;
  };
  _ctor.prototype.length = function () {
    return this.right - this.left;
  };
  _ctor.prototype.and = function (t) {
    var o = new _ctor();
    if (this.isNullRange() || t.isNullRange()) {
      return o;
    } else {
      return this.adjust(), t.adjust(), this.left >= t.left && this.left <= t.right ? o.left = this.left : t.left >= this.left && t.left <= this.right && (o.left = t.left), this.right >= t.left && this.right <= t.right ? o.right = this.right : t.right >= this.left && t.right <= this.right && (o.right = t.right), o;
    }
  };
  _ctor.prototype.Invert = function (t) {
    if (this.isNullRange()) {
      return t;
    }
    var o = new _ctor();
    if (this.left === t.left && this.right === t.right) {
      return o;
    } else {
      return this.left === t.left ? (o.left = this.right + 1, o.right = t.right) : this.right === t.right && (o.left = t.left, o.right = this.left - 1), o;
    }
  };
  return _ctor;
}();
exports.MathSection = exp_MathSection;